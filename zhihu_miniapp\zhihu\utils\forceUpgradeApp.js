define("utils/forceUpgradeApp.js",(function(e,n,t,r,o,s,u,a,i,l,c,d,p,f,b,y,m,g,v,h,x,j,k,w,I,_,T,U,D,E,P,S,A,C,G,M,R,V,K,N){"use strict";function q(e,n){var t,r,o,s,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;u;)try{if(t=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return u.label++,{value:s[1],done:!1};case 5:u.label++,r=s[1],s=[0];continue;case 7:s=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){u=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){u.label=s[1];break}if(6===s[0]&&u.label<o[1]){u.label=o[1],o=s;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(s);break}o[2]&&u.ops.pop(),u.trys.pop();continue}s=n.call(e,u)}catch(e){s=[6,e],r=0}finally{t=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}}var z=function(e,n,t){return new o((function(r,s){var u=function(e){try{i(t.next(e))}catch(e){s(e)}},a=function(e){try{i(t.throw(e))}catch(e){s(e)}},i=function(e){return e.done?r(e.value):o.resolve(e.value).then(u,a)};i((t=t.apply(e,n)).next())}))};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==f?f:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="4f4b945b-284a-47b8-964d-b39bb3e8ba32",e._sentryDebugIdIdentifier="sentry-dbid-4f4b945b-284a-47b8-964d-b39bb3e8ba32")}catch(e){}}();var B=e("../common/vendor.js"),F=e("./track.js");e("../stores/modules/user/index.js"),e("../stores/modules/history/index.js"),e("../stores/modules/login/index.js"),e("../stores/modules/search-history/index.js"),e("../stores/modules/koc-ascription/index.js"),e("../stores/modules/purchaseInfo/index.js"),e("../stores/modules/clientInfo/index.js"),e("../stores/modules/systenInfo/index.js");var H=e("../stores/modules/globalVariable/index.js");e("../stores/modules/trackInfo/index.js"),e("../stores/modules/devDebug/index.js"),t.forceUpgradeApp=function(e){return z(t,[e],(function(e){var n,r,o,s,u,a,i,l;return q(this,(function(c){return n=e.pageName,r=void 0===n?"":n,o=e.ignoreKm,s=void 0!==o&&o,u=function(){var e=H.useGlobalVariable().kmResource;return(null==e?void 0:e.forceUpgrade)||{}}(),a=u.isEnabled,i=u.text,(a||s)&&(l=B.index.getUpdateManager()).onUpdateReady((function(){return z(t,null,(function(){return q(this,(function(e){return F.track({elementType:"Popup",eventType:"Show",moduleId:"upgrade_popup",message:"".concat(r,"更新提醒弹窗曝光")}),B.index.showModal({title:"更新提示",content:i||"新版本已经准备好，重启一下即可",success:function(e){e.confirm?(F.track({elementType:"Popup",eventType:"Click",moduleId:"upgrade_popup",message:"".concat(r,"更新提醒弹窗按钮点击"),extra:{button_text:"确定"}}),l.applyUpdate()):e.cancel&&F.track({elementType:"Popup",eventType:"Click",moduleId:"upgrade_popup",message:"".concat(r,"更新提醒弹窗按钮点击"),extra:{button_text:"取消"}})}}),[2]}))}))})),[2]}))}))}}));
//# sourceMappingURL=forceUpgradeApp.js.map