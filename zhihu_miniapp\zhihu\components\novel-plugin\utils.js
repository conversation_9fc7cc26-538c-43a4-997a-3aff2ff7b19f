define("components/novel-plugin/utils.js",(function(e,s,o,n,t,d,r,i,u,l,c,a,b,f,j,g,x,m,y,v,I,h,p,D,k,_,w,S,V,A,E,K,O,P,Q,T,U,W,q,z){"use strict";!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==f?f:{},s=(new Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="3b70f308-0d23-41b5-a28d-6bbce60d0cfd",e._sentryDebugIdIdentifier="sentry-dbid-3b70f308-0d23-41b5-a28d-6bbce60d0cfd")}catch(e){}}(),e("../../stores/modules/user/index.js"),e("../../stores/modules/history/index.js"),e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js");var B=e("../../stores/modules/koc-ascription/index.js");e("../../stores/modules/purchaseInfo/index.js"),e("../../stores/modules/clientInfo/index.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js"),e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js");var C=e("../../utils/utils.js");o.getWxViewUrlPath=function(){var e=B.useKocAscriptionStore().launchInfo;return"fakeurl://pages/novel/index?".concat(C.convertObjectToQueryString(e))}}));
//# sourceMappingURL=utils.js.map