define("hooks/usePayment.js",(function(e,t,n,r,a,s,o,u,i,c,l,d,p,y,f,m,_,v,h,g,b,S,I,k,w,x,T,P,O,q,j,A,U,M,N,C,D,B,R,E){"use strict";function L(e,t){var n,r,a,s,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return s={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function u(s){return function(u){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,r&&(a=2&s[0]?r.return:s[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,s[1])).done)return a;switch(r=0,a&&(s=[2&s[0],a.value]),s[0]){case 0:case 1:a=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,r=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(a=o.trys,(a=a.length>0&&a[a.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!a||s[1]>a[0]&&s[1]<a[3])){o.label=s[1];break}if(6===s[0]&&o.label<a[1]){o.label=a[1],a=s;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(s);break}a[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],r=0}finally{n=a=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}}var z=Object.defineProperty,J=Object.defineProperties,Y=Object.getOwnPropertyDescriptors,K=Object.getOwnPropertySymbols,$=Object.prototype.hasOwnProperty,F=Object.prototype.propertyIsEnumerable,G=function(e,t,n){return t in e?z(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},H=function(e,t){for(var n in t||(t={}))$.call(t,n)&&G(e,n,t[n]);var r=!0,a=!1,s=void 0;if(K)try{for(var o,u=K(t)[Symbol.iterator]();!(r=(o=u.next()).done);r=!0){n=o.value;F.call(t,n)&&G(e,n,t[n])}}catch(e){a=!0,s=e}finally{try{r||null==u.return||u.return()}finally{if(a)throw s}}return e},V=function(e,t){return J(e,Y(t))},W=function(e,t,n){return new a((function(r,s){var o=function(e){try{i(n.next(e))}catch(e){s(e)}},u=function(e){try{i(n.throw(e))}catch(e){s(e)}},i=function(e){return e.done?r(e.value):a.resolve(e.value).then(o,u)};i((n=n.apply(e,t)).next())}))};!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==y?y:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f886a765-ca8c-49ca-b437-2e59ed8334f4",e._sentryDebugIdIdentifier="sentry-dbid-f886a765-ca8c-49ca-b437-2e59ed8334f4")}catch(e){}}();var Q=e("../common/vendor.js"),X=e("../utils/showToast.js"),Z=e("../utils/utils.js"),ee=e("../utils/payUtils.js"),te=e("../components/pay-card/fetch.js"),ne=e("../utils/track.js"),re=e("../utils/constant.js");e("../stores/modules/user/index.js"),e("../stores/modules/history/index.js"),e("../stores/modules/login/index.js"),e("../stores/modules/search-history/index.js");var ae=e("../stores/modules/koc-ascription/index.js"),se=e("../stores/modules/purchaseInfo/index.js");e("../stores/modules/clientInfo/index.js");var oe=e("../stores/modules/systenInfo/index.js"),ue=e("../stores/modules/globalVariable/index.js");e("../stores/modules/trackInfo/index.js"),e("../stores/modules/devDebug/index.js");var ie=e("../utils/appFetch.js"),ce=e("../pages/manuscript/store.js"),le=function(e){return!!e&&-1!==e.indexOf("cancel")};n.usePayment=function(e,t,n){var r=this,a="",s=se.usePurchaseInfo(),o=ae.useKocAscriptionStore(),c=ce.useManuscriptStore(),l=oe.useSystemInfoStore();Q.storeToRefs(l);var d=Q.storeToRefs(s),p=d.needShowSubscribeConfirm,y=d.clickedSkuId;var f=Q.ref(ee.isIos()&&!0),m=Q.ref(null),_=Q.ref(0),v=Q.ref(null),h=Q.ref(!1),g={},b=ue.useGlobalVariable(),S=0,I=0,k=function(e){var t,n,r=e.status,a=void 0===r||r,s=e.errorMessage;(null==(n=null==(t=Q.index)?void 0:t.$performanceSDK)?void 0:n.reportCustomEvent)&&Q.index.$performanceSDK.reportCustomEvent("payment_panel_open",{userPayStayDuration:Date.now()-I,duration:I-S,status:a?"success":"fail",error_message:s})},w=function(){var t=e.mixPaymentChannelItemList;return t&&0!==t.length?{mix_payment_channel_item_list:t,mix_payment_channel:t.map((function(e){return null==e?void 0:e.payment_channel})).filter(Boolean)}:{}},x=function(n){t.value.type="dialog",t.value.isPayPopup=!0,ne.track({elementType:"Page",eventType:"Show",message:"支付文案蒙层曝光",extra:{pay_source:e.paySource}}),ne.track({elementType:"Block",eventType:"Show",moduleId:"vipmini_inquiry_popup",message:"支付流程-问询弹窗-模块曝光",extra:{request_res:n,pay_source:e.paySource}})};function T(){ne.track({elementType:"Card",eventType:"Unknown",moduleId:"order_status",message:"订单支付失败",extra:V(H({status:"fail",deal_id:e.deal_id,sku_id:e.sku_id},g),{pay_source:e.paySource})}),n&&b.setIsFromManuscriptPaySuccess(!1),x(),P(),Q.index.$emit(re.PAY_SUCCESS_CB,{paySuccess:!1})}var P=function(){v.value&&(i(Number(v.value)),v.value=null,_.value=0)},O=function(){t.value.isPayPopup=!1,P()},q=function(t){O(),ne.track({elementType:"Block",eventType:"Show",moduleId:"vipmini_close_cashier_counter_cancel_payment",message:"关闭收营台-取消支付-模块曝光",extra:{request_res:t,pay_source:e.paySource}})};function j(){ne.track({elementType:"Card",eventType:"Unknown",moduleId:"order_status",message:"订单支付成功",extra:V(H({status:"success",deal_id:e.deal_id,sku_id:e.sku_id},g),{pay_source:e.paySource,action:e.benefitType||e.channelKey})}),ne.track({elementType:"Block",eventType:"Show",moduleId:"vipmini_payment_success",message:"支付流程-支付成功-模块曝光",extra:{request_res:JSON.stringify(e)}}),O(),X.showToast({title:"购买成功"}),p.value&&"xima"!==e.benefitType&&b.setShowSubscribePopup(!0),n&&b.setIsFromManuscriptPaySuccess(!0),Q.index.$emit(re.PAY_SUCCESS_CB,{paySuccess:!0,benefitType:e.benefitType}),ie.trackBuyEvent(n?"manuscript":"buyMembers",c.lastSectionId)}function A(t,n){var r=this;try{ne.track({elementType:"Block",eventType:"Show",moduleId:"vipmini_payment_monitor_success_start_poll",message:"支付监听成功-开始轮询-模块曝光",extra:{request_res:JSON.stringify(e)}}),v.value=u((function(){return W(r,null,(function(){var r;return L(this,(function(a){switch(a.label){case 0:return[4,function(){return W(this,null,(function(){var t,n,r,a;return L(this,(function(s){switch(s.label){case 0:t={deal_id:e.deal_id},n=0,s.label=1;case 1:return s.trys.push([1,3,,4]),[4,te.getPayResultApi(t)];case 2:return r=s.sent(),n=r.producerIsReady,ne.track({elementType:"Page",eventType:"Unknown",moduleId:"pay_result_success",message:"支付轮询接口success",extra:{status:"success",payResult:r,pay_source:e.paySource}}),[3,4];case 3:return a=s.sent(),ne.track({elementType:"Page",eventType:"Unknown",moduleId:"pay_result_fail",message:"支付轮询接口返回fail",extra:{status:"fail",errorInfo:a,pay_source:e.paySource}}),[3,4];case 4:return[2,n]}}))}))}()];case 1:return(r=a.sent())||++_.value>=60?(s.changePaymentStatus(!1),ne.track({elementType:"Block",eventType:"Show",moduleId:r?"vipmini_poll_payment_result_interface_success":"vipmini_poll_payment_result_interface_fail",message:"支付流程-轮询支付结果-接口".concat(r?"success":"fail","-模块曝光"),extra:{polling_res:r,request_res:JSON.stringify(e)}}),r?t():n()):ne.track({elementType:"Block",eventType:"Show",moduleId:"vipmini_poll_payment_result_poll_timeout",message:"支付流程-轮询支付结果-轮询超时-模块曝光",extra:{polling_res:r,request_res:JSON.stringify(e)}}),[2]}}))}))}),1e3)}catch(e){Q.index.hideLoading()}}function U(t){ne.track({elementType:"Block",eventType:"Show",moduleId:"vipmini_prepayment_success_cashier_counter_exposed",message:"预支付成功-收营台曝光-模块曝光",extra:{request_res:t,pay_source:e.paySource}})}function M(t){k({status:!0}),4===(null==t?void 0:t.code)&&le(null==t?void 0:t.errMsg)||A(j,T),ne.track({elementType:"Page",eventType:"Unknown",moduleId:"request_pay_success",message:"支付回调状态成功code",extra:{requestPaymentRes:t,pay_source:e.paySource}})}function N(t){k({status:!1,errorMessage:null==t?void 0:t.errMsg}),ne.track({elementType:"Page",eventType:"Unknown",moduleId:"request_pay_fail",message:"支付失败",extra:{failPayInfo:t,pay_source:e.paySource}})}function C(t){"请求预支付失败"===t&&ne.track({elementType:"Block",eventType:"Show",moduleId:"vipmini_prepayment_fail",message:"预支付失败-模块曝光",extra:{payment_errno:"".concat(t,": 参数错误"),pay_source:e.paySource}}),k({status:!1,errorMessage:t})}var D=Q.reactive({provider:"WECHAT",provider_channel_type:"NORMAL"});function B(){var t="DOUYIN_COMMON_MAPP";return e.isRenew&&(t="DOUYIN_SUBSCRIPTION"),f.value&&(t="DOUYIN_MAPP_DIAMOND_APPLE"),t}var R=Z.debounce((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return W(r,null,(function(){var n,r;return L(this,(function(s){switch(s.label){case 0:if(S=Date.now(),h.value)return[2];n=!ee.isIos(),r={},s.label=1;case 1:return s.trys.push([1,3,4,5]),Q.index.showLoading({title:"加载中"}),h.value=!0,[4,function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return W(this,null,(function(){var n,r,s,u,i,c,l,d,p,y;return L(this,(function(f){switch(f.label){case 0:return e.type,"wx"===t?D.provider="WECHAT":"zfb"===t&&(D.provider="ALIPAY"),[4,Q.getTrackData()];case 1:n=f.sent(),r=null==n?void 0:n.oneId,s=Z.convertObjectToQueryString(H(H({},r?{tracing_one_id:r}:{}),o.launchInfo)),u={},i={page_params:JSON.stringify(H({},o.page_query)),order_source:JSON.stringify(H({},o.order_source)),search_source:JSON.stringify({search_query:o.search_query,share_query:o.share_query,search_timestamp:o.time_stamp,attributes:{koc:o.koc_query}}),growth_ref_url:s,scene:o.scene||""},u={sku_data:{sku_id:e.sku_id,quantity:1},payment:V(H({payment_channel:B()},w()),{coupon_number:e.couponId}),kind:"normal",is_anonymous:!0,extra:V(H({mobile_channel:"douyin",login_code:e.isRenew?a:""},i),{pay_source:e.paySource||"",mini_action_tag:e.benefitType||e.channelKey})},c=e.promoKey||"",u.promotion_items=[V(H({},c&&{key:c}),{type:"UNION_PROMOTION",strategy:c?"USE":"UNUSE"})],"wx"===t&&(u.sku_data.sku_id=null==e?void 0:e.wxSkuId),ne.track({elementType:"Page",eventType:"Unknown",moduleId:"get_order_info_request",message:"请求预下单接口参数",extra:{requestParams:u,pay_source:e.paySource}}),"function"==typeof e.beforeOrderInfo&&e.beforeOrderInfo(u),m.value=u,l={},f.label=2;case 2:return f.trys.push([2,4,,5]),[4,te.getOrderInfoApi(u)];case 3:return d=f.sent(),ne.track({elementType:"Page",eventType:"Unknown",moduleId:"get_order_info_request",message:"请求预下单接口返回",extra:{requestRes:d,pay_source:e.paySource}}),l=(null==d?void 0:d.paymentOutParams)||{},p=(d||{}).dealId,e.deal_id=p,[3,5];case 4:return y=f.sent(),ne.track({elementType:"Page",eventType:"Unknown",moduleId:"get_order_info_request",message:"请求预下单失败",extra:{errInfo:y,pay_source:e.paySource}}),ne.track({elementType:"Block",eventType:"Show",moduleId:"vipmini_prepayment_fail",message:"预支付失败-模块曝光",extra:{payment_errno:y,pay_source:e.paySource}}),[3,5];case 5:return[2,H({},l)]}}))}))}(t)];case 2:return r=s.sent(),g=r,[3,5];case 3:return s.sent(),[3,5];case 4:return h.value=!1,Q.index.hideLoading(),[7];case 5:return n?e.isRenew?function(e){tt.canIUse("createSignOrder")?(null==e?void 0:e.data)&&(null==e?void 0:e.byteAuthorization)?tt.createSignOrder({businessType:2,data:e.data,byteAuthorization:e.byteAuthorization,success:function(e){I=Date.now(),tt.sign({businessType:2,orderId:e.authOrderId,success:function(e){M(e)},fail:function(e){N(e)},complete:function(e){U(e),le(null==e?void 0:e.errMsg)?q(e):x(e)}})},fail:function(e){C("拉起收银台失败")}}):C("请求预支付失败"):Q.index.showModal({title:"提示",content:"当前客户端版本过低，无法使用该功能，请升级客户端或关闭后重启更新。"})}(r):function(e){tt.canIUse("requestOrder")?(null==e?void 0:e.data)&&(null==e?void 0:e.byteAuthorization)?tt.requestOrder({data:e.data,byteAuthorization:e.byteAuthorization,success:function(e){I=Date.now(),tt.getOrderPayment({orderId:null==e?void 0:e.orderId,success:function(e){M(e)},fail:function(e){N(e),4===e.errNo&&Q.index.$emit(re.PAY_CANCEL_CB,{payCancel:!0,requestRes:m.value})},complete:function(e){U(e),le(null==e?void 0:e.errMsg)?q(e):x(e)}})},fail:function(e){C("拉起收银台失败")}}):C("请求预支付失败"):Q.index.showModal({title:"提示",content:"当前客户端版本过低，无法使用该功能，请升级客户端或关闭后重启更新。"})}(r):function(t){tt.canIUse("getOrderPayment.object.imId")&&tt.canIUse("requestOrder")?(null==t?void 0:t.data)&&(null==t?void 0:t.byteAuthorization)?tt.requestOrder({data:t.data,byteAuthorization:t.byteAuthorization,success:function(t){I=Date.now(),tt.getOrderPayment({orderId:null==t?void 0:t.orderId,imId:"1110282023",immediate:!1,success:function(t){var n;n=t,A(j,T),ne.track({elementType:"Page",eventType:"Unknown",moduleId:"request_pay_success",message:"IOS支付回调状态成功",extra:{requestPaymentRes:n,pay_source:e.paySource}})},fail:function(e){N(e),e.errNo},complete:function(e){le(null==e?void 0:e.errMsg)?q(e):x(e)}})},fail:function(e){C("不支持iOS支付")}}):C("请求预支付失败"):Q.index.showModal({title:"提示",content:"当前客户端版本过低，无法使用该功能，请升级客户端或关闭后重启更新。"})}(r),[2]}}))}))}));return Q.onUnmounted((function(){P()})),{startPay:R,ttLoginAndStartPay:function(){Q.index.login({force:!0,success:function(t){return W(this,null,(function(){return L(this,(function(n){return(a=t.code?t.code:"")&&y.value===e.sku_id&&R(undefined),[2]}))}))},fail:function(e){return Q.index.hideLoading(),""}})},closePayPopup:O,showPayLoadingPopup:function(){P(),t.value.type="loading",t.value.isPayPopup=!0,A(j,T),ne.track({elementType:"Page",eventType:"Show",message:"支付轮询蒙层曝光",extra:{pay_source:e.paySource}})}}}}));
//# sourceMappingURL=usePayment.js.map