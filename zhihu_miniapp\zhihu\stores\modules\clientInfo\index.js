define("stores/modules/clientInfo/index.js",(function(e,t,i,n,d,s,r,u,o,a,b,c,l,f,I,p,g,m,y,C,h,v,_,z,D,N,j,E,S,k,w,x,A,B,F,K,L,O,T,U){"use strict";!function(){try{var e=void 0!==b?b:"undefined"!=typeof global?global:void 0!==f?f:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="bb737950-cb57-4505-979f-b89da10e4474",e._sentryDebugIdIdentifier="sentry-dbid-bb737950-cb57-4505-979f-b89da10e4474")}catch(e){}}();var Y=e("../../../utils/constant.js"),q=e("../../../utils/utils.js"),G=e("../../../common/vendor.js"),H={udid:"",bizClientId:"",appName:""},J=G.defineStore(Y.CLIENT_INFO_KEY,{state:function(){return H},getters:{},actions:{updateUdid:function(e){this.udid=e},updateAppName:function(e){this.appName=e},initBizClientId:function(){if(this.bizClientId)return this.bizClientId;var e=q.randomID();return this.bizClientId=e,e}},persist:{enabled:!0}});i.useClientStore=J}));
//# sourceMappingURL=index.js.map