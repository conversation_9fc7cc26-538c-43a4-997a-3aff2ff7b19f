define("utils/appInit.js",(function(e,n,t,s,o,r,i,a,u,c,l,d,f,p,h,v,I,b,m,y,g,S,_,T,k,w,A,j,x,P,O,C,D,z,R,L,V,N,E,M){"use strict";function U(e,n){var t,s,o,r,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(r){return function(a){return function(r){if(t)throw new TypeError("Generator is already executing.");for(;i;)try{if(t=1,s&&(o=2&r[0]?s.return:r[0]?s.throw||((o=s.return)&&o.call(s),0):s.next)&&!(o=o.call(s,r[1])).done)return o;switch(s=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return i.label++,{value:r[1],done:!1};case 5:i.label++,s=r[1],r=[0];continue;case 7:r=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==r[0]&&2!==r[0])){i=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){i.label=r[1];break}if(6===r[0]&&i.label<o[1]){i.label=o[1],o=r;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(r);break}o[2]&&i.ops.pop(),i.trys.pop();continue}r=n.call(e,i)}catch(e){r=[6,e],s=0}finally{t=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,a])}}}var H=function(e,n,t){return new o((function(s,r){var i=function(e){try{u(t.next(e))}catch(e){r(e)}},a=function(e){try{u(t.throw(e))}catch(e){r(e)}},u=function(e){return e.done?s(e.value):o.resolve(e.value).then(i,a)};u((t=t.apply(e,n)).next())}))};!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==p?p:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="e5a62c7d-bc80-486f-a217-098f89bea838",e._sentryDebugIdIdentifier="sentry-dbid-e5a62c7d-bc80-486f-a217-098f89bea838")}catch(e){}}();var Z=e("../common/vendor.js"),G=e("../stores/modules/user/index.js");e("../stores/modules/history/index.js");var q=e("../stores/modules/login/index.js");e("../stores/modules/search-history/index.js");var F=e("../stores/modules/koc-ascription/index.js");e("../stores/modules/purchaseInfo/index.js");var $=e("../stores/modules/clientInfo/index.js"),K=e("../stores/modules/systenInfo/index.js"),J=e("../stores/modules/globalVariable/index.js");e("../stores/modules/trackInfo/index.js");var Q=e("../stores/modules/devDebug/index.js"),B=e("./appFetch.js"),W=e("./onGetUdid.js"),X=e("../writeConstant.js"),Y=e("./utils.js"),ee=e("./launchOptionsHelper.js"),ne=function(e){var n=K.useSystemInfoStore().hostName,t=F.useKocAscriptionStore(),s=ee.getLaunchOptions(),o=s.path,r=s.scene,i=e||s.query;t.updateLaunchInfo(i),r&&t.updateAscriptionScene(r);var a=$.useClientStore(),u=a.bizClientId,c=a.udid,l=q.useLoginStore().accessToken,d="".concat(o,"?").concat(Y.convertObjectToQueryString(i));Z.init({baseURL:X.API_HOST,MINI_APPID:X.MINI_APPID,accessToken:l,udid:c,appName:n,isOpen:!0,path:d,bizClientId:u,productId:X.TRACK_PRODUCT_ID,isOnlyOne:!0}),Z.trackEvent({event_type:8,event_name:"活跃",event_value:{section_id:(null==i?void 0:i.id)||""}})},te=function(){var e=J.useGlobalVariable(),n=e.miniVersion,t=e.miniEnvType;if(!n||!t){var s,o,r,i=Z.index.getEnvInfoSync().microapp;return s=(null==i?void 0:i.mpVersion)||"",r="production"===(o=(null==i?void 0:i.envType)||""),e.setMiniVersion(s),e.setMiniEnvType(o),e.setIsRelease(r),s}return n};t.authorizedLogin=function(e){return H(t,null,(function(){var n,s;return U(this,(function(r){switch(r.label){case 0:return n=q.useLoginStore(),s=G.useUserStore(),[4,new o((function(o,r){return H(t,null,(function(){var t,i,a,u;return U(this,(function(c){switch(c.label){case 0:return c.trys.push([0,3,,4]),[4,B.phonenumAuthorizationLogin(e)];case 1:return t=c.sent()||{},(i=t.data)&&n.updateAccessToken(i),(null==i?void 0:i.accessToken)?(Z.setOneIdRequestConfig({accessToken:n.accessToken}),ne(),Z.trackEvent({event_type:10,event_name:"登录事件"}),Z.index.showToast({icon:"success",title:"登录成功"}),s.updateRenewInfo()):o(t),(null==i?void 0:i.hashId)&&Z.zaV3.config({memberHashId:i.hashId}),[4,B.getUserInfo()];case 2:return a=c.sent(),s.updateUserInfo(a),o(t),[3,4];case 3:return u=c.sent(),r(u),[3,4];case 4:return[2]}}))}))}))];case 1:return[2,r.sent()]}}))}))},t.getMiniEnvInfo=function(){return H(t,null,(function(){var e,n,t,s,o,r,i;return U(this,(function(a){switch(a.label){case 0:return e=K.useSystemInfoStore(),n=e.hostName,t=W.initUdidListener(),(s=$.useClientStore()).updateAppName(n),o=s.udid,r=o,o?[3,2]:[4,B.getUdid()];case 1:i=a.sent(),s.updateUdid(null==i?void 0:i.udid),r=null==i?void 0:i.udid,a.label=2;case 2:return Z.setTags({udid:r}),t(),[2]}}))}))},t.getMiniVersion=te,t.initOneId=ne,t.initSentry=function(e){var n=e.bizClientId,t=te(),s=q.useLoginStore();Z.init$1({dsn:"https://<EMAIL>/2845",tracesSampleRate:.3,ignoreErrors:[/createRequestTask:fail$/,/getPhoneNumber:fail$/,"addShortcut:fail"],normalizeDepth:10,sampleRate:.5,release:"141-26f461ef"}),Z.setTags({bizClientId:n,platform:X.MINI_APP_PLATFORM,miniVersion:t||"unknown",hashId:s.hashId||"unknown",urlToken:s.urlToken||"unknown"})},t.initZaV3Config=function(e){var n=q.useLoginStore(),t=$.useClientStore().bizClientId,s=K.useSystemInfoStore(),o=s.osName,r=s.osVersion,i=s.version,a=JSON.stringify(e),u=te(),c=Q.useDevDebugStore(),l=c.zapApiHost,d=c.zapApiPath,f=J.useGlobalVariable().isRelease;Z.zaV3.config({clientId:t,platform:X.ZA_PLATFORM,memberHashId:n.hashId||"unknown",baseExtraInfo:{os:o,os_version:r,mini_version:u||"unknown",app_version:i,app_id:X.MINI_APPID,launch_options:a},apiHost:l&&!f?l:X.ZA_APIHOST,apiPath:d&&!f?d:X.ZA_APIPATH,product:X.ZA_PRODUCT})}}));
//# sourceMappingURL=appInit.js.map