{"appId": "tt9d69ebd4025fd5ea01", "entryPagePath": "pages/home/<USER>", "isMicroApp": true, "pages": ["pages/home/<USER>", "pages/historyPage/historyPage", "pages/mine/mine", "pages/points/points", "pages/manuscript/manuscript", "pages/novel_plugin/index", "sub_pages/login/login", "sub_pages/requestErrorPage/requestErrorPage", "sub_pages/orderList/orderList", "sub_pages/renewManage/renewManage", "sub_pages/setting/setting", "sub_pages/recover/recover", "sub_pages/authErrorPage/authErrorPage", "sub_pages/search/search", "sub_pages/result/result", "sub_pages/protocol/protocol", "sub_pages/miniDebugger/miniDebugger", "sub_pages/buyMembers/buyMembers", "sub_pages/rankList/rankList", "sub_pages/points/exchange", "sub_pages/points/income"], "subPackages": [{"root": "sub_pages/", "pages": ["login/login", "requestErrorPage/requestErrorPage", "orderList/orderList", "renewManage/renewManage", "setting/setting", "recover/recover", "authErrorPage/authErrorPage", "search/search", "result/result", "protocol/protocol", "miniDebugger/miniDebugger", "buyMembers/buyMembers", "rankList/rankList", "points/exchange", "points/income"]}, {"root": "commonPackage/", "pages": [], "common": true}], "networkTimeout": {"request": 10000, "uploadFile": 60000, "connectSocket": 60000, "downloadFile": 60000}, "global": {"window": {"backgroundColor": "#F6F6F7", "navigationBarBackgroundColor": "#F6F6F7", "navigationBarTextStyle": "black", "navigationStyle": "default", "navigationBarTitleText": "知乎小说"}, "prelaunch": {"enable": 1}}, "tabBar": {"color": "#656565", "borderStyle": "white", "backgroundColor": "#FFFFFF", "list": [{"pagePath": "pages/home/<USER>", "iconPath": "static/tab-bar/bookmark.png", "text": "推荐", "selectedIconPath": "static/tab-bar/bookmark-active.png"}, {"pagePath": "pages/points/points", "iconPath": "static/tab-bar/points.png", "text": "积分", "selectedIconPath": "static/tab-bar/points-active.png"}, {"pagePath": "pages/historyPage/historyPage", "iconPath": "static/tab-bar/history.png", "text": "浏览历史", "selectedIconPath": "static/tab-bar/history-active.png"}, {"pagePath": "pages/mine/mine", "iconPath": "static/tab-bar/mine.png", "text": "我的", "selectedIconPath": "static/tab-bar/mine-active.png"}], "selectedColor": "#056DE8"}, "navigateToMiniProgramAppIdList": [], "permission": {}, "prefetches": {}, "preloadRule": {"pages/home/<USER>": {"network": "all", "packages": ["sub_pages/"]}}, "prefetchRules": {}, "page": {"pages/historyPage/historyPage": {"window": {"backgroundColor": "#f6f6f7", "navigationBarBackgroundColor": "#f6f6f7", "backgroundColorTop": "#f6f6f7", "backgroundColorBottom": "#f6f6f7", "navigationBarTitleText": "历史记录", "usingComponents": {"uni-swipe-action-item": "/uni_modules/uni-swipe-action/components/uni-swipe-action-item/uni-swipe-action-item", "view-log": "/components/view-log/view-log", "uni-swipe-action": "/uni_modules/uni-swipe-action/components/uni-swipe-action/uni-swipe-action", "empty": "/components/empty/empty", "book-tags": "/components/book-tags/book-tags"}, "embedType": "none"}}, "pages/home/<USER>": {"window": {"navigationBarTitleText": "知乎小说", "enablePullDownRefresh": true, "onReachBottomDistance": 150, "usingComponents": {"tabs": "/components/tabs/tabs", "view-log": "/components/view-log/view-log", "empty": "/components/empty/empty", "loading-more": "/components/loading-more/loading-more", "charge-dialog": "/components/novel-plugin/components/charge-dialog/charge-dialog", "full-screen": "/components/novel-plugin/components/full-screen/full-screen"}, "embedType": "none"}}, "pages/manuscript/manuscript": {"window": {"navigationBarTitleText": "", "navigationBarBackgroundColor": "#F9F9F9", "backgroundColor": "#F9F9F9", "componentPlaceholder": {"confirm-modal": "view", "content-retention-popup": "view", "lucky-wheel": "view", "reward": "view", "comment-list": "view", "member-expire-popup": "view", "desktop-shortcut": "view", "subscribe-confirm-popup": "view", "koc-guide-card": "view", "pay-retention-popup": "view"}, "usingComponents": {"empty": "/components/empty/empty", "article-content": "/components/article-content/article-content", "view-log": "/components/view-log/view-log", "lead-generation": "/components/lead-generation/lead-generation", "pay-part-read-page": "/components/pay-part-read-page/pay-part-read-page", "loading": "/components/loading/loading", "login-popup": "/components/login-popup/login-popup", "pay-popup": "/components/pay-card/components/pay-popup", "promotion-card": "/components/pay-card/components/promotion-card", "confirm-modal": "/commonPackage/components/confirm-modal/confirm-modal", "content-retention-popup": "/commonPackage/components/content-retention-popup/content-retention-popup", "reward": "/commonPackage/components/reward/reward", "comment-list": "/commonPackage/components/comment-list/comment-list", "member-expire-popup": "/commonPackage/components/member-expire-popup/member-expire-popup", "desktop-shortcut": "/commonPackage/components/desktop-shortcut/desktop-shortcut", "pay-retention-popup": "/commonPackage/components/pay-retention-popup/pay-retention-popup", "lucky-wheel": "/commonPackage/components/lucky-wheel/lucky-wheel"}, "embedType": "force", "coLayerComponents": ["canvas"]}, "prelaunch": {"enable": 1}}, "pages/mine/mine": {"window": {"navigationBarTitleText": "我的", "backgroundColor": "#FFFFFF", "disableScroll": true, "navigationBarBackgroundColor": "#FFFFFF", "componentPlaceholder": {"confirm-modal": "view"}, "usingComponents": {"login-popup": "/components/login-popup/login-popup", "confirm-modal": "/commonPackage/components/confirm-modal/confirm-modal"}, "embedType": "none"}}, "pages/novel_plugin/index": {"window": {"navigationBarTitleText": "", "navigationBarBackgroundColor": "#F9F9F9", "backgroundColor": "#F9F9F9", "componentPlaceholder": {"confirm-modal": "view", "content-retention-popup": "view", "lucky-wheel": "view", "reward": "view", "comment-list": "view", "member-expire-popup": "view", "desktop-shortcut": "view", "subscribe-confirm-popup": "view", "koc-guide-card": "view", "pay-retention-popup": "view"}, "usingComponents": {"empty": "/components/empty/empty", "article-content": "/components/article-content/article-content", "view-log": "/components/view-log/view-log", "lead-generation": "/components/lead-generation/lead-generation", "pay-part-read-page": "/components/pay-part-read-page/pay-part-read-page", "loading": "/components/loading/loading", "login-popup": "/components/login-popup/login-popup", "pay-popup": "/components/pay-card/components/pay-popup", "promotion-card": "/components/pay-card/components/promotion-card", "confirm-modal": "/commonPackage/components/confirm-modal/confirm-modal", "content-retention-popup": "/commonPackage/components/content-retention-popup/content-retention-popup", "reward": "/commonPackage/components/reward/reward", "comment-list": "/commonPackage/components/comment-list/comment-list", "member-expire-popup": "/commonPackage/components/member-expire-popup/member-expire-popup", "desktop-shortcut": "/commonPackage/components/desktop-shortcut/desktop-shortcut", "pay-retention-popup": "/commonPackage/components/pay-retention-popup/pay-retention-popup", "lucky-wheel": "/commonPackage/components/lucky-wheel/lucky-wheel"}, "embedType": "force", "coLayerComponents": ["canvas"]}, "prelaunch": {"enable": 1}}, "pages/points/points": {"window": {"navigationBarTitleText": "", "backgroundColor": "#FBE6D0", "navigationBarBackgroundColor": "#FE8934", "componentPlaceholder": {"exchange-section": "view", "pay-tips": "view"}, "usingComponents": {"exchange-section": "/commonPackage/components/exchange-section/exchange-section", "pay-tips": "/commonPackage/components/pay-tips/pay-tips"}, "embedType": "none"}}, "sub_pages/authErrorPage/authErrorPage": {"window": {"disableScroll": true, "usingComponents": {"empty": "/components/empty/empty"}, "embedType": "none"}}, "sub_pages/buyMembers/buyMembers": {"window": {"navigationBarTitleText": "", "navigationBarTextStyle": "black", "backgroundColor": "#F9F9F9", "navigationBarBackgroundColor": "#F9F9F9", "componentPlaceholder": {"confirm-modal": "view", "subscribe-confirm-popup": "view"}, "usingComponents": {"login-popup": "/components/login-popup/login-popup", "pay-card-switch": "/components/pay-card/pay-card-switch", "pay-card-tile": "/components/pay-card/pay-card-tile", "confirm-modal": "/commonPackage/components/confirm-modal/confirm-modal", "subscribe-confirm-popup": "/commonPackage/components/subscribe-confirm-popup/subscribe-confirm-popup"}, "embedType": "none"}}, "sub_pages/login/login": {"window": {"disableScroll": true, "usingComponents": {}, "embedType": "fallback", "coLayerComponents": ["input"]}}, "sub_pages/miniDebugger/miniDebugger": {"window": {"usingComponents": {"scan": "/sub_pages/miniDebugger/components/scan", "zae": "/sub_pages/miniDebugger/components/zae", "za-setting": "/sub_pages/miniDebugger/components/zaSetting"}, "embedType": "fallback", "coLayerComponents": ["input"]}}, "sub_pages/orderList/orderList": {"window": {"usingComponents": {"empty": "/components/empty/empty"}, "embedType": "none"}}, "sub_pages/points/exchange": {"window": {"navigationBarTitleText": "积分兑换", "navigationBarBackgroundColor": "#F9F9F9", "backgroundColor": "#F9F9F9", "componentPlaceholder": {"exchange-section": "view", "pay-tips": "view"}, "usingComponents": {"exchange-section": "/commonPackage/components/exchange-section/exchange-section", "pay-tips": "/commonPackage/components/pay-tips/pay-tips"}, "embedType": "none"}}, "sub_pages/points/income": {"window": {"navigationBarTitleText": "我的收益", "backgroundColor": "#F9F9F9", "navigationBarBackgroundColor": "#F9F9F9", "usingComponents": {"income-list": "/sub_pages/points/components/income-list"}, "embedType": "none"}}, "sub_pages/protocol/protocol": {"window": {"usingComponents": {}, "embedType": "none"}}, "sub_pages/rankList/rankList": {"window": {"navigationBarTitleText": "榜单", "enablePullDownRefresh": true, "onReachBottomDistance": 150, "componentPlaceholder": {"annual-pass-coupon-popup": "view"}, "usingComponents": {"view-log": "/components/view-log/view-log", "empty": "/components/empty/empty", "annual-pass-coupon-popup": "/commonPackage/components/annual-pass-coupon-popup/annual-pass-coupon-popup", "loading-more": "/components/loading-more/loading-more"}, "embedType": "none"}}, "sub_pages/recover/recover": {"window": {"navigationBarTitleText": "会员未到账找回", "backgroundColor": "#FFFFFF", "navigationBarBackgroundColor": "#FFFFFF", "usingComponents": {}, "embedType": "none"}}, "sub_pages/renewManage/renewManage": {"window": {"navigationBarTitleText": "订阅管理", "backgroundColor": "#FFFFFF", "navigationBarBackgroundColor": "#FFFFFF", "disableScroll": true, "usingComponents": {"uni-popup": "/uni_modules/uni-popup/components/uni-popup/uni-popup"}, "embedType": "none"}}, "sub_pages/requestErrorPage/requestErrorPage": {"window": {"usingComponents": {"empty": "/components/empty/empty"}, "embedType": "none"}}, "sub_pages/result/result": {"window": {"navigationBarTitleText": "搜索", "onReachBottomDistance": 150, "componentPlaceholder": {"koc-guide-card": "view"}, "usingComponents": {"common-cards": "/components/common-cards/common-cards", "view-log": "/components/view-log/view-log", "empty": "/components/empty/empty", "loading-more": "/components/loading-more/loading-more", "koc-card": "/sub_pages/result/components/koc-card", "koc-guide-card": "/commonPackage/components/koc-guide-card/koc-guide-card"}, "embedType": "fallback", "coLayerComponents": ["input"]}}, "sub_pages/search/search": {"window": {"usingComponents": {}, "embedType": "fallback", "coLayerComponents": ["input"]}}, "sub_pages/setting/setting": {"window": {"navigationBarTitleText": "设置", "backgroundColor": "#FFFFFF", "disableScroll": true, "navigationBarBackgroundColor": "#FFFFFF", "usingComponents": {"login-popup": "/components/login-popup/login-popup"}, "embedType": "none"}}}, "ttPlugins": {}, "extAlias": {}, "npmAlias": {}, "pluginPages": [], "fallbackPluginPages": {}, "debug": false, "ttLaunchApp": {}, "widgets": [], "ext": {}, "extAppid": "", "customClose": false, "usingProvide": false, "redirection": {}, "component2": false, "industrySDKPages": [], "referrerPolicy": {}}