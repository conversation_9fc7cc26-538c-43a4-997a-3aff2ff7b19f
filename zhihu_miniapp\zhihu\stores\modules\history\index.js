define("stores/modules/history/index.js",(function(t,e,r,i,n,o,s,a,d,c,u,l,f,y,h,b,p,v,g,I,k,L,m,j,O,w,D,F,S,H,_,C,P,T,x,E,U,q,z,A){"use strict";var B=Object.defineProperty,G=Object.getOwnPropertySymbols,J=Object.prototype.hasOwnProperty,K=Object.prototype.propertyIsEnumerable,M=function(t,e,r){return e in t?B(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r},N=function(t,e){for(var r in e||(e={}))J.call(e,r)&&M(t,r,e[r]);var i=!0,n=!1,o=void 0;if(G)try{for(var s,a=G(e)[Symbol.iterator]();!(i=(s=a.next()).done);i=!0){r=s.value;K.call(e,r)&&M(t,r,e[r])}}catch(t){n=!0,o=t}finally{try{i||null==a.return||a.return()}finally{if(n)throw o}}return t};!function(){try{var t=void 0!==u?u:"undefined"!=typeof global?global:void 0!==y?y:{},e=(new Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="aed80ca6-e9c9-4903-a358-f01eb51de146",t._sentryDebugIdIdentifier="sentry-dbid-aed80ca6-e9c9-4903-a358-f01eb51de146")}catch(t){}}();var Q=t("../../../common/vendor.js"),R={historyList:[],unreadTipClicked:!1,desktopFlagInfo:{addFlag:!1,isShrink:!1}},V=Q.defineStore("history",{state:function(){return R},getters:{},actions:{addHistoryList:function(t){var e=[t].concat(this.historyList.filter((function(e){return e.id!==t.id})));this.historyList=e.slice(0,100)},removeHistoryList:function(t){var e=this.historyList.filter((function(e){return e.id!==t.id}));this.historyList=e},setUnreadTipClick:function(t){this.unreadTipClicked=!!t},updateHistoryList:function(t){this.historyList=t},updateDesktopFlagInfo:function(t){var e=N(N({},this.desktopFlagInfo),t);this.desktopFlagInfo=e}},persist:{enabled:!0}});r.useHistoryStore=V}));
//# sourceMappingURL=index.js.map