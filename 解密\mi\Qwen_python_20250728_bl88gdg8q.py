import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import binascii
import base64

# --- 从 jie.txt 提取的固定密钥和 IV ---
# 注意：这些是硬编码在 jie.txt 中的值，用于解密 jia.txt
aeskey_hex = "3e6cebec56e4bd95e8ae6ed4bf03c52e"
aesiv_hex = "54dee60aac15d992a4293886d5119b0c"

def aes_decrypt_with_fixed_key(encrypted_data_b64, key_hex, iv_hex):
    """
    使用固定的 AES 密钥和 IV 解密 Base64 编码的数据。
    算法: AES/CBC/PKCS7Padding
    """
    try:
        # 1. 解码 Base64
        encrypted_bytes = base64.b64decode(encrypted_data_b64)
        print(f"[DEBUG] Base64 decoded data length: {len(encrypted_bytes)} bytes")

        # 2. 转换十六进制密钥和 IV 为字节
        key_bytes = binascii.unhexlify(key_hex)
        iv_bytes = binascii.unhexlify(iv_hex)
        print(f"[DEBUG] Key length: {len(key_bytes)} bytes")
        print(f"[DEBUG] IV length: {len(iv_bytes)} bytes")

        # 3. 创建 AES 解密器 (CBC 模式)
        cipher = AES.new(key_bytes, AES.MODE_CBC, iv_bytes)

        # 4. 解密
        decrypted_bytes = cipher.decrypt(encrypted_bytes)
        print(f"[DEBUG] Raw decrypted data length: {len(decrypted_bytes)} bytes")

        # 5. 移除 PKCS7 填充
        # unpad 函数会自动处理填充
        unpadded_bytes = unpad(decrypted_bytes, AES.block_size, style='pkcs7')
        print(f"[DEBUG] Unpadded data length: {len(unpadded_bytes)} bytes")

        # 6. 转换为字符串
        plaintext = unpadded_bytes.decode('utf-8')
        return plaintext

    except (ValueError, binascii.Error) as e:
        # ValueError 可能来自 unpad (Incorrect padding) 或 AES.new (Invalid key/IV length)
        # binascii.Error 可能来自 unhexlify (Non-hexadecimal digit)
        print(f"[ERROR] Decryption failed (likely incorrect key/IV or padding): {e}")
        return None
    except UnicodeDecodeError as e:
        print(f"[ERROR] Failed to decode decrypted bytes to UTF-8 string: {e}")
        # 可以选择返回原始字节，或者 None
        # return decrypted_bytes # 如果需要查看原始字节
        return None
    except Exception as e:
        print(f"[ERROR] An unexpected error occurred during decryption: {e}")
        return None

def main():
    """主函数"""
    # 1. 读取 jia.txt 文件
    try:
        with open('jia.txt', 'r', encoding='utf-8') as f:
            jia_content = f.read()
        print("[INFO] Successfully read 'jia.txt'")
    except FileNotFoundError:
        print("[ERROR] File 'jia.txt' not found. Please check the file path.")
        return
    except Exception as e:
        print(f"[ERROR] An error occurred while reading 'jia.txt': {e}")
        return

    # 2. 解析 jia.txt 内容为 JSON
    try:
        jia_data = json.loads(jia_content)
        print("[INFO] Successfully parsed 'jia.txt' as JSON")
    except json.JSONDecodeError as e:
        print(f"[ERROR] Failed to parse 'jia.txt' as JSON: {e}")
        return

    # 3. 提取加密数据
    encrypted_data_b64 = jia_data.get("data")
    if not encrypted_data_b64:
        print("[ERROR] Could not find 'data' field in 'jia.txt' or it is empty.")
        return
    print("[INFO] Extracted encrypted data from 'data' field.")

    # 4. 调用解密函数
    print("[INFO] Starting decryption with fixed key and IV from 'jie.txt'...")
    decrypted_text = aes_decrypt_with_fixed_key(encrypted_data_b64, aeskey_hex, aesiv_hex)

    # 5. 输出结果
    if decrypted_text:
        print("\n[SUCCESS] Decryption successful!")
        print("-" * 30)
        print(decrypted_text)
        print("-" * 30)

        # 6. (可选) 保存解密后的内容到文件
        try:
            with open('jia_decrypted.txt', 'w', encoding='utf-8') as f:
                f.write(decrypted_text)
            print("\n[INFO] Decrypted content saved to 'jia_decrypted.txt'")
        except Exception as e:
            print(f"\n[WARNING] Failed to save decrypted content to file: {e}")
    else:
        print("\n[FAILURE] Decryption failed.")

if __name__ == "__main__":
    main()