define("utils/jumpToProtocolPage.js",(function(e,t,o,a,n,r,d,c,s,l,u,i,g,p,y,m,f,b,v,_,T,I,j,P,k,x,D,h,w,C,E,N,q,z,A,B,F,G,H,J){"use strict";!function(){try{var e=void 0!==u?u:"undefined"!=typeof global?global:void 0!==p?p:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="943de74f-2a34-4a82-944d-309a9a4ae528",e._sentryDebugIdIdentifier="sentry-dbid-943de74f-2a34-4a82-944d-309a9a4ae528")}catch(e){}}();var K=e("./track.js"),L=e("../common/vendor.js"),M={1:"会员服务协议",2:"知乎支付协议",3:"隐私保护协议",4:"知乎协议",5:"知乎盐选会员服务协议",6:"连续订阅服务协议"};o.jumpToProtocolPage=function(e){K.track({elementText:M[e],elementType:"Text",eventType:"Click",moduleId:"pay_card_agreement",message:"会员购买卡片协议点击",extra:{protocol_text:e,docname:M[e]}}),L.NovelPlugin.navigateTo({url:"/sub_pages/protocol/protocol?type=".concat(e)})}}));
//# sourceMappingURL=jumpToProtocolPage.js.map