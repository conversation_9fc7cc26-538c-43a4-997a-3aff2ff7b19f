define("utils/onGetUdid.js",(function(n,e,d,t,i,o,f,r,c,u,b,s,a,y,g,v,U,l,G,I,D,_,w,$,h,j,m,L,k,p,E,q,x,z,A,B,C,F,H,J){"use strict";!function(){try{var n=void 0!==b?b:"undefined"!=typeof global?global:void 0!==y?y:{},e=(new Error).stack;e&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[e]="1cc71eaf-f9bd-4745-81eb-de5f326b8bf7",n._sentryDebugIdIdentifier="sentry-dbid-1cc71eaf-f9bd-4745-81eb-de5f326b8bf7")}catch(n){}}();var K=n("../common/vendor.js"),M={$onGetUdid:new i((function(){}))};d.initUdidListener=function(){var n;return M.$onGetUdid=new i((function(e){n=e})),n},d.onGetUdid=function(n){K.onLoad((function(e){M.$onGetUdid.then((function(){n(e)}))}))}}));
//# sourceMappingURL=onGetUdid.js.map