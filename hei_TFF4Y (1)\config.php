<?php
/**
 * Configuration for the proxy server
 */
return [
    // API configuration
    'api' => [
        'base_url' => 'https://biz.zhangwenpindu.cn',
        'session_id' => '528824149f99afbbd425f792740211fb', // 您的 session ID
        'secret' => 'q62y7oqdQk90ez2rJfgkPbqtjy4VUhNU', // Secret for signing chapter requests
    ],
    
    // Cache configuration
    'cache' => [
        'enabled' => true,
        'ttl' => 3600, // Cache TTL in seconds (1 hour)
        'directory' => 'cache',
    ],
    
    // Headers
    'headers' => [
        'User-Agent' => 'Mozilla/5.0 (Linux; Android 13; 2210132C Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.156 Mobile Safari/537.36 aweme/24.2.0 ToutiaoMicroApp/2.81.1 PluginVersion/24209009',
        'app-name' => '3',
        'client-platform' => '5',
        'app-version' => '81218893',
        'os-type' => '3',
        'package-time' => '1741852720676',
    ],
    
    // Login configuration
    'login' => [
        'url' => 'https://ma5-normal-lf.zijieapi.com/api/apps/v3/login',
        'params' => [
            'appid' => 'tta977dfca3648b5f501',
            'aid' => '1128',
        ],
        'auth_url' => 'https://biz.zhangwenpindu.cn/user/third/auth/byteDance/miniApp',
        'headers' => [
            'User-Agent' => 'com.ss.android.ugc.aweme/240201 (Linux; U; Android 13; zh_CN; 2210132C; Build/TKQ1.221114.001;tt-ok/3.12.13.1)',
        ],
        'auth_headers' => [
            'User-Agent' => 'Mozilla/5.0 (Linux; Android 13; 2210132C Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.156 Mobile Safari/537.36 aweme/24.2.0 ToutiaoMicroApp/2.81.1 PluginVersion/24209009',
            'Referer' => 'https://tmaservice.developer.toutiao.com/?appid=tta977dfca3648b5f501&version=1.3.39',
            'Content-Type' => 'application/x-www-form-urlencoded',
            'app-name' => '3',
            'client-platform' => '5',
        ],
        'token_cache_ttl' => 86400, // 24 hours
    ],
    
    // Debug configuration
    'debug' => [
        'enabled' => true, // 启用调试模式
        'log_file' => 'debug.log',
    ],
];
?> 