<?php
// Include the cache class and logger
require_once 'cache.php';
require_once 'logger.php';

// Load configuration
$config = require_once 'config.php';

// Initialize cache and logger
$cache = new Cache($config['cache']['directory'], $config['cache']['ttl']);
$logger = new Logger('access.log', true);

// Handle actions
$message = '';
if (isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'clear_cache':
            $cache->clear();
            $message = '缓存已清除';
            break;
            
        case 'clear_logs':
            $logger->clear();
            $message = '日志已清除';
            break;
            
        case 'update_session':
            if (isset($_POST['session_id']) && !empty($_POST['session_id'])) {
                // Update the session ID in the config file
                $configContent = file_get_contents('config.php');
                $newConfigContent = preg_replace(
                    "/'session_id' => '([^']+)'/",
                    "'session_id' => '" . $_POST['session_id'] . "'",
                    $configContent
                );
                
                if (file_put_contents('config.php', $newConfigContent)) {
                    $message = 'Session ID 已更新';
                    
                    // Clear the auth token cache
                    $cacheKey = 'auth_token_' . md5($_POST['session_id']);
                    $cache->delete($cacheKey);
                } else {
                    $message = '无法更新配置文件';
                }
            }
            break;
    }
}

// Get cache stats
$cacheFiles = glob($config['cache']['directory'] . '/*.cache');
$cacheCount = count($cacheFiles);
$cacheSize = 0;

foreach ($cacheFiles as $file) {
    $cacheSize += filesize($file);
}

// Format cache size
if ($cacheSize > 1024 * 1024) {
    $cacheSizeFormatted = round($cacheSize / (1024 * 1024), 2) . ' MB';
} elseif ($cacheSize > 1024) {
    $cacheSizeFormatted = round($cacheSize / 1024, 2) . ' KB';
} else {
    $cacheSizeFormatted = $cacheSize . ' bytes';
}

// Get current session ID
$currentSessionId = $config['api']['session_id'];

// Check if the auth token is valid
$authToken = '';
$tokenValid = false;

$headers = [];
foreach ($config['login']['headers'] as $key => $value) {
    $headers[] = $key . ': ' . $value;
}
$headers[] = 'cookie: sessionid=' . $currentSessionId;

$loginParams = http_build_query($config['login']['params']);
$loginUrl = $config['login']['url'] . '?' . $loginParams;

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $loginUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

$response = curl_exec($ch);
curl_close($ch);

$responseData = json_decode($response, true);
$tokenValid = isset($responseData['data']['code']);

// Get recent requests
$recentRequests = $logger->getRecentLogs(20);

// Get API usage stats
$apiStats = [];
if (file_exists('access.log')) {
    $logs = file('access.log');
    foreach ($logs as $log) {
        $parts = explode(' | ', trim($log));
        if (count($parts) >= 3) {
            $path = $parts[2];
            $endpoint = strtok($path, '?');
            
            if (!isset($apiStats[$endpoint])) {
                $apiStats[$endpoint] = 0;
            }
            
            $apiStats[$endpoint]++;
        }
    }
    
    // Sort by usage count (descending)
    arsort($apiStats);
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>黑岩故事会 API 代理 - 管理面板</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            color: #3498db;
            margin-top: 30px;
        }
        .card {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .stats {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            flex: 1;
            min-width: 200px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #2980b9;
        }
        .message {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 14px;
        }
        .status-valid {
            background-color: #d4edda;
            color: #155724;
        }
        .status-invalid {
            background-color: #f8d7da;
            color: #721c24;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table th, table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        table th {
            background-color: #f8f9fa;
        }
        .button-group {
            display: flex;
            gap: 10px;
        }
        .chart {
            height: 300px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>黑岩故事会 API 代理 - 管理面板</h1>
    
    <?php if (!empty($message)): ?>
    <div class="message"><?php echo $message; ?></div>
    <?php endif; ?>
    
    <div class="stats">
        <div class="stat-card">
            <h3>缓存文件数</h3>
            <div class="stat-value"><?php echo $cacheCount; ?></div>
        </div>
        
        <div class="stat-card">
            <h3>缓存大小</h3>
            <div class="stat-value"><?php echo $cacheSizeFormatted; ?></div>
        </div>
        
        <div class="stat-card">
            <h3>Session 状态</h3>
            <div class="stat-value">
                <?php if ($tokenValid): ?>
                <span class="status status-valid">有效</span>
                <?php else: ?>
                <span class="status status-invalid">无效</span>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="card">
        <h2>系统管理</h2>
        <div class="button-group">
            <form method="post">
                <input type="hidden" name="action" value="clear_cache">
                <button type="submit">清除缓存</button>
            </form>
            
            <form method="post">
                <input type="hidden" name="action" value="clear_logs">
                <button type="submit">清除日志</button>
            </form>
        </div>
    </div>
    
    <div class="card">
        <h2>Session ID 管理</h2>
        <form method="post">
            <input type="hidden" name="action" value="update_session">
            <div class="form-group">
                <label for="session_id">当前 Session ID:</label>
                <input type="text" id="session_id" name="session_id" value="<?php echo htmlspecialchars($currentSessionId); ?>" required>
            </div>
            <button type="submit">更新 Session ID</button>
        </form>
    </div>
    
    <div class="card">
        <h2>API 使用统计</h2>
        <table>
            <thead>
                <tr>
                    <th>端点</th>
                    <th>请求次数</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($apiStats as $endpoint => $count): ?>
                <tr>
                    <td><?php echo htmlspecialchars($endpoint); ?></td>
                    <td><?php echo $count; ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <div class="card">
        <h2>最近请求</h2>
        <table>
            <thead>
                <tr>
                    <th>时间</th>
                    <th>IP</th>
                    <th>请求</th>
                    <th>状态</th>
                    <th>耗时</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($recentRequests as $request): ?>
                <?php
                    $parts = explode(' | ', trim($request));
                    if (count($parts) >= 5):
                ?>
                <tr>
                    <td><?php echo htmlspecialchars($parts[0]); ?></td>
                    <td><?php echo htmlspecialchars($parts[1]); ?></td>
                    <td><?php echo htmlspecialchars($parts[2]); ?></td>
                    <td><?php echo htmlspecialchars($parts[3]); ?></td>
                    <td><?php echo htmlspecialchars($parts[4]); ?></td>
                </tr>
                <?php endif; ?>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <p><a href="index.php">返回首页</a></p>
</body>
</html> 