define("stores/modules/login/index.js",(function(e,n,i,s,o,t,r,d,a,c,u,h,g,f,l,I,T,k,b,v,_,E,L,p,C,P,m,R,x,y,A,O,K,N,w,D,F,S,G,H){"use strict";function Y(e,n,i){return n in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i,e}var j;!function(){try{var e=void 0!==u?u:"undefined"!=typeof global?global:void 0!==f?f:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="0dd2b9c1-a2f9-4c48-b467-6f0de4542d71",e._sentryDebugIdIdentifier="sentry-dbid-0dd2b9c1-a2f9-4c48-b467-6f0de4542d71")}catch(e){}}();var q=e("../../../common/vendor.js"),X=e("../../../utils/constant.js"),z=(Y(j={},X.LOGIN_CACHE_TOKEN_KEY,""),Y(j,X.LOGIN_CACHE_EXPIRE_KEY,0),Y(j,"clearLoginStoreFlag",!1),Y(j,"accessToken",""),Y(j,"expiredIn",0),Y(j,"uid",""),Y(j,"hashId",""),Y(j,"urlToken",""),Y(j,"refreshToken",""),Y(j,"isRefreshPending",!1),Y(j,"expiresTime",0),Y(j,"isLoginRequestPending",!1),Y(j,"isFromLoginPage",!1),j),B=q.defineStore(X.LOGIN_CACHE_KEY,{state:function(){return z},getters:{},actions:{updateAccessToken:function(e){var n=e||{},i=n.accessToken,s=void 0===i?"":i,o=n.expiredIn,t=void 0===o?0:o,r=n.uid,d=void 0===r?"":r,a=n.hashId,c=void 0===a?"":a,u=n.urlToken,h=void 0===u?"":u,g=n.refreshToken,f=void 0===g?"":g;this[X.LOGIN_CACHE_TOKEN_KEY]=s?"bearer ".concat(s):"",this.expiredIn=t,this[X.LOGIN_CACHE_EXPIRE_KEY]=t?Date.now()+1e3*t:0,this.uid=d,this.hashId=c,this.urlToken=h,this.refreshToken=f,q.setTags({hashId:c||"unknown",urlToken:h||"unknown"})},clearAccessToken:function(){this.updateAccessToken()},setLoginRequestPending:function(e){this.isLoginRequestPending=e},setIsFromLoginPage:function(e){this.isFromLoginPage=!!e},setClearLoginStoreFlag:function(){this.clearLoginStoreFlag=!0},changeRefreshStatus:function(e){this.isRefreshPending=!!e},updateRefreshToken:function(e){var n=e||{},i=n.refreshToken,s=void 0===i?"":i,o=n.expiresIn,t=void 0===o?0:o,r=n.accessToken,d=void 0===r?"":r;this.accessToken=d?"bearer ".concat(d):"",this.expiredIn=t,this.refreshToken=s,this.expiresTime=t?Date.now()+1e3*t:0}},persist:{enabled:!0}});i.useLoginStore=B}));
//# sourceMappingURL=index.js.map