define("pages/mine/fetch.js",(function(e,n,t,r,a,o,i,u,l,c,f,s,b,d,y,p,h,g,v,w,_,k,x,I,D,m,j,E,S,q,B,C,G,T,z,A,F,H,J,K){"use strict";function L(e,n){var t,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function u(o){return function(u){return function(o){if(t)throw new TypeError("Generator is already executing.");for(;i;)try{if(t=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=n.call(e,i)}catch(e){o=[6,e],r=0}finally{t=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,u])}}}!function(){try{var e=void 0!==f?f:"undefined"!=typeof global?global:void 0!==d?d:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="8a76907b-3fba-4271-9f84-88095a496edd",e._sentryDebugIdIdentifier="sentry-dbid-8a76907b-3fba-4271-9f84-88095a496edd")}catch(e){}}();var M=e("../../utils/request.js");t.getBenefitConfig=function(){return e=t,n=function(){return L(this,(function(e){switch(e.label){case 0:return[4,M.api.get("/dalaran/april_fools_day/benefit_config")];case 1:return[2,e.sent()]}}))},new a((function(t,r){var o=function(e){try{u(n.next(e))}catch(e){r(e)}},i=function(e){try{u(n.throw(e))}catch(e){r(e)}},u=function(e){return e.done?t(e.value):a.resolve(e.value).then(o,i)};u((n=n.apply(e,null)).next())}));var e,n}}));
//# sourceMappingURL=fetch.js.map