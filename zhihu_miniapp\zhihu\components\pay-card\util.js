define("components/pay-card/util.js",(function(s,e,o,d,n,r,t,i,a,u,c,f,l,j,y,m,b,x,I,v,g,h,D,p,_,k,S,w,E,N,T,V,q,z,A,B,C,F,G,H){"use strict";!function(){try{var s=void 0!==c?c:"undefined"!=typeof global?global:void 0!==j?j:{},e=(new Error).stack;e&&(s._sentryDebugIds=s._sentryDebugIds||{},s._sentryDebugIds[e]="8a447d3c-8bd9-4355-accd-b9aa2f260cf2",s._sentryDebugIdIdentifier="sentry-dbid-8a447d3c-8bd9-4355-accd-b9aa2f260cf2")}catch(s){}}();var J=s("../../common/vendor.js");s("../../stores/modules/user/index.js"),s("../../stores/modules/history/index.js"),s("../../stores/modules/login/index.js"),s("../../stores/modules/search-history/index.js"),s("../../stores/modules/koc-ascription/index.js"),s("../../stores/modules/purchaseInfo/index.js"),s("../../stores/modules/clientInfo/index.js");var K=s("../../stores/modules/systenInfo/index.js");s("../../stores/modules/globalVariable/index.js"),s("../../stores/modules/trackInfo/index.js"),s("../../stores/modules/devDebug/index.js"),J.dayjs.extend(J.duration),o.dateToDiff=function(s,e){if(void 0===s||void 0===e)return[0,0,0];var o=J.dayjs.unix(s),d=J.dayjs.unix(e),n=o.diff(d);return[J.dayjs.duration(n).hours(),J.dayjs.duration(n).minutes(),J.dayjs.duration(n).seconds()]},o.isIos=function(){var s=K.useSystemInfoStore();return s.osName||s.init(),s.isIos}}));
//# sourceMappingURL=util.js.map