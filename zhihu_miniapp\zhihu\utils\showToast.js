define("utils/showToast.js",(function(e,r,t,n,o,i,c,a,s,f,l,u,b,y,d,p,v,g,w,O,j,h,m,I,D,P,_,T,k,x,E,S,q,z,A,B,C,F,G,H){"use strict";var J=Object.defineProperty,K=Object.defineProperties,L=Object.getOwnPropertyDescriptors,M=Object.getOwnPropertySymbols,N=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable,R=function(e,r,t){return r in e?J(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t};!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==y?y:{},r=(new Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="39652cf5-5326-43e5-98c4-f34087fb9ce1",e._sentryDebugIdIdentifier="sentry-dbid-39652cf5-5326-43e5-98c4-f34087fb9ce1")}catch(e){}}();var U=e("../common/vendor.js");t.showToast=function(e){var r,t=(r=function(e,r){for(var t in r||(r={}))N.call(r,t)&&R(e,t,r[t]);var n=!0,o=!1,i=void 0;if(M)try{for(var c,a=M(r)[Symbol.iterator]();!(n=(c=a.next()).done);n=!0){t=c.value;Q.call(r,t)&&R(e,t,r[t])}}catch(e){o=!0,i=e}finally{try{n||null==a.return||a.return()}finally{if(o)throw i}}return e}({},e),K(r,L({icon:"none",mask:!0,duration:1500,position:"center"})));U.index.showToast(t)}}));
//# sourceMappingURL=showToast.js.map