define("components/book-tags/book-tags.js",(function(t,e,n,a,o,r,d,u,s,c,i,f,b,l,_,g,p,y,v,I,k,m,D,j,C,h,w,x,A,E,q,z,B,F,G,H,J,<PERSON>,<PERSON>,<PERSON>){"use strict";!function(){try{var t=void 0!==i?i:"undefined"!=typeof global?global:void 0!==l?l:{},e=(new Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="3b9a4608-9da0-421b-a3d6-90302c18a094",t._sentryDebugIdIdentifier="sentry-dbid-3b9a4608-9da0-421b-a3d6-90302c18a094")}catch(t){}}();var N=t("../../common/vendor.js"),O={__name:"book-tags",props:{data:{type:Array,default:function(){return[]}},likeCount:{default:function(){return""}}},setup:function(t){return function(e,n){var a;return{a:N.f(null==(a=t.data)?void 0:a.slice(0,2),(function(t,e,n){return{a:N.t(e?" · ":""),b:N.t(t),c:e}}))}}}},P=N._export_sfc(O,[["__scopeId","data-v-cae5238f"]]);tt.createComponent(P)}));
//# sourceMappingURL=book-tags.js.map