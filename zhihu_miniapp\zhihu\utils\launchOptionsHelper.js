define("utils/launchOptionsHelper.js",(function(e,n,t,a,c,r,s,d,i,o,u,f,l,y,g,b,h,p,O,_,v,I,S,N,D,H,L,j,m,x,A,C,E,P,T,U,k,w,J,K){"use strict";!function(){try{var e=void 0!==u?u:"undefined"!=typeof global?global:void 0!==y?y:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="1a5ab0da-a7d2-4f89-9e18-cc0205ca68ee",e._sentryDebugIdIdentifier="sentry-dbid-1a5ab0da-a7d2-4f89-9e18-cc0205ca68ee")}catch(e){}}();var Y=e("../common/vendor.js"),Z=e("../prelaunch/tt/pre-fetch-launch-options.js");t.getLaunchOptions=function(){var e=null;try{var n=Y.index.getStorageSync(Z.LAUNCH_OPTIONS_KEY||"ZH_LAUNCH_OPTIONS");if(n)try{e=JSON.parse(n)}catch(e){}}catch(e){}return e||(e=Y.index.getLaunchOptionsSync()||{}),e}}));
//# sourceMappingURL=launchOptionsHelper.js.map