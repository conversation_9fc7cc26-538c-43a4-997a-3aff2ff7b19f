define("uni_modules/uni-swipe-action/components/uni-swipe-action-item/mpother.js",(function(t,i,e,n,s,o,h,c,a,r,u,l,f,d,p,m,g,v,b,w,S,X,T,y,D,Y,x,M,C,W,_,k,I,A,O,Q,$,j,z,E){"use strict";!function(){try{var t=void 0!==u?u:"undefined"!=typeof global?global:void 0!==d?d:{},i=(new Error).stack;i&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[i]="d031f8c0-0b21-454e-9592-524ab473db4c",t._sentryDebugIdIdentifier="sentry-dbid-d031f8c0-0b21-454e-9592-524ab473db4c")}catch(t){}}();var L,R=t("../../../../common/vendor.js");L={data:function(){return{uniShow:!1,left:0,buttonShow:"none",ani:!1,moveLeft:"",elClass:"Uni_".concat(Math.ceil(1e6*Math.random()).toString(36))}},watch:{show:function(t){this.autoClose||this.openState(t)},left:function(){this.moveLeft="translateX(".concat(this.left,"px)")},buttonShow:function(t){this.autoClose||this.openState(t)},leftOptions:function(){this.init()},rightOptions:function(){this.init()}},mounted:function(){this.swipeaction=this.getSwipeAction(),this.swipeaction&&Array.isArray(this.swipeaction.children)&&this.swipeaction.children.push(this),this.init()},methods:{init:function(){var t=this;h(this.timer),this.timer=o((function(){t.getSelectorQuery()}),100),this.left=0,this.x=0},closeSwipe:function(t){this.autoClose&&this.swipeaction&&this.swipeaction.closeOther(this)},appTouchStart:function(t){var i=t.changedTouches[0].clientX;this.clientX=i,this.timestamp=(new Date).getTime()},appTouchEnd:function(t,i,e,n){var s=t.changedTouches[0].clientX,o=Math.abs(this.clientX-s),h=(new Date).getTime()-this.timestamp;o<40&&h<300&&this.$emit("click",{content:e,index:i,position:n})},touchstart:function(t){this.disabled||(this.ani=!1,this.x=this.left||0,this.stopTouchStart(t),this.autoClose&&this.closeSwipe())},touchmove:function(t){if(!this.disabled&&(this.stopTouchMove(t),"horizontal"===this.direction))return this.move(this.x+this.deltaX),!1},touchend:function(){this.disabled||this.moveDirection(this.left)},move:function(t){t=t||0;var i=this.leftWidth,e=this.rightWidth;this.left=this.range(t,-e,i)},range:function(t,i,e){return Math.min(Math.max(t,i),e)},moveDirection:function(t){var i=this.threshold,e=this.isopen||"none",n=this.leftWidth,s=this.rightWidth;0!==this.deltaX?"none"===e&&s>0&&-t>i||"none"!==e&&s>0&&s+t<i?this.openState("right"):"none"===e&&n>0&&t>i||"none"!==e&&n>0&&n-t<i?this.openState("left"):this.openState("none"):this.openState("none")},openState:function(t){var i=this,e=this.leftWidth,n=this.rightWidth,s="";switch(this.isopen=this.isopen?this.isopen:"none",t){case"left":s=e;break;case"right":s=-n;break;default:s=0}this.isopen!==t&&(this.throttle=!0,this.$emit("change",t)),this.isopen=t,this.ani=!0,this.$nextTick((function(){i.move(s)}))},close:function(){this.openState("none")},getDirection:function(t,i){return t>i&&t>10?"horizontal":i>t&&i>10?"vertical":""},resetTouchStatus:function(){this.direction="",this.deltaX=0,this.deltaY=0,this.offsetX=0,this.offsetY=0},stopTouchStart:function(t){this.resetTouchStatus();var i=t.touches[0];this.startX=i.clientX,this.startY=i.clientY},stopTouchMove:function(t){var i=t.touches[0];this.deltaX=i.clientX-this.startX,this.deltaY=i.clientY-this.startY,this.offsetX=Math.abs(this.deltaX),this.offsetY=Math.abs(this.deltaY),this.direction=this.direction||this.getDirection(this.offsetX,this.offsetY)},getSelectorQuery:function(){var t=this;R.index.createSelectorQuery().in(this).selectAll("."+this.elClass).boundingClientRect((function(i){if(0!==i.length){var e;e=t.autoClose?"none":t.show,t.leftWidth=i[0].width||0,t.rightWidth=i[1].width||0,t.buttonShow=e}})).exec()}}};var U=L;e.mpother=U}));
//# sourceMappingURL=mpother.js.map