define("pages/mine/mine.js",(function(e,n,t,r,o,u,s,a,i,l,c,f,d,p,v,m,g,T,h,y,b,k,x,_,I,j,w,M,<PERSON>,P,R,E,A,U,S,B,D,O,L,N){"use strict";function F(e,n){var t,r,o,u,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return u={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function a(u){return function(a){return function(u){if(t)throw new TypeError("Generator is already executing.");for(;s;)try{if(t=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return s.label++,{value:u[1],done:!1};case 5:s.label++,r=u[1],u=[0];continue;case 7:u=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==u[0]&&2!==u[0])){s=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){s.label=u[1];break}if(6===u[0]&&s.label<o[1]){s.label=o[1],o=u;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(u);break}o[2]&&s.ops.pop(),s.trys.pop();continue}u=n.call(e,s)}catch(e){u=[6,e],r=0}finally{t=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,a])}}}var H=function(e,n,t){return new o((function(r,u){var s=function(e){try{i(t.next(e))}catch(e){u(e)}},a=function(e){try{i(t.throw(e))}catch(e){u(e)}},i=function(e){return e.done?r(e.value):o.resolve(e.value).then(s,a)};i((t=t.apply(e,n)).next())}))};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==p?p:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="f808bf31-20d4-4d6f-b885-d094a7392c47",e._sentryDebugIdIdentifier="sentry-dbid-f808bf31-20d4-4d6f-b885-d094a7392c47")}catch(e){}}();var G=e("../../common/vendor.js"),V=e("../../utils/login.js"),K=e("../../utils/constant.js"),q=e("../../utils/appInit.js"),W=e("../../stores/modules/user/index.js"),X=e("../../stores/modules/history/index.js");e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js");var z=e("../../stores/modules/purchaseInfo/index.js"),J=e("../../stores/modules/clientInfo/index.js");e("../../stores/modules/systenInfo/index.js");var Y=e("../../stores/modules/globalVariable/index.js");e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js");var Q=e("../../utils/track.js"),Z=e("../../utils/pageHelper.js"),$=e("../../utils/forceUpgradeApp.js"),ee=e("../../writeConstant.js"),ne=e("./fetch.js");e("../../utils/utils.js"),e("../../prelaunch/tt/pre-init-info.js"),e("../../utils/appFetch.js"),e("../../utils/request.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),e("../../utils/onGetUdid.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../manuscript/store.js"),Math||(te+re)();var te=function(){return"../../components/login-popup/login-popup.js"},re=function(){return"../../commonPackage/components/confirm-modal/confirm-modal.js"},oe={__name:"mine",setup:function(e){var n=this,t=G.ref(""),r=G.ref(null),o=G.ref("im"),u=X.useHistoryStore(),s=W.useUserStore(),a=Y.useGlobalVariable(),i=z.usePurchaseInfo(),l=G.storeToRefs(i),c=l.isReceivedWelfare,f=l.welfareType,d=G.ref(!1),p=G.ref(null),v=G.getCurrentInstance(),m=G.storeToRefs(s),g=m.name,T=m.avatarUrl,h=m.expireText,y=m.isVip,b=m.isLogin,k=m.renewInfo,x=m.renewTips,_=m.vipNameCn,I=m.isAppletVip,j=G.storeToRefs(a),w=j.imId,M=j.desktop,C=(j.iosPaymentSwitch,G.ref(!0)),P=G.ref(!1),R=0;G.onMounted((function(){}));var E=function(){Q.track({elementText:"添加到桌面",elementType:"Block",eventType:"Click",moduleId:"add_desktop_tab",message:"我的页添加到桌面点击"}),u.updateDesktopFlagInfo({addFlag:!0})},A=G.computed((function(){return d.value&&c.value&&"xima"===f.value})),U=function(){if(++R>=10){R=0;var e=J.useClientStore().udid;G.index.setClipboardData({data:e,success:function(){}})}},S=function(){G.index.setClipboardData({data:K.XIMA_WELFARE_URL,success:function(){G.index.showToast({title:"复制成功",icon:"none"})}})},B=function(){P.value&&$.forceUpgradeApp({pageName:"我的页",ignoreKm:!0})},D=function(){var e,n;null==(n=null==(e=v.refs)?void 0:e.confirmModalRef)||n.open()},O=function(){return H(n,null,(function(){var e;return F(this,(function(n){switch(n.label){case 0:return[4,ne.getBenefitConfig()];case 1:return e=n.sent(),d.value=1==e.activityStatus,[2]}}))}))};G.onLoad((function(){var e=q.getMiniVersion();t.value=e||"",G.index.getUpdateManager().onCheckForUpdate((function(e){e.hasUpdate&&(P.value=!0)}))})),G.onShow((function(){return H(n,null,(function(){var e;return F(this,(function(n){switch(n.label){case 0:return Q.track({elementType:"Page",eventType:"Show",message:"我的页曝光"}),[4,V.isLoginMp()];case 1:return n.sent(),b.value?(null==(e=r.value)||e.close(),s.updateUserInfo(),s.updateRenewInfo(),O()):V.loginMp(),[2]}}))}))}));var L=function(){var e;b.value=!0,null==(e=r.value)||e.close()};G.onShareAppMessage((function(){return Q.track({elementType:"Button",eventType:"Click",message:"我的页分享按钮点击"}),Z.getAppShareMessage()})),G.onTabItemTap((function(){Q.track({elementText:"我的",elementType:"Button",eventType:"Click",message:"我的icon点击"})}));var N=function(){var e,n;Q.track({elementText:"立即登录",elementType:"Button",eventType:"Click",message:"立即登录按钮点击"}),null==(n=null==(e=v.refs)?void 0:e.loginPopupRef)||n.open()},te=function(){return H(n,null,(function(){return F(this,(function(e){return Q.track({elementType:"Card",eventType:"Click",moduleId:"mine_vip_card",message:"我的页会员卡片点击"}),G.index.navigateTo({url:K.BUY_MEMBER_PAGE_PATH}),[2]}))}))},re=function(){Q.track({elementText:"帮助与反馈",elementType:"Block",eventType:"Click",moduleId:"im_tab",message:"帮助与反馈点击"})},oe=function(){b.value=!0},ue=function(){G.index.showModal({title:"帮助与反馈",content:"客服电话：13331055037",showCancel:!1})},se=function(){Q.track({elementText:"订阅管理",elementType:"Block",eventType:"Click",moduleId:"renew_tab",message:"订阅管理点击",extra:{is_reddot:x.value.showUnreadTip}}),G.index.navigateTo({url:"/sub_pages/renewManage/renewManage"})},ae=function(){Q.track({elementText:"设置",elementType:"Block",eventType:"Click",moduleId:"setting_tab",message:"设置点击"}),G.index.navigateTo({url:"/sub_pages/setting/setting"})},ie=function(){G.index.navigateTo({url:"/sub_pages/orderList/orderList"})};return function(e,n){var u,s,a,i;return G.e({a:G.unref(b)},G.unref(b)?{b:G.unref(T)}:{},{c:G.o(U),d:G.t(G.unref(g)||"游客用户"),e:G.unref(y)&&C.value},(G.unref(y)&&C.value,{}),{f:!G.unref(b)},G.unref(b)?{}:{g:G.o(N)},{h:C.value},C.value?G.e({i:G.unref(I)},(G.unref(I),{}),{j:G.t("".concat(G.unref(_)).concat(G.unref(h))),k:G.t(G.unref(y)?"立即续费":"立即开通"),l:G.o(te),m:G.n({svip:G.unref(y)&&!G.unref(I)})}):{},{n:G.o(ie),o:G.unref(A)},G.unref(A)?{p:G.o(D)}:{},{q:o.value,r:G.unref(w),s:G.o(re),t:G.o(oe),v:G.o(ue),w:null==(u=G.unref(k))?void 0:u.contractId},(null==(s=G.unref(k))?void 0:s.contractId)?G.e({x:G.unref(x).showUnreadTip},(G.unref(x).showUnreadTip,{}),{y:G.o(se)}):{},{z:G.o(ae),A:null==(a=G.unref(M))?void 0:a.includes(G.unref(K.DESKTOP_MAP).mine)},(null==(i=G.unref(M))?void 0:i.includes(G.unref(K.DESKTOP_MAP).mine))?{B:G.o(E)}:{},{C:t.value},t.value?G.e({D:P.value},P.value?{E:G.t(G.unref(ee.MINI_APP_NAME))}:{},{F:G.t(t.value),G:G.o(B)}):{},{H:!G.unref(b)},G.unref(b)?{}:{I:G.sr(r,"200225b2-0",{k:"loginPopupRef"}),J:G.o(L)},{K:G.sr(p,"200225b2-1",{k:"confirmModalRef"}),L:G.o(S),M:G.p({title:G.unref(K.PROMOTION_CONFIRM_TITLE_MINE),confirmText:G.unref(K.PROMOTION_CONFIRM_TEXT)})})}}},ue=G._export_sfc(oe,[["__scopeId","data-v-200225b2"]]);oe.__runtimeHooks=2,tt.createPage(ue)}));
//# sourceMappingURL=mine.js.map