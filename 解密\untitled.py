import base64
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

def decrypt_aes_cbc(encrypted_data, key, iv):
    """
    使用AES-CBC模式解密数据
    """
    cipher = AES.new(key, AES.MODE_CBC, iv)
    decrypted_data = cipher.decrypt(encrypted_data)
    return unpad(decrypted_data, AES.block_size).decode('utf-8')

def process_encrypted_data():
    """
    处理加密数据
    """
    # 读取 encrypt_section.json 文件
    with open("C:\\botfanqie\\解密\\encrypt_section.json", "r", encoding="utf-8") as file:
        data_dict = json.load(file)
    
    # 处理 payload 字段
    if "payload" in data_dict:
        payload = data_dict["payload"]
        print("原始 payload:", payload)
        
        # 移除前缀"100"
        if payload.startswith("100"):
            encrypted_content = payload[3:]
            print("移除前缀后的加密内容:", encrypted_content)
        else:
            encrypted_content = payload
            
        # 注意：要完全解密内容，我们需要RSA私钥来解密获取AES密钥和IV
        # 由于缺乏私钥，暂时无法完成完整解密
        
        # 如果你有AES密钥和IV，可以使用以下代码进行解密:
         key = b'3e6cebec56e4bd95e8ae6ed4bf03c52e'  # 16字节密钥
         iv = b'54dee60aac15d992a4293886d5119b0c'    # 16字节IV
         decoded_data = base64.b64decode(encrypted_content)
         decrypted_text = decrypt_aes_cbc(decoded_data, key, iv)
         print("解密结果:", decrypted_text)
        
    # 处理 data 字段（如果存在）
    if "data" in data_dict:
        data = data_dict["data"]
        print("原始 data:", data)
        # 同样需要RSA私钥才能解密
        
    print("\n注意：完整解密需要RSA私钥或直接的AES密钥和IV。")
    print("请提供相应的密钥信息以完成解密。")

if __name__ == "__main__":
    process_encrypted_data()