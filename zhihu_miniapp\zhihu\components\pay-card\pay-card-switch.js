define("components/pay-card/pay-card-switch.js",(function(e,n,t,s,o,r,u,i,a,l,p,c,d,v,f,m,j,g,y,h,_,b,k,I,x,T,w,D,C,N,P,q,S,M,F,A,B,E,H,L){"use strict";!function(){try{var e=void 0!==p?p:"undefined"!=typeof global?global:void 0!==v?v:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="57e85b3d-a35a-459d-bcb3-ddde4c481902",e._sentryDebugIdIdentifier="sentry-dbid-57e85b3d-a35a-459d-bcb3-ddde4c481902")}catch(e){}}();var U=e("../../common/vendor.js");e("../../stores/modules/user/index.js"),e("../../stores/modules/history/index.js"),e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js");var R=e("../../stores/modules/purchaseInfo/index.js");e("../../stores/modules/clientInfo/index.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js"),e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js");var G=e("../../utils/track.js");e("../../utils/appFetch.js"),e("../../utils/request.js"),e("../../writeConstant.js"),e("../../utils/appInit.js"),e("../../utils/onGetUdid.js"),e("../../utils/utils.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),e("../../utils/pageHelper.js"),e("../../utils/constant.js"),e("../../pages/manuscript/store.js"),Math||(V+O+z+J+K)();var O=function(){return"./components/promotion-card.js"},V=function(){return"./components/sku-card-list.js"},z=function(){return"./components/pay-agreement.js"},J=function(){return"../recharge-tips/recharge-tips.js"},K=function(){return"../confirm-popup/confirm-popup.js"},Q={__name:"pay-card-switch",props:{type:{type:String,required:!0,default:""},handleTimeUp:{type:s,required:!0,default:function(){}},isNovelPlugin:{type:Boolean,required:!1,default:!1}},setup:function(e){var n=e,t=U.ref(!1),s=U.ref(!1),o=R.usePurchaseInfo(),r=U.storeToRefs(o).couponAnnualCardInfo,u=function(){G.track({elementType:"Block",eventType:"Click",moduleId:"vipmini_recharge_panel_payment_information",message:"充值面板付费须知-模块单击"}),n.isNovelPlugin?U.index.showModal({title:"付费须知",content:c.value,showCancel:!1}):s.value=!0},i=function(){G.track({elementType:"Block",eventType:"Click",moduleId:"vipmini_recharge_panel_no_receive",message:"充值面板充值未到账-模块单击"}),n.isNovelPlugin?U.NovelPlugin.getCurrentNovelManager().setFullScreenComponentStatus({show:!0}):t.value=!0},a="manuscript"===n.type,l=U.storeToRefs(o),p=l.skuList,c=l.prompt,d=l.appletPackage,v=l.serverNowTime,f=l.promotionDescFirst,m=l.promotionDescSecond,j=l.discount,g=U.ref("applet_svip"),y=U.computed((function(){return"svip"===g.value?p.value:d.value})),h=U.computed((function(){return"svip"===g.value})),_=function(e){var n,t=((null==(n=null==e?void 0:e.currentTarget)?void 0:n.dataset)||{}).type;t!==g.value&&(g.value=t)};return function(n,o){var l,b;return U.e({a:"applet_svip"==g.value?1:"",b:U.o(_),c:"svip"==g.value?1:"",d:U.o(_),e:"applet_svip"==g.value},(g.value,{}),{f:"svip"==g.value},(g.value,{}),{g:"applet_svip"==g.value},(g.value,{}),{h:"svip"==g.value},(g.value,{}),{i:U.n(g.value),j:U.p({skuList:U.unref(y),vipType:g.value,isShowInManuscript:a}),k:U.unref(h)},U.unref(h)?{l:U.p({vipType:g.value,expireTime:null==(b=null==(l=U.unref(r))?void 0:l.coupon)?void 0:b.expireAt,serverNowTime:U.unref(v),countdownEnds:e.handleTimeUp,promotionDescFirst:U.unref(f),promotionDescSecond:U.unref(m),discount:U.unref(j)})}:{},{m:U.p({skuList:U.unref(p),appletPackage:U.unref(d)}),n:U.o(u),o:U.o(i),p:U.o((function(e){return t.value=!1})),q:U.p({visible:t.value}),r:U.o((function(e){return s.value=e})),s:U.p({title:"付费须知",content:U.unref(c),contentAlign:"left",visible:s.value}),t:U.n(g.value)})}}},W=U._export_sfc(Q,[["__scopeId","data-v-f2cebc41"]]);tt.createComponent(W)}));
//# sourceMappingURL=pay-card-switch.js.map