define("components/tabs/tabs.js",(function(e,s,t,n,r,o,d,i,a,u,l,c,p,j,b,m,f,x,v,y,g,I,_,h,k,T,D,q,w,C,F,H,B,E,A,G,M,O,U,V){"use strict";!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==j?j:{},s=(new Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="a6bdcda4-20db-47db-af20-d4954c0d9cab",e._sentryDebugIdIdentifier="sentry-dbid-a6bdcda4-20db-47db-af20-d4954c0d9cab")}catch(e){}}();var z=e("../../common/vendor.js"),J=e("../../utils/track.js");e("../../stores/modules/user/index.js"),e("../../utils/appFetch.js"),e("../../utils/request.js"),e("../../stores/modules/history/index.js"),e("../../stores/modules/login/index.js"),e("../../utils/constant.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js"),e("../../stores/modules/purchaseInfo/index.js"),e("../../pages/manuscript/store.js"),e("../../stores/modules/clientInfo/index.js"),e("../../utils/utils.js"),e("../../writeConstant.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js"),e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js"),e("../../utils/appInit.js"),e("../../utils/onGetUdid.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),e("../../utils/pageHelper.js");var K={__name:"tabs",props:{list:{type:Array,required:!0,default:function(){return[]}},isFixedTop:{type:Boolean,required:!0,default:!1}},emits:["click"],setup:function(e,s){var t=s.emit,n=z.ref(0),r=function(e){var s,r=(null==(s=null==e?void 0:e.currentTarget)?void 0:s.dataset)||{},o=r.name,d=r.index;d!==n.value&&(J.track({elementType:"Block",eventType:"Click",elementText:o,moduleIndex:d,moduleId:"home_tabs",message:"首页分类推荐tab点击",extra:{tab_id:o}}),n.value=d,t("click",o))};return function(s,t){return{a:z.f(e.list,(function(e,s,t){return{a:z.t(e),b:s,c:e,d:s,e:s==n.value?1:""}})),b:["active-0"],c:z.o(r),d:e.isFixedTop?1:""}}}},L=z._export_sfc(K,[["__scopeId","data-v-5024d884"]]);tt.createComponent(L)}));
//# sourceMappingURL=tabs.js.map