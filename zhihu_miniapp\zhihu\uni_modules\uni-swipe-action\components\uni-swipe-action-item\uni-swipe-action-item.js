define("uni_modules/uni-swipe-action/components/uni-swipe-action-item/uni-swipe-action-item.js",(function(t,n,e,r,o,i,a,u,c,s,l,f,p,y,d,h,m,g,b,v,w,A,S,C,F,x,j,I,_,k,T,D,O,E,$,z,M,B,U,L){"use strict";function N(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function X(t){return function(t){if(Array.isArray(t))return N(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(!t)return;if("string"==typeof t)return N(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return N(t,n)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}!function(){try{var t=void 0!==l?l:"undefined"!=typeof global?global:void 0!==y?y:{},n=(new Error).stack;n&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[n]="e001ecc5-d9a3-4992-a164-c954b8f3f208",t._sentryDebugIdIdentifier="sentry-dbid-e001ecc5-d9a3-4992-a164-c954b8f3f208")}catch(t){}}();var q=t("./mpwxs.js"),G=t("./bindingx.js"),H=t("./mpother.js"),J=t("../../../../common/vendor.js"),K={},P={},Q={mixins:[q.mpMixins,G.bindIngXMixins,H.mpother],emits:["click","change"],props:{show:{type:String,default:"none"},disabled:{type:Boolean,default:!1},autoClose:{type:Boolean,default:!0},threshold:{type:Number,default:20},leftOptions:{type:Array,default:function(){return[]}},rightOptions:{type:Array,default:function(){return[]}}},unmounted:function(){this.__isUnmounted=!0,this.uninstall()},methods:{uninstall:function(){var t=this;this.swipeaction&&this.swipeaction.children.forEach((function(n,e){n===t&&t.swipeaction.children.splice(e,1)}))},getSwipeAction:function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"uniSwipeAction",n=this.$parent,e=n.$options.name;e!==t;){if(!(n=n.$parent))return!1;e=n.$options.name}return n}}};"function"==typeof K&&K(Q),"function"==typeof P&&P(Q);var R=J._export_sfc(Q,[["render",function(t,n,e,r,o,i){var a,u,c,s,l;return{a:J.f(e.leftOptions,(function(n,e,r){return{a:J.t(n.text),b:n.style&&n.style.color?n.style.color:"#FFFFFF",c:e,d:n.style&&n.style.backgroundColor?n.style.backgroundColor:"#C7C6CD",e:n.style&&n.style.fontSize?n.style.fontSize:"16px",f:J.o((function(r){return t.appTouchEnd(r,e,n,"left")}))}})),b:J.o((function(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];return t.appTouchStart&&(a=t).appTouchStart.apply(a,X(e))})),c:J.n(t.elClass),d:J.f(e.rightOptions,(function(n,e,r){return{a:J.t(n.text),b:n.style&&n.style.color?n.style.color:"#FFFFFF",c:e,d:n.style&&n.style.backgroundColor?n.style.backgroundColor:"#C7C6CD",e:n.style&&n.style.fontSize?n.style.fontSize:"16px",f:J.o((function(r){return t.appTouchEnd(r,e,n,"right")}))}})),e:J.o((function(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];return t.appTouchStart&&(u=t).appTouchStart.apply(u,X(e))})),f:J.n(t.elClass),g:J.o((function(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];return t.touchstart&&(c=t).touchstart.apply(c,X(e))})),h:J.o((function(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];return t.touchmove&&(s=t).touchmove.apply(s,X(e))})),i:J.o((function(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];return t.touchend&&(l=t).touchend.apply(l,X(e))})),j:t.moveLeft,k:t.ani?1:""}}]]);tt.createComponent(R)}));
//# sourceMappingURL=uni-swipe-action-item.js.map