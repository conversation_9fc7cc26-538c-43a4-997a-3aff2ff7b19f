import json
import hashlib
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
import base64

# 固定参数
AES_KEY_HEX = "3e6cebec56e4bd95e8ae6ed4bf03c52e"
AES_IV_HEX = "54dee60aac15d992a4293886d5119b0c"
RSA_PUBLIC_KEY = """-----BEGIN PUBLIC KEY-----
MIIBITANBgkqhkiG9w0BAQEFAAOCAQ4AMIIBCQKCAQBTeA5vtwjrhTPG44cO7eqg
Yn1u59sbvscqULjREdmTpf1yCGQPtqBrFwe+VoKlGhcvdrnQhKrBlePXMVtAXmJD
j0LBrKCUzYPBNPnBiDaTVW1YWpPfMqfpBYVLyu0LcvKzhWdapSTNavmCsgo1EGXQ
DfX2rq3ZYF/FI4/tnrxQFrltT5Zh88kbpxLLHlKSXzKOeQ74+3LrNfeEWPS5NEZhb
GXDnAaXjTibNOLc5kWLi+IhSU47ecs5psudhPJIPI4v4NIVLRErj1LMJIy/IhYQn
my8b+ppBelEKt0B9vumphp7bMfGdUb2FSY2iKmSIUbHkb/RwGnl++b+qjqWnsfBA
gMBAAE=
-----END PUBLIC KEY-----"""

# 转换为字节
aes_key = bytes.fromhex(AES_KEY_HEX)
aes_iv = bytes.fromhex(AES_IV_HEX)

# 加载 RSA 公钥
rsa_key = RSA.import_key(RSA_PUBLIC_KEY)
cipher_rsa = PKCS1_v1_5.new(rsa_key)


def sha256_hex(data: str) -> str:
    """计算 SHA-256 并返回十六进制字符串"""
    return hashlib.sha256(data.encode()).hexdigest()


def aes_decrypt(data: bytes) -> str:
    """AES-128-CBC 解密"""
    cipher = AES.new(aes_key, AES.MODE_CBC, aes_iv)
    decrypted = cipher.decrypt(data)
    return unpad(decrypted, AES.block_size).decode('utf-8')


def rsa_encrypt(data: str) -> str:
    """RSA 加密并 Base64 编码"""
    ciphertext = cipher_rsa.encrypt(data.encode())
    return base64.b64encode(ciphertext).decode('utf-8')


def get_body(section_id: str) -> str:
    """生成加密的请求体"""
    # 1. 构造 AES 加密的密钥
    c = AES_KEY_HEX + sha256_hex(AES_KEY_HEX)[:5]  # 3e6cebec56e4bd95e8ae6ed4bf03c52e + 前5位hash

    # 2. 构造要 AES 加密的数据
    r = {"id": str(section_id)}
    p = {"data": r, "key": c, "iv": AES_IV_HEX}

    # 3. 将 p JSON 序列化后用 RSA 加密
    p_str = json.dumps(p, separators=(',', ':'))  # 去掉空格
    sign = rsa_encrypt(p_str)

    # 4. 构造最终 payload
    payload = "100" + sign  # 注意：原文是 "".concat("100").concat(sign)

    # 5. 返回给服务器的 JSON
    return json.dumps({"payload": payload})


# 测试
if __name__ == "__main__":
    section_id = "123456"  # 替换为真实的 bookid
    body = get_body(section_id)
    print("请求体 (payload):")
    print(body)