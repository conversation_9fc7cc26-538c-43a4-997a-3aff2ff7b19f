define("components/lead-generation/index.js",(function(e,t,n,r,u,a,o,c,i,s,l,d,f,p,b,v,y,h,g,T,w,x,m,I,k,G,D,_,j,C,E,L,Q,S,M,N,q,z,A,B){"use strict";function F(e,t){var n,r,u,a,o={label:0,sent:function(){if(1&u[0])throw u[1];return u[1]},trys:[],ops:[]};return a={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function c(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,r&&(u=2&a[0]?r.return:a[0]?r.throw||((u=r.return)&&u.call(r),0):r.next)&&!(u=u.call(r,a[1])).done)return u;switch(r=0,u&&(a=[2&a[0],u.value]),a[0]){case 0:case 1:u=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,r=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(u=o.trys,(u=u.length>0&&u[u.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!u||a[1]>u[0]&&a[1]<u[3])){o.label=a[1];break}if(6===a[0]&&o.label<u[1]){o.label=u[1],u=a;break}if(u&&o.label<u[2]){o.label=u[2],o.ops.push(a);break}u[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],r=0}finally{n=u=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==p?p:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="fdff8d71-8316-4c4a-8300-8499d2db318d",e._sentryDebugIdIdentifier="sentry-dbid-fdff8d71-8316-4c4a-8300-8499d2db318d")}catch(e){}}();var H=e("../../pages/manuscript/fetch.js"),J=e("../../common/vendor.js");n.useLeadGeneration=function(e){var t=e.sectionId,r=e.contentType,a=e.leadQuery,o=e.isLogin,c=J.ref(!1),i=J.reactive({code:"",guideText:"",buttonText:"",successTips:""});return J.watchEffect((function(){return e=n,s=function(){var e,n,u,s;return F(this,(function(l){switch(l.label){case 0:if(!o.value||!t.value||!r.value&&0!==Number(r.value))return[3,4];l.label=1;case 1:return l.trys.push([1,3,,4]),[4,H.createGuideCode({sectionId:t.value,contentType:r.value,leadQuery:a.value})];case 2:return e=l.sent(),n=e.code,u=e.hitManuscriptTruncateGuideConfig,s=e.manuscriptTruncateGuideConfig,c.value=u,c.value?(i.code=n,i.guideText=s.guideTips,i.buttonText=s.buttonTips,i.successTips=s.toastTips,[3,4]):[2];case 3:return l.sent(),[3,4];case 4:return[2]}}))},new u((function(t,n){var r=function(e){try{o(s.next(e))}catch(e){n(e)}},a=function(e){try{o(s.throw(e))}catch(e){n(e)}},o=function(e){return e.done?t(e.value):u.resolve(e.value).then(r,a)};o((s=s.apply(e,null)).next())}));var e,s})),[c,i]}}));
//# sourceMappingURL=index.js.map