define("stores/modules/user/index.js",(function(e,i,n,t,r,s,o,a,u,l,c,p,d,h,f,v,b,w,y,T,x,g,m,I,U,V,j,k,R,C,D,S,N,A,G,_,E,L,F,H){"use strict";function P(e,i){var n,t,r,s,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,t&&(r=2&s[0]?t.return:s[0]?t.throw||((r=t.return)&&r.call(t),0):t.next)&&!(r=r.call(t,s[1])).done)return r;switch(t=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,t=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){o.label=s[1];break}if(6===s[0]&&o.label<r[1]){o.label=r[1],r=s;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(s);break}r[2]&&o.ops.pop(),o.trys.pop();continue}s=i.call(e,o)}catch(e){s=[6,e],t=0}finally{n=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}}var W=function(e,i,n){return new r((function(t,s){var o=function(e){try{u(n.next(e))}catch(e){s(e)}},a=function(e){try{u(n.throw(e))}catch(e){s(e)}},u=function(e){return e.done?t(e.value):r.resolve(e.value).then(o,a)};u((n=n.apply(e,i)).next())}))};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==h?h:{},i=(new Error).stack;i&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[i]="bbb415ee-adc7-48b9-a49f-307aad628076",e._sentryDebugIdIdentifier="sentry-dbid-bbb415ee-adc7-48b9-a49f-307aad628076")}catch(e){}}();var q=e("../../../common/vendor.js"),z=e("../../../utils/appFetch.js"),B=e("../../../utils/utils.js"),J=e("../history/index.js"),K=e("../login/index.js");e("../search-history/index.js"),e("../koc-ascription/index.js"),e("../purchaseInfo/index.js"),e("../clientInfo/index.js"),e("../systenInfo/index.js"),e("../globalVariable/index.js"),e("../trackInfo/index.js"),e("../devDebug/index.js");var M={name:"",avatarUrl:"",vipType:"",isVip:!1,vipNameCn:"",isAppletVip:!1,isGuest:!1,isRenewal:!1,isCanVipTransfer:!1,urlToken:"",phoneNumber:"",icon:"",expireText:"",imgUrl:"",renewInfo:{},renewTips:{showUnreadTip:!1,showRenewTip:!1},isVipRemind:!1},O=q.defineStore("user",{state:function(){return M},getters:{isLogin:function(){var e=K.useLoginStore(),i=e.accessToken,n=e.expiresTime;return!B.isTokenExpired(i,n)}},actions:{updateUserInfo:function(e){var i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return W(this,null,(function(){var n,t,r,s,o,a,u,l,c,p,d,h,f,v,b,w,y,T,x,g,m,I,U,V,j,k,R,C;return P(this,(function(D){switch(D.label){case 0:return e?[3,2]:[4,z.getUserInfo(i)];case 1:e=D.sent(),D.label=2;case 2:return t=(n=e||{}).name,r=void 0===t?"":t,s=n.avatarUrl,o=void 0===s?"":s,a=n.vipType,u=void 0===a?"":a,l=n.isVip,c=void 0!==l&&l,p=n.icon,d=void 0===p?"":p,h=n.imgUrl,f=void 0===h?"":h,v=n.expireText,b=void 0===v?"":v,w=n.isAppletVip,y=void 0!==w&&w,T=n.isGuest,x=void 0!==T&&T,g=n.isRenewal,m=void 0!==g&&g,I=n.isCanVipTransfer,U=void 0!==I&&I,V=n.urlToken,j=void 0===V?"":V,k=n.phoneNumber,R=void 0===k?"":k,C=n.isVipRemind,this.name=r,this.avatarUrl=o,this.vipType=u,this.isVip=c,this.isAppletVip=y,this.isGuest=x,this.isRenewal=m,this.isCanVipTransfer=U,this.urlToken=j,this.phoneNumber=R,this.icon=d,this.imgUrl=f,this.expireText=b,this.isVipRemind=C,this.vipNameCn=c?y?"体验":"盐选":"",[2]}}))}))},resetUserInfo:function(){this.name="",this.avatarUrl="",this.vipType="",this.isVip=!1,this.icon="",this.imgUrl="",this.expireText="",this.renewInfo={}},updateRenewInfo:function(){return W(this,null,(function(){var e,i,n,t,r;return P(this,(function(s){switch(s.label){case 0:return this.isVip?[4,z.getRenewPageInfo()]:[2];case 1:return e=s.sent(),this.renewInfo=(null==e?void 0:e.data)||{},i=(null==e?void 0:e.data)||"",(n=i.nextWithholdAt)&&(t=B.getDateDiff(n),r=J.useHistoryStore(),t<=5?this.renewTips.showRenewTip=!0:(this.renewTips={showUnreadTip:!1,showRenewTip:!1},r.unreadTipClicked&&r.setUnreadTipClick(!1))),[2]}}))}))}}});n.useUserStore=O}));
//# sourceMappingURL=index.js.map