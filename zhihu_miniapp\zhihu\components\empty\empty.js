define("components/empty/empty.js",(function(e,t,n,a,d,r,o,c,b,s,i,u,l,f,p,m,y,_,x,g,v,I,h,k,C,D,T,j,w,E,$,q,z,A,B,F,G,H,J,<PERSON>){"use strict";!function(){try{var e=void 0!==i?i:"undefined"!=typeof global?global:void 0!==f?f:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="340c200d-2b04-4187-b2f3-a26ae301290d",e._sentryDebugIdIdentifier="sentry-dbid-340c200d-2b04-4187-b2f3-a26ae301290d")}catch(e){}}();var L=e("../../common/vendor.js"),M={__name:"empty",props:{text:{},btnText:{},handleCallback:{}},emits:["handleCallback"],setup:function(e){return function(t,n){return L.e({a:e.text,b:e.btnText},e.btnText?{c:L.t(e.btnText),d:L.o((function(e){return t.$emit("handleCallback")}))}:{})}}},N=L._export_sfc(M,[["__scopeId","data-v-94e6a2c8"]]);tt.createComponent(N)}));
//# sourceMappingURL=empty.js.map