define("pages/novel_plugin/fetch.js",(function(t,e,n,r,a,i,c,o,u,s,l,f,p,d,y,b,h,v,_,g,w,I,m,S,x,D,k,C,E,T,j,z,F,L,M,P,G,R,q,A){"use strict";function B(t,e){var n,r,a,i,c={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return i={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function o(i){return function(o){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;c;)try{if(n=1,r&&(a=2&i[0]?r.return:i[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,i[1])).done)return a;switch(r=0,a&&(i=[2&i[0],a.value]),i[0]){case 0:case 1:a=i;break;case 4:return c.label++,{value:i[1],done:!1};case 5:c.label++,r=i[1],i=[0];continue;case 7:i=c.ops.pop(),c.trys.pop();continue;default:if(!(a=c.trys,(a=a.length>0&&a[a.length-1])||6!==i[0]&&2!==i[0])){c=0;continue}if(3===i[0]&&(!a||i[1]>a[0]&&i[1]<a[3])){c.label=i[1];break}if(6===i[0]&&c.label<a[1]){c.label=a[1],a=i;break}if(a&&c.label<a[2]){c.label=a[2],c.ops.push(i);break}a[2]&&c.ops.pop(),c.trys.pop();continue}i=e.call(t,c)}catch(t){i=[6,t],r=0}finally{n=a=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,o])}}}var N=function(t,e,n){return new a((function(r,i){var c=function(t){try{u(n.next(t))}catch(t){i(t)}},o=function(t){try{u(n.throw(t))}catch(t){i(t)}},u=function(t){return t.done?r(t.value):a.resolve(t.value).then(c,o)};u((n=n.apply(t,e)).next())}))};!function(){try{var t=void 0!==l?l:"undefined"!=typeof global?global:void 0!==d?d:{},e=(new Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="0c51c4ae-**************-bbb14d73a5d7",t._sentryDebugIdIdentifier="sentry-dbid-0c51c4ae-**************-bbb14d73a5d7")}catch(t){}}();var Q=t("../../utils/request.js"),U=t("../../writeConstant.js"),W=t("../../utils/constant.js");n.callLottery=function(){return N(n,null,(function(){return B(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,Q.api.post("/dalaran/lottery/start")];case 1:return[2,t.sent()];case 2:return t.sent(),[3,3];case 3:return[2]}}))}))},n.createGuideCode=function(t){return N(n,[t],(function(t){var e,n,r,a,i;return B(this,(function(c){switch(c.label){case 0:e=t.sectionId,n=t.contentType,r=void 0===n?0:n,a=t.leadQuery,i=void 0===a?"":a,c.label=1;case 1:return c.trys.push([1,3,,4]),[4,Q.api.post("/dalaran/content/guide_code",{section_id:e,content_type:String(r),lead_query:i})];case 2:return[2,c.sent()];case 3:return c.sent(),[3,4];case 4:return[2]}}))}))},n.getBenefitConfig=function(){return N(n,null,(function(){return B(this,(function(t){switch(t.label){case 0:return[4,Q.api.get("/dalaran/april_fools_day/benefit_config")];case 1:return[2,t.sent()]}}))}))},n.getCommentList=function(t){return N(n,[t],(function(t){var e,n,r;return B(this,(function(a){switch(a.label){case 0:e=t.sectionId,n=t.contentType,r=void 0===n?0:n,a.label=1;case 1:return a.trys.push([1,3,,4]),[4,Q.api.get("/dalaran/content/comments",{params:{section_id:e,content_type:String(r)}})];case 2:return[2,a.sent()];case 3:return a.sent(),[3,4];case 4:return[2]}}))}))},n.getLotteryInfo=function(){return N(n,null,(function(){var t,e,n;return B(this,(function(r){switch(r.label){case 0:return[4,Q.api.get("/dalaran/cancel_payment/activity")];case 1:return n=r.sent(),[2,((null==(e=null==(t=null==n?void 0:n.data)?void 0:t.lottery)?void 0:e.prizes)&&(n.data.lottery.prizes=n.data.lottery.prizes.map((function(t){return{id:t.id,fonts:[{text:t.desc,top:"50%",fontSize:"38.15rpx",fontColor:"#ED5848"},{text:t.name,top:"10%",fontSize:"30.52rpx",fontColor:"#FFB700"}],background:"#FFF9ED"}}))),n)]}}))}))},n.getManuscript=function(t){return N(n,[t],(function(t){var e,n,r;return B(this,(function(a){switch(a.label){case 0:return e=t.contentId,n=t.contentType,r=void 0===n?0:n,[4,Q.api.post("/dalaran/manuscript/encrypt_section",{id:e,content_type:String(r)},{custom:{useSignature:!0,isReport:!0}})];case 1:return[2,a.sent()]}}))}))},n.preLoadManuscript=function(t){return N(n,[t],(function(t){var e,n,r;return B(this,(function(a){switch(a.label){case 0:e=t.contentId,n=t.contentType,r=void 0===n?0:n,a.label=1;case 1:return a.trys.push([1,3,,4]),[4,Q.api.post("/dalaran/manuscript/truncate_section",{payload:e,content_type:String(r)},{id:W.GET_MANUSCRIPT_REQUEST_ID,usePrefetchCache:!0})];case 2:return[2,a.sent()];case 3:return a.sent(),[3,4];case 4:return[2]}}))}))},n.receiveWelfare=function(t){return N(n,null,(function(){return B(this,(function(e){switch(e.label){case 0:return[4,Q.api.post("/dalaran/april_fools_day/receive_benefit",{benefit_type:t})];case 1:return[2,e.sent()]}}))}))},n.trackDeviceEvent=function(t){return N(n,null,(function(){return B(this,(function(e){return[2,Q.api.get("/api/dalaran-nodejs/open/track/device-info",{params:{channel_id:U.MINI_APPID,content_id:t}}).catch((function(){}))]}))}))}}));
//# sourceMappingURL=fetch.js.map