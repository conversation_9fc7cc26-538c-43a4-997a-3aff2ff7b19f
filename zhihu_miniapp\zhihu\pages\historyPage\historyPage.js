define("pages/historyPage/historyPage.js",(function(e,t,n,o,r,i,s,u,l,a,c,d,p,f,v,m,y,b,h,g,w,j,x,k,_,I,T,P,C,S,O,D,H,E,M,A,B,G,L,q){"use strict";function J(e,t){var n,o,r,i,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,o&&(r=2&i[0]?o.return:i[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,i[1])).done)return r;switch(o=0,r&&(i=[2&i[0],r.value]),i[0]){case 0:case 1:r=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,o=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!r||i[1]>r[0]&&i[1]<r[3])){s.label=i[1];break}if(6===i[0]&&s.label<r[1]){s.label=r[1],r=i;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(i);break}r[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],o=0}finally{n=r=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}}var F=Object.defineProperty,N=Object.defineProperties,R=Object.getOwnPropertyDescriptors,U=Object.getOwnPropertySymbols,V=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable,K=function(e,t,n){return t in e?F(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},Q=function(e,t){for(var n in t||(t={}))V.call(t,n)&&K(e,n,t[n]);var o=!0,r=!1,i=void 0;if(U)try{for(var s,u=U(t)[Symbol.iterator]();!(o=(s=u.next()).done);o=!0){n=s.value;z.call(t,n)&&K(e,n,t[n])}}catch(e){r=!0,i=e}finally{try{o||null==u.return||u.return()}finally{if(r)throw i}}return e},W=function(e,t){return N(e,R(t))},X=function(e,t,n){return new r((function(o,i){var s=function(e){try{l(n.next(e))}catch(e){i(e)}},u=function(e){try{l(n.throw(e))}catch(e){i(e)}},l=function(e){return e.done?o(e.value):r.resolve(e.value).then(s,u)};l((n=n.apply(e,t)).next())}))};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==f?f:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="27b12378-4231-4b84-918f-6c219420b4fc",e._sentryDebugIdIdentifier="sentry-dbid-27b12378-4231-4b84-918f-6c219420b4fc")}catch(e){}}();var Y=e("../../common/vendor.js");e("../../stores/modules/user/index.js");var Z=e("../../stores/modules/history/index.js");e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js"),e("../../stores/modules/purchaseInfo/index.js"),e("../../stores/modules/clientInfo/index.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js"),e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js");var $=e("../../utils/track.js"),ee=e("../../utils/pageHelper.js"),te=e("../../utils/appFetch.js"),ne=e("../../components/novel-plugin/novelPluginJump.js");e("../../utils/utils.js"),e("../../writeConstant.js"),e("../../utils/constant.js"),e("../manuscript/store.js"),e("../../utils/request.js"),e("../../utils/appInit.js"),e("../../utils/onGetUdid.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),Array||(Y.resolveComponent("uni-swipe-action-item")+Y.resolveComponent("view-log")+Y.resolveComponent("uni-swipe-action")+Y.resolveComponent("empty"))(),Math||(oe+function(){return"../../uni_modules/uni-swipe-action/components/uni-swipe-action-item/uni-swipe-action-item.js"}+function(){return"../../components/view-log/view-log.js"}+function(){return"../../uni_modules/uni-swipe-action/components/uni-swipe-action/uni-swipe-action.js"}+function(){return"../../components/empty/empty.js"})();var oe=function(){return"../../components/book-tags/book-tags.js"},re={__name:"historyPage",setup:function(e){var t=this,n=Z.useHistoryStore(),o=Y.storeToRefs(n).historyList,r=Y.ref([]),i=Y.ref(!0),s=function(e){var t=e.title,n=e.id,o=e.index;$.track({elementType:"Card",eventType:"Show",elementText:t,moduleIndex:o,moduleId:"history_item_card",message:"浏览历史卡片曝光",extra:{section_id:n}})},u=function(){return X(t,null,(function(){var e,t;return J(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),e=[],o.value.map((function(t){return e.push(t.id)})),[4,te.getGreyList({scene:1,section_ids:e})];case 1:return t=n.sent(),r.value=o.value.filter((function(e){return!!(null==t?void 0:t[e.id])})),[3,3];case 2:return n.sent(),r.value=o.value,[3,3];case 3:return[2]}}))}))};return Y.onShow((function(){return X(t,null,(function(){return J(this,(function(e){switch(e.label){case 0:return $.track({elementType:"Page",eventType:"Show",message:"历史记录页曝光"}),[4,u()];case 1:return e.sent(),[2]}}))}))})),Y.onShareAppMessage((function(){return $.track({elementType:"Button",eventType:"Click",message:"历史记录页分享按钮点击"}),ee.getAppShareMessage()})),Y.onTabItemTap((function(){$.track({elementText:"浏览历史",elementType:"Button",eventType:"Click",message:"浏览历史icon点击"})})),function(e,t){return Y.e({a:r.value.length},r.value.length?{b:Y.f(r.value,(function(e,t,r){var s,l;return Y.e({a:null==e?void 0:e.title,b:Y.t(null==e?void 0:e.content),c:Y.n((null==e?void 0:e.artwork)?"with-cover":""),d:null==e?void 0:e.artwork},(null==e?void 0:e.artwork)?{e:"url(".concat(e.artwork,")")}:{},{f:null==(s=null==e?void 0:e.labels)?void 0:s.length},(null==(l=null==e?void 0:e.labels)?void 0:l.length)?{g:"e0e95ddb-3-"+r+",e0e95ddb-2-"+r,h:Y.p({data:null==e?void 0:e.labels,likeCount:null==e?void 0:e.likeCount})}:{},i.value?{i:Y.t((null==e?void 0:e.is_finished)?"已读完":"未读完")}:{},{j:Y.o((function(n){return function(e){var t=e.title,n=e.id,o=e.index,r=e.wxBookId,i=e.contentType,s=void 0===i?0:i;$.track({elementType:"Card",eventType:"Show",elementText:t,moduleIndex:o,moduleId:"history_item_card",message:"浏览历史卡片点击",extra:{section_id:n}});var u="/pages/novel_plugin/index?id=".concat(n,"&bookId=").concat(r,"&content_type=").concat(s);ne.NovelPluginJump({url:u})}(W(Q({},e),{index:t}))})),k:Y.o((function(t){return function(e){var t=o.value.filter((function(t){return t.id!==e}));n.updateHistoryList(t),u(),Y.index.showToast({title:"记录删除成功"})}(e.id)})),l:"e0e95ddb-2-"+r+",e0e95ddb-1-"+r,m:"e0e95ddb-1-"+r+",e0e95ddb-0",n:Y.p({data:W(Q({},e),{index:t})}),o:e.id})})),c:i.value,d:Y.o(s)}:{},{e:!Y.unref(o).length},Y.unref(o).length?{}:{f:Y.p({text:"什么还没有读哦"})})}}},ie=Y._export_sfc(re,[["__scopeId","data-v-e0e95ddb"]]);re.__runtimeHooks=2,tt.createPage(ie)}));
//# sourceMappingURL=historyPage.js.map