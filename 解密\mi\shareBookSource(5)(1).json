[{"bookSourceComment": "抓包，填authorization的值到设置源变量", "bookSourceGroup": "抖音小程序", "bookSourceName": "知乎小说", "bookSourceType": 0, "bookSourceUrl": "https://api.zhihu.com", "bookUrlPattern": "https?:\\/\\/www.iesdouyin.com\\/share\\/microapp\\/\\?token=.*&share_channel=知乎小说", "customOrder": 0, "enabled": true, "enabledCookieJar": false, "enabledExplore": true, "header": "@js:\nvar token = source.getVariable()\nvar t = {\n\t\"User-Agent\": \"Mozilla/5.0 (Linux; Android 13; 2210132C Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.156 Mobile Safari/537.36 aweme/24.2.0 ToutiaoMicroApp/2.81.1 PluginVersion/24209009\",\n\t\"x-mini-token\": \"tt9d69ebd4025fd5ea01\",\n\t\"x-dy-app-name\": \"<PERSON><PERSON><PERSON>\",\n\t\"authorization\":token\n}\nJSON.stringify(t)", "jsLib": "var PublicKey = \"MIIBITANBgkqhkiG9w0BAQEFAAOCAQ4AMIIBCQKCAQBTeA5vtwjrhTPG4cO7eqgYn1u59sbvscqULjREdmTpf1yCGQPtqBrFwe+VoKlGhcvdrnQhKrBlePXMVtAXmJDj0LBrKCUzYPBNPnBiDaTVW1YWpPfMqfpBYVLyu0LcvKzhWdapSTNavmCsgo1EGXQDfX2rq3ZYF/FI4/tnrxQFrltT5Zh88kbpxLLHlKSXzKOeQ74+3LrNfeEWPS5NEZhbGXBDnAaXjTibNOLc5kWLi+IhSU47ecs5psudhPJIPI4v4NIVLRErj1LMJIy/IhYQnmy8b+ppBelEKt0B9vumphp7bMfGdUb2FSY2iKmSIUbHkb/RwGnl++b+qjqWnsfBAgMBAAE=\"\r\nvar aeskey = \"3e6cebec56e4bd95e8ae6ed4bf03c52e\"\r\nvar aesiv = \"54dee60aac15d992a4293886d5119b0c\"\r\nfunction RSAencrypt(text, PublicKey,t) {\r\n    var cipher = t.java.createAsymmetricCrypto(\"RSA/ECB/PKCS1Padding\")\r\n    var key = t.java.base64DecodeToByteArray(PublicKey)\r\n    cipher.setPublicKey(key)\r\n    var res = cipher.encryptBase64(text, true)\r\n    return res\r\n}\r\nfunction get_body(sectionId){\r\n  c = aeskey + this.java.digestHex(aeskey, \"SHA-256\").substring(0, 5);\r\n  r = {id:String(sectionId)}\r\n  p = {data: r,key: c,iv: aesiv}\r\n  sign = RSAencrypt(JSON.stringify(p), PublicKey,this)\r\n  return JSON.stringify({\r\n  \t  payload: \"\".concat(\"100\").concat(sign)\r\n  \t})\r\n}\nfunction AESdecrypt(text) {\r\n    const { java } = this\r\n    key = java.hexDecodeToByteArray(aeskey)\r\n    iv = java.hexDecodeToByteArray(aesiv)\r\n    var cipher = java.createSymmetricCrypto(\"AES/CBC/PKCS7Padding\", key, iv)\r\n    var res = cipher.decryptStr(text)\r\n    return res\r\n} \n", "lastUpdateTime": 1753715295206, "loginUi": "", "loginUrl": "", "respondTime": 180000, "ruleBookInfo": {"coverUrl": "@js:\nif(/iesdouyin/.test(baseUrl)){\n\tresult = java.getString(\"$.manuscript_info.artwork\")\n}else{\n result = \"\"\n}", "init": "<js>\nif(/iesdouyin/.test(baseUrl)){\n\t  let bookid = result.match(/Fid%253D(\\d+)%/)[1]\n\t  body  = get_body(bookid)\n   url = \"https://api.zhihu.com/dalaran/manuscript/encrypt_section,\" + JSON.stringify({\"method\":\"POST\",\"body\":body})\n   res = java.ajax(url)\n\t  java.log(bookid)\n\t  java.put(\"bkid\",bookid)\n\t  result = AESdecrypt(JSON.parse(res).data)\n\t}else{\n\t\tresult = result\n\t}\n</js>", "intro": "$.manuscript_info.content", "name": "@js:\nif(/iesdouyin/.test(baseUrl)){\n\tresult = java.getString(\"$.manuscript_info.title\")\n}else{\nresult = \"\"\n}", "tocUrl": "@js:\nif(/iesdouyin/.test(baseUrl)){\nvar sid = java.get(\"bkid\")\nbody  = get_body(sid)\nurl = \"https://api.zhihu.com/dalaran/manuscript/encrypt_section,\" + JSON.stringify({\"method\":\"POST\",\"body\":body})\nresult = url\n}else{\n result  = baseUrl\n}"}, "ruleContent": {"content": "$.data\n<js>\ntxt = result\nresult = AESdecrypt(txt)\n</js>\n$.manuscript_content.data"}, "ruleExplore": {}, "ruleReview": {}, "ruleSearch": {"bookList": "data", "bookUrl": "@js:\nvar sid = java.getString(\"$.section_id\")\n//java.log(sid)\nbody  = get_body(sid)\nurl = \"https://api.zhihu.com/dalaran/manuscript/encrypt_section,\" + JSON.stringify({\"method\":\"POST\",\"body\":body})", "checkKeyWord": "网恋校草后我翻车了", "coverUrl": "$.artwork", "intro": "$.content", "kind": "$.labels", "name": "$.title##\\</?em\\>##"}, "ruleToc": {"chapterList": "@js:\nd = [{title:\"第一章\",url:baseUrl}]", "chapterName": "title", "chapterUrl": "url"}, "searchUrl": "https://api.zhihu.com/dalaran/search/result?search_title={{key}}", "weight": 0}]