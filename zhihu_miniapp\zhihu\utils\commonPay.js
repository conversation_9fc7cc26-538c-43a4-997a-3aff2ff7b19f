define("utils/commonPay.js",(function(e,r,n,t,o,a,u,s,i,c,l,f,d,b,y,p,v,h,m,g,S,_,w,E,C,T,x,R,I,j,k,A,U,D,P,H,K,O,$,q){"use strict";function L(e,r){var n,t,o,a,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,t&&(o=2&a[0]?t.return:a[0]?t.throw||((o=t.return)&&o.call(t),0):t.next)&&!(o=o.call(t,a[1])).done)return o;switch(t=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return u.label++,{value:a[1],done:!1};case 5:u.label++,t=a[1],a=[0];continue;case 7:a=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){u=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){u.label=a[1];break}if(6===a[0]&&u.label<o[1]){u.label=o[1],o=a;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(a);break}o[2]&&u.ops.pop(),u.trys.pop();continue}a=r.call(e,u)}catch(e){a=[6,e],t=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}}var M=function(e,r,n){return new o((function(t,a){var u=function(e){try{i(n.next(e))}catch(e){a(e)}},s=function(e){try{i(n.throw(e))}catch(e){a(e)}},i=function(e){return e.done?t(e.value):o.resolve(e.value).then(u,s)};i((n=n.apply(e,r)).next())}))};!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==b?b:{},r=(new Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="e7d14164-07f9-4a15-85ab-9a2699b39b5e",e._sentryDebugIdIdentifier="sentry-dbid-e7d14164-07f9-4a15-85ab-9a2699b39b5e")}catch(e){}}();var G=e("../common/vendor.js"),N=e("./request.js"),X=e("./utils.js"),z=e("./paymentSubsApi.js");n.formatError=function(e){var r,n,t,o=new Error;return e&&"object"==(void 0===e?"undefined":(t=e)&&"undefined"!=typeof Symbol&&t.constructor===Symbol?"symbol":typeof t)?e.data?o.message=(null==(n=null==(r=e.data)?void 0:r.error)?void 0:n.message)||"unkonwn api request error":e.errMsg&&(o.message=e.errMsg):o.message="未知错误，当前错误信息不合法， current error : ".concat(e),o._source=e,o},n.getPayStaus=function(e){return M(n,[e],(function(e){var r,n,t,o,a,u;return L(this,(function(s){switch(s.label){case 0:r=e.dealId,n=e.count,t=void 0===n?20:n,s.label=1;case 1:s.trys.push([1,7,,8]),G.index.$emit(z.CHECK_STATUS_POLL_START),s.label=2;case 2:return t>0?[4,N.api.get("/order/v1/delivery/status",{params:{deal_id:r}})]:[3,6];case 3:return o=s.sent(),(a=o.producerIsReady)?[2,(G.index.$emit(z.CHECK_STATUS_SUCCESS,{response:o}),a)]:(t--,[4,X.sleep(500)]);case 4:s.sent(),s.label=5;case 5:return[3,2];case 6:return[3,8];case 7:return u=s.sent(),G.index.$emit(z.CHECK_STATUS_ERROR,{error:u}),[3,8];case 8:return[2,(t<=0&&G.index.$emit(z.CHECK_STATUS_EXCEED_COUNT_ERROR),!1)]}}))}))},n.getProvider=function(e){return M(n,null,(function(){return L(this,(function(r){return[2,new o((function(r,n){G.index.getProvider({service:e,success:function(e){r(e)},fail:function(e){n(e)}})}))]}))}))}}));
//# sourceMappingURL=commonPay.js.map