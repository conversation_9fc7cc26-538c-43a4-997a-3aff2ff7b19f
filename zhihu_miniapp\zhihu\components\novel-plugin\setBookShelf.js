define("components/novel-plugin/setBookShelf.js",(function(n,e,t,r,o,u,l,a,i,c,s,f,b,d,h,y,p,v,g,w,k,m,x,I,D,S,_,j,B,T,C,E,N,G,H,M,P,q,z,A){"use strict";function F(n,e){var t,r,o,u,l={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return u={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function a(u){return function(a){return function(u){if(t)throw new TypeError("Generator is already executing.");for(;l;)try{if(t=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return l.label++,{value:u[1],done:!1};case 5:l.label++,r=u[1],u=[0];continue;case 7:u=l.ops.pop(),l.trys.pop();continue;default:if(!(o=l.trys,(o=o.length>0&&o[o.length-1])||6!==u[0]&&2!==u[0])){l=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){l.label=u[1];break}if(6===u[0]&&l.label<o[1]){l.label=o[1],o=u;break}if(o&&l.label<o[2]){l.label=o[2],l.ops.push(u);break}o[2]&&l.ops.pop(),l.trys.pop();continue}u=e.call(n,l)}catch(n){u=[6,n],r=0}finally{t=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,a])}}}var J=function(n,e,t){return new o((function(r,u){var l=function(n){try{i(t.next(n))}catch(n){u(n)}},a=function(n){try{i(t.throw(n))}catch(n){u(n)}},i=function(n){return n.done?r(n.value):o.resolve(n.value).then(l,a)};i((t=t.apply(n,e)).next())}))};!function(){try{var n=void 0!==s?s:"undefined"!=typeof global?global:void 0!==d?d:{},e=(new Error).stack;e&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[e]="b161a4a3-f202-42fa-9407-c27d73280e13",n._sentryDebugIdIdentifier="sentry-dbid-b161a4a3-f202-42fa-9407-c27d73280e13")}catch(n){}}();var K=n("../../common/vendor.js"),L=n("../../utils/showToast.js");t.setBookShelfHandle=function(){return J(t,null,(function(){return F(this,(function(n){return K.NovelPlugin.getCurrentNovelManager().onClickBookshelf((function(n){return J(t,null,(function(){return F(this,(function(n){return L.showToast({title:"暂未开放书架，可在浏览历史中查看"}),[2]}))}))})),[2]}))}))}}));
//# sourceMappingURL=setBookShelf.js.map