define("app.js",(function(e,n,t,o,r,i,s,a,u,l,c,p,d,g,h,f,m,v,b,j,y,x,w,P,S,k,_,D,I,E,L,M,T,N,A,C,F,G,z,R){"use strict";function U(e,n){var t,o,r,i,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(t)throw new TypeError("Generator is already executing.");for(;s;)try{if(t=1,o&&(r=2&i[0]?o.return:i[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,i[1])).done)return r;switch(o=0,r&&(i=[2&i[0],r.value]),i[0]){case 0:case 1:r=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,o=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!r||i[1]>r[0]&&i[1]<r[3])){s.label=i[1];break}if(6===i[0]&&s.label<r[1]){s.label=r[1],r=i;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(i);break}r[2]&&s.ops.pop(),s.trys.pop();continue}i=n.call(e,s)}catch(e){i=[6,e],o=0}finally{t=r=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}var V=function(e,n,t){return new r((function(o,i){var s=function(e){try{u(t.next(e))}catch(e){i(e)}},a=function(e){try{u(t.throw(e))}catch(e){i(e)}},u=function(e){return e.done?o(e.value):r.resolve(e.value).then(s,a)};u((t=t.apply(e,n)).next())}))};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==g?g:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="37c31043-ba78-4abe-872c-2daf16b1ff60",e._sentryDebugIdIdentifier="sentry-dbid-37c31043-ba78-4abe-872c-2daf16b1ff60")}catch(e){}}(),Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});var H=e("./common/vendor.js"),O=e("./utils/appInit.js"),$=e("./utils/pageHelper.js");e("./stores/modules/user/index.js"),e("./stores/modules/history/index.js");var q=e("./stores/modules/login/index.js");e("./stores/modules/search-history/index.js"),e("./stores/modules/koc-ascription/index.js"),e("./stores/modules/purchaseInfo/index.js");var B=e("./stores/modules/clientInfo/index.js"),K=e("./stores/modules/systenInfo/index.js"),W=e("./stores/modules/globalVariable/index.js");e("./stores/modules/trackInfo/index.js"),e("./stores/modules/devDebug/index.js");var Z=e("./utils/login.js"),J=e("./components/novel-plugin/novelPlugin.js"),Q=e("./utils/adManager.js"),X=e("./utils/image.js"),Y=e("./utils/utils.js");e("./utils/appFetch.js"),e("./utils/request.js"),e("./writeConstant.js"),e("./utils/encryptHandler.js"),e("./utils/requstErrorMiddlewares.js"),e("./utils/constant.js"),e("./utils/onGetUdid.js"),e("./utils/launchOptionsHelper.js"),e("./prelaunch/tt/pre-fetch-launch-options.js"),e("./pages/manuscript/store.js"),e("./prelaunch/tt/pre-init-info.js"),e("./utils/track.js"),e("./components/novel-plugin/trace.js"),e("./components/novel-plugin/utils.js"),e("./pages/manuscript/fetch.js"),e("./components/novel-plugin/setUnlockMode.js"),e("./utils/payUtils.js"),e("./components/novel-plugin/setBookShelf.js"),e("./utils/showToast.js"),e("./components/novel-plugin/addHistoryList.js"),e("./utils/gift.js"),Math;var ee=null,ne={onLaunch:function(e){return V(this,null,(function(){var n,t;return U(this,(function(o){switch(o.label){case 0:return K.useSystemInfoStore().init(),n=B.useClientStore().initBizClientId(),O.initSentry({bizClientId:n}),X.initWebpSupport(),O.initZaV3Config(e),t=O.getMiniEnvInfo(),H.NovelPlugin.onPageLoad(J.onNovelPluginLoad),[4,this.initData(e)];case 1:return o.sent(),H.NovelPlugin.isValidNovelRoute(e.path)||r.all([t,ee]).then((function(){O.initOneId()})),this.startShakeListener(),[2]}}))}))},onError:function(e){$.AutoGoToErrorPage(e),H.captureException(e,{tags:{source:"onError"}})},onPageNotFound:function(){$.AutoGoToErrorPage(new Error("onPageNotFound")),H.captureException(new Error("onPageNotFound"),{tags:{source:"onPageNotFound"}})},onUnhandledRejection:function(e){$.AutoGoToErrorPage(e),H.captureException(e,{tags:{source:"onUnhandledRejection"}})},globalData:{$onLaunched:!1,adManager:Q.adManager},methods:{startShakeListener:function(){try{if(W.useGlobalVariable().isRelease)return;Y.onAccelerate((function(){var e=getCurrentPages(),n=e[e.length-1];"sub_pages/miniDebugger/miniDebugger"!==(null==n?void 0:n.route)&&H.index.navigateTo({url:"/sub_pages/miniDebugger/miniDebugger"})}))}catch(e){}},initData:function(e){return V(this,null,(function(){var e;return U(this,(function(n){e=q.useLoginStore(),e.clearLoginStoreFlag||(e.setClearLoginStoreFlag(),e.updateAccessToken());try{ee=Z.loginMp(),W.useGlobalVariable().init()}catch(e){}return[2]}))}))}}};try{var te=new H.MiniProgramPerformanceSDK({project:"zhihu-story-mini-app",isDevelopment:!1,realtimeLogType:"weber_pico",matrix_name:"zhihugushi",reportUrl:"https://apm.laiyagushi.com/collector/mnp",samplingRate:1,apiWhitelist:["/dalaran/home/<USER>/list","/dalaran/manuscript/encrypt_section","/dalaran/manuscript/truncate_section","/km-ravenclaw/douyin/zhihu_novel/vip_purchase","/km-ravenclaw/kuaishou/zhihu_novel/vip_purchase","/km-ravenclaw/mini_app/vip_purchase","/order/v1/purchase"]});H.index.$performanceSDK=te}catch(e){}function oe(){var e=H.createSSRApp(ne),n=H.createPinia();return n.use(H.index$1),e.use(n),{app:e,Pinia:H.Pinia}}oe().app.mount("#app"),t.createApp=oe}));
//# sourceMappingURL=app.js.map