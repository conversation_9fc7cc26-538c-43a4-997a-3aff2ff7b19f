define("pages/novel_plugin/hooks.js",(function(e,t,n,r,a,o,i,u,l,c,s,f,v,d,b,p,y,h,g,w,k,x,D,T,I,_,j,m,E,S,B,N,G,L,P,q,z,A,C,F){"use strict";function H(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function u(o){return function(u){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=i.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(e){o=[6,e],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,u])}}}!function(){try{var e=void 0!==s?s:"undefined"!=typeof global?global:void 0!==d?d:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ba2dc9a9-ef0e-4860-8828-ac2ea47beaa0",e._sentryDebugIdIdentifier="sentry-dbid-ba2dc9a9-ef0e-4860-8828-ac2ea47beaa0")}catch(e){}}();var J=e("../../common/vendor.js"),K=e("../../utils/utils.js"),M=e("./fetch.js");n.useSetNavigationBarTitle=function(e){var t="";J.onPageScroll((function(n){0===n.scrollTop?(J.index.setNavigationBarTitle({title:""}),t=""):t!==e.value&&(J.index.setNavigationBarTitle({title:e.value}),t=e.value)}))},n.useTrackDeviceEvent=function(){J.onLoad((function(e){return t=n,r=function(){return H(this,(function(t){switch(t.label){case 0:return t.trys.push([0,3,,4]),[4,K.sleep(5e3)];case 1:return t.sent(),[4,M.trackDeviceEvent(null==e?void 0:e.id)];case 2:return t.sent(),[3,4];case 3:return t.sent(),[3,4];case 4:return[2]}}))},new a((function(e,n){var o=function(e){try{u(r.next(e))}catch(e){n(e)}},i=function(e){try{u(r.throw(e))}catch(e){n(e)}},u=function(t){return t.done?e(t.value):a.resolve(t.value).then(o,i)};u((r=r.apply(t,null)).next())}));var t,r}))}}));
//# sourceMappingURL=hooks.js.map