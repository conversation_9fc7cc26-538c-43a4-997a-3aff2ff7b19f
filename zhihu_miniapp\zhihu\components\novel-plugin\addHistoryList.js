define("components/novel-plugin/addHistoryList.js",(function(e,s,o,n,d,t,r,i,l,a,u,c,b,g,v,y,f,j,m,x,I,_,p,h,k,D,w,H,L,N,P,q,B,C,E,M,O,S,V,z){"use strict";!function(){try{var e=void 0!==u?u:"undefined"!=typeof global?global:void 0!==g?g:{},s=(new Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="592262cc-bce3-42d6-ad90-9ac9ebb459ab",e._sentryDebugIdIdentifier="sentry-dbid-592262cc-bce3-42d6-ad90-9ac9ebb459ab")}catch(e){}}();var A=e("../../common/vendor.js");e("../../stores/modules/user/index.js");var F=e("../../stores/modules/history/index.js");e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js"),e("../../stores/modules/purchaseInfo/index.js"),e("../../stores/modules/clientInfo/index.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js"),e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js"),o.addHistoryList=function(e,s){var o,n,d;if(["start_read","page_hide","page_unload","leave_chapter","expose_introduction"].includes(e.event_id)){var t=A.NovelPlugin.getCurrentNovelManager().getPluginInfo().query,r=t.id,i=t.bookId,l=F.useHistoryStore(),a=e.title,u=e.read_progress,c=u?{progress:u}:{},b=u?{is_finished:u>=.99}:{},g=Object.assign({id:r,wxBookId:i,title:a,content:null!=(o=null==s?void 0:s.content)?o:"",artwork:null!=(n=null==s?void 0:s.artwork)?n:"",labels:null!=(d=null==s?void 0:s.labels)?d:[]},c,b);l.addHistoryList(g)}}}));
//# sourceMappingURL=addHistoryList.js.map