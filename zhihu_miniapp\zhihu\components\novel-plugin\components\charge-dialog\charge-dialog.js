define("components/novel-plugin/components/charge-dialog/charge-dialog.js",(function(e,n,t,r,s,o,i,u,l,a,c,d,p,f,h,y,g,v,j,b,m,x,w,I,k,_,C,D,M,P,S,q,E,H,T,U,V,G,N,F){"use strict";function O(e,n){var t,r,s,o,i={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]};return o={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function u(o){return function(u){return function(o){if(t)throw new TypeError("Generator is already executing.");for(;i;)try{if(t=1,r&&(s=2&o[0]?r.return:o[0]?r.throw||((s=r.return)&&s.call(r),0):r.next)&&!(s=s.call(r,o[1])).done)return s;switch(r=0,s&&(o=[2&o[0],s.value]),o[0]){case 0:case 1:s=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(s=i.trys,(s=s.length>0&&s[s.length-1])||6!==o[0]&&2!==o[0])){i=0;continue}if(3===o[0]&&(!s||o[1]>s[0]&&o[1]<s[3])){i.label=o[1];break}if(6===o[0]&&i.label<s[1]){i.label=s[1],s=o;break}if(s&&i.label<s[2]){i.label=s[2],i.ops.push(o);break}s[2]&&i.ops.pop(),i.trys.pop();continue}o=n.call(e,i)}catch(e){o=[6,e],r=0}finally{t=s=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,u])}}}!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==f?f:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="2995d686-98d8-4ec5-9d23-1d7da5aa60cb",e._sentryDebugIdIdentifier="sentry-dbid-2995d686-98d8-4ec5-9d23-1d7da5aa60cb")}catch(e){}}();var W=e("../../../../common/vendor.js"),z=e("../../../../utils/track.js");e("../../../../stores/modules/user/index.js"),e("../../../../stores/modules/history/index.js"),e("../../../../stores/modules/login/index.js"),e("../../../../stores/modules/search-history/index.js"),e("../../../../stores/modules/koc-ascription/index.js");var A=e("../../../../stores/modules/purchaseInfo/index.js");e("../../../../stores/modules/clientInfo/index.js"),e("../../../../stores/modules/systenInfo/index.js"),e("../../../../stores/modules/globalVariable/index.js"),e("../../../../stores/modules/trackInfo/index.js"),e("../../../../stores/modules/devDebug/index.js");var B=e("../../utils.js");e("../../../../utils/appFetch.js"),e("../../../../utils/request.js"),e("../../../../writeConstant.js"),e("../../../../utils/appInit.js"),e("../../../../utils/onGetUdid.js"),e("../../../../utils/utils.js"),e("../../../../utils/launchOptionsHelper.js"),e("../../../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../../../utils/encryptHandler.js"),e("../../../../utils/requstErrorMiddlewares.js"),e("../../../../utils/pageHelper.js"),e("../../../../utils/constant.js"),e("../../../../pages/manuscript/store.js"),Math||W.unref(J)();var J=function(){return"../novel-pay-card/novel-pay-card.js"},K={__name:"charge-dialog",setup:function(e){var n,t,r=this,o=(null==(t=null==(n=W.NovelPlugin)?void 0:n.getCurrentNovelManager)?void 0:t.call(n)).query,i=null==o?void 0:o.id,u=A.usePurchaseInfo();return W.onMounted((function(){return e=r,n=function(){return O(this,(function(e){return u.getPayCards({ignoreCoupon:1,ignoreSingle:i?0:1,contentId:i}),z.track({elementType:"Card",eventType:"Show",moduleId:"pay_card",message:"会员购买截断卡片曝光",wxViewUrl:B.getWxViewUrlPath(),extra:{sectionId:null==o?void 0:o.id}}),[2]}))},new s((function(t,r){var o=function(e){try{u(n.next(e))}catch(e){r(e)}},i=function(e){try{u(n.throw(e))}catch(e){r(e)}},u=function(e){return e.done?t(e.value):s.resolve(e.value).then(o,i)};u((n=n.apply(e,null)).next())}));var e,n})),function(e,n){return{a:W.p({type:"manuscript","section-id":W.unref(i)})}}}};tt.createComponent(K)}));
//# sourceMappingURL=charge-dialog.js.map