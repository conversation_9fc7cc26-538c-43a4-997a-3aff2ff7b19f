define("utils/requstErrorMiddlewares.js",(function(e,r,t,n,o,u,s,i,a,d,g,c,l,f,p,b,E,v,_,I,y,h,q,P,j,A,w,C,D,L,T,m,x,H,M,N,k,G,R,U){"use strict";!function(){try{var e=void 0!==g?g:"undefined"!=typeof global?global:void 0!==f?f:{},r=(new Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="032f6324-29b9-4e51-9994-8935464202c4",e._sentryDebugIdIdentifier="sentry-dbid-032f6324-29b9-4e51-9994-8935464202c4")}catch(e){}}();var V=e("../common/vendor.js"),W=e("./request.js"),$=e("./pageHelper.js"),z=[function(e){return function(r){return W.printApiLog(r),e(r),o.reject(r)}},function(e){return function(r){401===r.statusCode&&(W.resetUserLoginInfo(),V.captureMessage("http_401",{level:"info",extra:{response:r},tags:{errorId:"http_401"}}),$.AutoGoToErrorPage(new Error("autoLoginError"))),e(r)}},function(e){return function(r){var t,n,o;[40350,40351,40352,40353,40362].includes(null==(n=null==(t=r.data)?void 0:t.error)?void 0:n.code)?(o=getCurrentPages()).length>0&&"sub_pages/requestErrorPage/requestErrorPage"===o[o.length-1].route||(V.index.redirectTo({url:"/sub_pages/requestErrorPage/requestErrorPage?type=antiCrawler"}),V.index.$emit("ANTI_CRAWL_EVENT")):e(r)}}],B=function(){for(var e=function(e){return e},r=z.length;r--;)e=z[r](e);return e}();t.responseInterceptorApiHandle=B}));
//# sourceMappingURL=requstErrorMiddlewares.js.map