import requests

url = "https://api.zhihu.com/search_v3"

params = {
  'gk_version': "gz-gaokao",
  'q': "相依背景",
  't': "general",
  'search_source': "Normal",
  'is_real_time': "0",
  'correction': "1",
  'advert_count': "",
  'show_all_topics': "0",
  'pin_flow': "false",
  'restricted_scene': "",
  'restricted_field': "",
  'restricted_value': "",
  'offset': "0",
  'limit': "20",
  'lc_idx': "0"
}

headers = {
  'User-Agent': "com.zhihu.android/Futureve/10.61.0 Mozilla/5.0 (Linux; Android 10; M2003J15SC Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.1000.10 Mobile Safari/537.36",
  'Accept-Encoding': "br,gzip",
  'x-client-info': "react-native/amadeus-rn/2.295.0",
  'x-api-version': "3.0.91",
  'x-b3-traceid': "1cbe18ec6c6848ada26fd3a4b73c013d",
  'x-client-ri': "3747188415",
  'x-app-version': "10.61.0",
  'x-app-za': "OS=Android&Release=10&Model=M2003J15SC&VersionName=10.61.0&VersionCode=26110&Product=com.zhihu.android&Width=1080&Height=2110&Installer=Market&DeviceType=AndroidPhone&Brand=Redmi",
  'x-app-bundleid': "com.zhihu.android",
  'x-app-flavor': "zhihuwap64",
  'x-app-build': "release",
  'x-network-type': "WiFi",
  'x-zst-82': "2.0oKcTQSRm1BoMAAAASwUAADIuMHV_h2gAAAAAOqEDqp3GE57Ms-SmUZNKcS7GQKk=",
  'x-zst-81': "PKTTM6xg1Bp9N2GlwgMt9_4axXZUuhXyyF9Oow==",
  'x-udid': "UKATuaZg1BpLBUWTwLMrCv4pFBpUPDd0zUA=",
  'authorization': "Bearer 2.1eLqhUwAAAABQoBO5pmDUGgwAAABgAlVNIQevaABOfPFqwUKc9WJJgLvMjKdMs_q2ng",
  'x-ms-id': "DU4s5SXY5o1pLPwikgaikElxX-udbzRtM14fRFU0czVTWFk1bzFwTFB3aWtnYWlrRWx4WC11ZGJ6UnRNMTRmc2h1",
  'x-suger': "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  'x-ad': "canvas_version:v=5.1;setting:cad=0",
  'x-ad': "canvas_version:v=5.1;setting:cad=0",
  'x-zse-96': "1.0_8n8lD2R9esKBIk5wzbteWLjMESXn0mbhRuwPjNvUG1GMpD/PqxCNBX6zSmfEJKxF",
  'x-zse-93': "101_1_1.0",
  'Cookie': "_zap=47e7c0ed-c47e-4397-8620-4e9aaba507e9; BEC=92a0fca0e2e4d1109c446d0a990ad863; _xsrf=24OiusIx75Cn2VovXSX3W4hRoKGAxqAN; z_c0=2|1:0|10:1753709089|4:z_c0|92:Mi4xZUxxaFV3QUFBQUJRb0JPNXBtRFVHZ3dBQUFCZ0FsVk5JUWV2YUFCT2ZQRnF3VUtjOVdKSmdMdk1qS2RNc19xMm5n|179a52fb755f3170ca11ac532b9f335a641401cc89f4852e05f32e671e5b274c; capsion_ticket=2|1:0|10:1753709033|14:capsion_ticket|44:ZGNlYTEyNDBhNDg3NDQ0ODk1NzRiOWY1MmRhNzY2NGQ=|641e1e3a04a5588b4503f73c620715d22646bd19f0afce560ef3a273768e1387"
}

response = requests.get(url, params=params, headers=headers)

print(response.text)