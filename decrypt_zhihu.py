import json
import base64
import binascii
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad


# AES解密密钥和IV（来自shareBookSource文件）
AES_KEY_HEX = "3e6cebec56e4bd95e8ae6ed4bf03c52e"
AES_IV_HEX = "54dee60aac15d992a4293886d5119b0c"


def aes_decrypt_with_fixed_key(encrypted_data_b64, key_hex, iv_hex):
    """
    使用固定的 AES 密钥和 IV 解密 Base64 编码的数据。
    算法: AES/CBC/PKCS7Padding
    """
    try:
        # 1. 解码 Base64
        encrypted_bytes = base64.b64decode(encrypted_data_b64)
        print(f"[DEBUG] Base64 decoded data length: {len(encrypted_bytes)} bytes")

        # 2. 转换十六进制密钥和 IV 为字节
        key_bytes = binascii.unhexlify(key_hex)
        iv_bytes = binascii.unhexlify(iv_hex)
        print(f"[DEBUG] Key length: {len(key_bytes)} bytes")
        print(f"[DEBUG] IV length: {len(iv_bytes)} bytes")

        # 3. 创建 AES 解密器 (CBC 模式)
        cipher = AES.new(key_bytes, AES.MODE_CBC, iv_bytes)

        # 4. 解密
        decrypted_bytes = cipher.decrypt(encrypted_bytes)
        print(f"[DEBUG] Raw decrypted data length: {len(decrypted_bytes)} bytes")

        # 5. 移除 PKCS7 填充
        unpadded_bytes = unpad(decrypted_bytes, AES.block_size, style='pkcs7')
        print(f"[DEBUG] Unpadded data length: {len(unpadded_bytes)} bytes")

        # 6. 转换为字符串
        plaintext = unpadded_bytes.decode('utf-8')
        return plaintext

    except (ValueError, binascii.Error) as e:
        print(f"[ERROR] Decryption failed (likely incorrect key/IV or padding): {e}")
        return None
    except UnicodeDecodeError as e:
        print(f"[ERROR] Failed to decode decrypted bytes to UTF-8 string: {e}")
        return None
    except Exception as e:
        print(f"[ERROR] An unexpected error occurred during decryption: {e}")
        return None


def decrypt_zhihu_data(encrypted_data):
    """
    解密知乎小说的加密数据
    """
    print("[INFO] Starting decryption with fixed key and IV from shareBookSource...")
    decrypted_text = aes_decrypt_with_fixed_key(encrypted_data, AES_KEY_HEX, AES_IV_HEX)
    return decrypted_text


def main():
    # 这里是你要解密的数据，从你的authorization获取
    # 由于你没有提供具体需要解密的数据，我将展示如何使用方法
    
    # 示例用法（你需要替换成实际的加密数据）:
    # encrypted_data = "YOUR_ENCRYPTED_DATA_HERE"
    # decrypted = decrypt_zhihu_data(encrypted_data)
    # 
    # if decrypted:
    #     print("\n[SUCCESS] Decryption successful!")
    #     print("-" * 30)
    #     print(decrypted)
    #     print("-" * 30)
    #     
    #     # 保存解密后的内容到文件
    #     try:
    #         with open('decrypted_result.txt', 'w', encoding='utf-8') as f:
    #             f.write(decrypted)
    #         print("\n[INFO] Decrypted content saved to 'decrypted_result.txt'")
    #     except Exception as e:
    #         print(f"\n[WARNING] Failed to save decrypted content to file: {e}")
    # else:
    #     print("\n[FAILURE] Decryption failed.")
    
    print("解密脚本已准备就绪")
    print("请提供需要解密的Base64编码数据")


if __name__ == "__main__":
    main()