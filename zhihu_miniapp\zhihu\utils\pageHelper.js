define("utils/pageHelper.js",(function(e,r,t,o,n,s,a,i,u,d,l,c,f,g,b,v,y,p,h,m,j,x,P,I,O,E,w,D,S,_,k,L,A,G,U,V,q,B,C,F){"use strict";var H=Object.defineProperty,M=Object.defineProperties,N=Object.getOwnPropertyDescriptors,T=Object.getOwnPropertySymbols,z=Object.prototype.hasOwnProperty,J=Object.prototype.propertyIsEnumerable,K=function(e,r,t){return r in e?H(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t},Q=function(e,r){for(var t in r||(r={}))z.call(r,t)&&K(e,t,r[t]);var o=!0,n=!1,s=void 0;if(T)try{for(var a,i=T(r)[Symbol.iterator]();!(o=(a=i.next()).done);o=!0){t=a.value;J.call(r,t)&&K(e,t,r[t])}}catch(e){n=!0,s=e}finally{try{o||null==i.return||i.return()}finally{if(n)throw s}}return e},R=function(e,r){return M(e,N(r))};!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==g?g:{},r=(new Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="ed066a07-d399-4e59-81d0-74d99bf55e48",e._sentryDebugIdIdentifier="sentry-dbid-ed066a07-d399-4e59-81d0-74d99bf55e48")}catch(e){}}();var W=e("../common/vendor.js");e("../stores/modules/user/index.js"),e("../stores/modules/history/index.js");var X=e("../stores/modules/login/index.js");e("../stores/modules/search-history/index.js"),e("../stores/modules/koc-ascription/index.js"),e("../stores/modules/purchaseInfo/index.js"),e("../stores/modules/clientInfo/index.js"),e("../stores/modules/systenInfo/index.js");var Y=e("../stores/modules/globalVariable/index.js");e("../stores/modules/trackInfo/index.js"),e("../stores/modules/devDebug/index.js");var Z=!1;t.AutoGoToErrorPage=function(e){if(e.message){var r=!0,t=!1,o=void 0;try{for(var n,s=["autoLoginError","onPageNotFound"][Symbol.iterator]();!(r=(n=s.next()).done);r=!0){var a=n.value;if(e.message.includes(a)){getCurrentPages()[0].route.includes("sub_pages/authErrorPage/authErrorPage")||Z||(Y.useGlobalVariable().setPagePathBeforeError(),Z=!0,W.index.reLaunch({url:"/sub_pages/authErrorPage/authErrorPage",complete:function(){Z=!1}}));break}}}catch(e){t=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(t)throw o}}}},t.getAppShareMessage=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=X.useLoginStore().hashId,t=e.path,o=void 0===t?"":t,n=e.query,s=void 0===n?{}:n,a=e.title,i=void 0===a?"":a,u=e.desc,d=void 0===u?"":u,l=e.imageUrl,c=void 0===l?"":l,f=(null==o?void 0:o.split("?")[0])||"/pages/home/<USER>",g={};o.indexOf("?")>=0&&(g=function(e){for(var r={},t=e.substr(e.indexOf("?")+1).split("&"),o=0;o<t.length;o++){var n=t[o].split("=");r[n[0]]=n[1]}return r}(o));var b=R(Q(Q({},g),s),{shareId:r,isShare:!0}),v=Object.keys(b).map((function(e){return"".concat(e,"=").concat(b[e])})).join("&");return R(Q(Q({path:"".concat(f,"?").concat(v)},i&&{title:i}),d&&{desc:d}),{imageUrl:c||""})}}));
//# sourceMappingURL=pageHelper.js.map