define("components/novel-plugin/novelPluginJump.js",(function(r,t,n,e,o,l,u,i,a,c,s,f,g,p,d,y,b,v,m,h,I,j,w,P,A,S,D,N,_,E,x,k,C,J,O,U,M,T,$,q){"use strict";function z(r,t){(null==t||t>r.length)&&(t=r.length);for(var n=0,e=new Array(t);n<t;n++)e[n]=r[n];return e}function B(r,t){return function(r){if(Array.isArray(r))return r}(r)||function(r,t){var n=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=n){var e,o,l=[],u=!0,i=!1;try{for(n=n.call(r);!(u=(e=n.next()).done)&&(l.push(e.value),!t||l.length!==t);u=!0);}catch(r){i=!0,o=r}finally{try{u||null==n.return||n.return()}finally{if(i)throw o}}return l}}(r,t)||function(r,t){if(!r)return;if("string"==typeof r)return z(r,t);var n=Object.prototype.toString.call(r).slice(8,-1);"Object"===n&&r.constructor&&(n=r.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return z(r,t)}(r,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}!function(){try{var r=void 0!==s?s:"undefined"!=typeof global?global:void 0!==p?p:{},t=(new Error).stack;t&&(r._sentryDebugIds=r._sentryDebugIds||{},r._sentryDebugIds[t]="925f8c2b-5220-4c85-83cb-755a8aa14523",r._sentryDebugIdIdentifier="sentry-dbid-925f8c2b-5220-4c85-83cb-755a8aa14523")}catch(r){}}();var F=r("../../common/vendor.js"),G=r("../../utils/utils.js");n.NovelPluginJump=function(r){if(!r.url)throw new Error("url 缺失");var t=!1,n=B(r.url.split("?"),2),e=n[0],o=n[1],l=F.NovelPlugin.getNovelPluginUrlPath();o.split("&").forEach((function(r){var n=B(r.split("="),2),e=n[0],o=n[1];"bookId"===e&&o&&(t=!0)})),(/pages\/manuscript\/manuscript/.test(e)||/pages\/novel_plugin\/index/.test(e))&&l&&t?F.NovelPlugin.jumpNovelPlugin({url:r.url+"&disableAutoShowChargeDialog=1&paragraphIndex=1"}):G.jumpPage(r)}}));
//# sourceMappingURL=novelPluginJump.js.map