define("stores/modules/systenInfo/index.js",(function(e,s,t,n,o,i,r,a,c,d,f,h,m,u,b,y,g,I,v,l,N,S,W,p,D,_,x,H,V,j,k,w,C,E,q,z,A,B,F,G){"use strict";!function(){try{var e=void 0!==f?f:"undefined"!=typeof global?global:void 0!==u?u:{},s=(new Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="a300e359-8fb9-4cd1-9731-051207eb9b9c",e._sentryDebugIdIdentifier="sentry-dbid-a300e359-8fb9-4cd1-9731-051207eb9b9c")}catch(e){}}();var J=e("../../../common/vendor.js"),K=<PERSON>.defineStore("systemInfo",{state:function(){return{screenHeight:0,screenWidth:0,hostName:"",platform:"",osName:"",osVersion:"",version:""}},getters:{isIos:function(e){return"ios"===e.osName},isWx:function(e){return"WeChat"===e.hostName}},actions:{init:function(){var e=J.index.getSystemInfoSync(),s=e.screenHeight,t=e.hostName,n=e.platform,o=e.screenWidth,i=e.osName,r=e.osVersion,a=e.version;this.screenHeight=s,this.screenWidth=o,this.hostName=t,this.platform=n,this.osVersion=r,this.version=a,this.osName=i}}});t.useSystemInfoStore=K}));
//# sourceMappingURL=index.js.map