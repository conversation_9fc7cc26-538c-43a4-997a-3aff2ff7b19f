define("components/novel-plugin/novelPlugin.js",(function(e,t,n,r,o,i,a,l,s,u,c,d,f,p,g,y,b,v,h,m,P,S,_,w,j,I,x,O,C,k,E,D,M,T,U,A,B,H,L,N){"use strict";function V(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function l(i){return function(l){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,l])}}}var Y=Object.defineProperty,$=Object.defineProperties,q=Object.getOwnPropertyDescriptors,z=Object.getOwnPropertySymbols,F=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable,R=function(e,t,n){return t in e?Y(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},Z=function(e,t){for(var n in t||(t={}))F.call(t,n)&&R(e,n,t[n]);var r=!0,o=!1,i=void 0;if(z)try{for(var a,l=z(t)[Symbol.iterator]();!(r=(a=l.next()).done);r=!0){n=a.value;G.call(t,n)&&R(e,n,t[n])}}catch(e){o=!0,i=e}finally{try{r||null==l.return||l.return()}finally{if(o)throw i}}return e},J=function(e,t){return $(e,q(t))};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==p?p:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d116fad0-0bd4-477f-9a4a-d8dee3f727c2",e._sentryDebugIdIdentifier="sentry-dbid-d116fad0-0bd4-477f-9a4a-d8dee3f727c2")}catch(e){}}();var K=e("../../common/vendor.js"),Q=e("./trace.js"),W=e("../../pages/manuscript/fetch.js"),X=e("./setUnlockMode.js"),ee=e("./setBookShelf.js"),te=e("./addHistoryList.js"),ne=e("../../utils/constant.js"),re=e("../../utils/utils.js"),oe=e("../../utils/appInit.js"),ie=e("../../pages/manuscript/store.js"),ae=e("../../utils/login.js");n.onNovelPluginLoad=function(e){return t=n,r=function(){var t,n,r,o,i,a,l,s,u;return V(this,(function(c){switch(c.label){case 0:return[4,ae.loginMp()];case 1:return c.sent(),t=ie.useManuscriptStore(),re.isFunction(K.index.setVisualEffectOnCapture)&&K.index.setVisualEffectOnCapture({visualEffect:"hidden"}),(n=K.NovelPlugin.getNovelManager(e.id)).setClosePluginInfo({url:"/pages/home/<USER>",mode:"switchTab"}),r=n.getPluginInfo(),o=r.query,i=o.id,a=o.zxh_adid,l=o.content_type,a&&(t.setAscribeZxhAdid(a),t.setAscribeSectionId(i)),t.setLastSectionId(i),oe.initOneId(o),[4,W.getManuscript({contentId:i,contentType:l})];case 2:return s=c.sent(),u=s.manuscriptInfo,Q.novelPluginPageShowTrace(J(Z({},u),{sectionId:i})),ee.setBookShelfHandle(),n.setShareParams({title:null==u?void 0:u.title,imageUrl:null==u?void 0:u.artwork,args:{member_id:"",share_time:""}}),X.setUnlockModeHandle(),K.index.$on(ne.PAY_SUCCESS_CB,(function(e){e.paySuccess&&n.paymentCompleted()})),n.onUserTriggerEvent((function(e){"start_read"===e.event_id&&(n.closeChargeDialog(),Q.novelPluginStartPolling({sectionId:i})),"page_unload"===e.event_id&&Q.novelPluginStopPolling(),"get_chapter"===e.event_id&&Q.novelPluginResponseTrace(J(Z({},u),{sectionId:i})),te.addHistoryList(e,u),"page_unload"===e.event_id&&K.index.$off(ne.PAY_SUCCESS_CB)})),[2]}}))},new o((function(e,n){var i=function(e){try{l(r.next(e))}catch(e){n(e)}},a=function(e){try{l(r.throw(e))}catch(e){n(e)}},l=function(t){return t.done?e(t.value):o.resolve(t.value).then(i,a)};l((r=r.apply(t,null)).next())}));var t,r}}));
//# sourceMappingURL=novelPlugin.js.map