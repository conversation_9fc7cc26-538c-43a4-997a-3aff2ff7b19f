define("utils/login.js",(function(e,n,s,r,t,o,a,c,i,u,l,d,f,p,h,g,b,y,m,x,v,k,T,I,w,j,L,_,A,E,C,D,P,S,M,U,R,V,q,z){"use strict";function F(e,n){var s,r,t,o,a={label:0,sent:function(){if(1&t[0])throw t[1];return t[1]},trys:[],ops:[]};return o={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function c(o){return function(c){return function(o){if(s)throw new TypeError("Generator is already executing.");for(;a;)try{if(s=1,r&&(t=2&o[0]?r.return:o[0]?r.throw||((t=r.return)&&t.call(r),0):r.next)&&!(t=t.call(r,o[1])).done)return t;switch(r=0,t&&(o=[2&o[0],t.value]),o[0]){case 0:case 1:t=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(t=a.trys,(t=t.length>0&&t[t.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!t||o[1]>t[0]&&o[1]<t[3])){a.label=o[1];break}if(6===o[0]&&a.label<t[1]){a.label=t[1],t=o;break}if(t&&a.label<t[2]){a.label=t[2],a.ops.push(o);break}t[2]&&a.ops.pop(),a.trys.pop();continue}o=n.call(e,a)}catch(e){o=[6,e],r=0}finally{s=t=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,c])}}}var G=function(e,n,s){return new t((function(r,o){var a=function(e){try{i(s.next(e))}catch(e){o(e)}},c=function(e){try{i(s.throw(e))}catch(e){o(e)}},i=function(e){return e.done?r(e.value):t.resolve(e.value).then(a,c)};i((s=s.apply(e,n)).next())}))};!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==p?p:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="d784a2ce-4a77-48fc-afd1-aa43cf2d9ec4",e._sentryDebugIdIdentifier="sentry-dbid-d784a2ce-4a77-48fc-afd1-aa43cf2d9ec4")}catch(e){}}();var H=e("../common/vendor.js");e("../stores/modules/user/index.js"),e("../stores/modules/history/index.js");var O=e("../stores/modules/login/index.js");e("../stores/modules/search-history/index.js"),e("../stores/modules/koc-ascription/index.js"),e("../stores/modules/purchaseInfo/index.js"),e("../stores/modules/clientInfo/index.js"),e("../stores/modules/systenInfo/index.js"),e("../stores/modules/globalVariable/index.js"),e("../stores/modules/trackInfo/index.js"),e("../stores/modules/devDebug/index.js");var B=e("./utils.js"),J=e("../prelaunch/tt/pre-init-info.js"),K=e("./appFetch.js"),N=e("./track.js"),Q=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"app";return G(s,null,(function(){var n,r,o,a,c,i,u,l,d,f,p,h,g,b;return F(this,(function(y){switch(y.label){case 0:n=!1,r=O.useLoginStore(),y.label=1;case 1:return y.trys.push([1,11,,12]),"app"!==e?[3,3]:[4,J.getLoginCodeAsync()];case 2:return a=y.sent(),[3,4];case 3:a=null,y.label=4;case 4:return c="",(o=a)?(r.clearAccessToken(),n=!0,c=o,[3,8]):[3,5];case 5:return i=r.accessToken,u=r.expiresTime,l=r.hashId,!B.isTokenExpired(i,u)&&l?[3,7]:(r.clearAccessToken(),[4,new t((function(e,n){H.index.login({force:!0,success:function(s){if("boolean"==typeof s.isLogin&&!s.isLogin||!s.code)return n(new Error("app is not login"));e(s.code)},fail:function(e){n(e)}})}))]);case 6:c=y.sent(),y.label=7;case 7:y.label=8;case 8:return c?[4,function(e){return G(s,null,(function(){var n,s,r;return F(this,(function(t){switch(t.label){case 0:return[4,K.getAuthTokenLogin(e)];case 1:return n=t.sent(),s=n.data,[2,((null==(r=n.baseResp)?void 0:r.code)&&(null==r?void 0:r.msg)&&N.track({eventType:"Unknown",moduleId:"get_phone_number_err",message:"静默登录失败",extra:{errorInfo:r,loginCode:e}}),s)]}}))}))}(c)]:[3,10];case 9:d=y.sent(),r.updateAccessToken(d),H.setOneIdRequestConfig({accessToken:r.accessToken}),y.label=10;case 10:return H.zaV3.config({memberHashId:r.hashId}),[3,12];case 11:return f=y.sent(),p=r.accessToken,h=r.expiresTime,g=r.hashId,b={usePreLaunchCode:n,errorInfo:f,source:e,expiresTime:h,hashId:g,hasAccessToken:p?"true":"false"},N.track({elementType:"Page",eventType:"Unknown",message:"登录失败",extra:b}),H.captureException(f,{tags:{errorId:"login_mp"},extra:{usePreLaunchCode:n,source:e}}),[3,12];case 12:return[2]}}))}))};s.isLoginMp=function(e){return G(s,null,(function(){var n,s,r,t,o,a;return F(this,(function(c){switch(c.label){case 0:return n=O.useLoginStore(),s=n.accessToken,r=n.expiresTime,t=n.hashId,!(o=B.isTokenExpired(s,r))&&t&&s?[3,2]:[4,Q("isLoginMp")];case 1:return c.sent(),a={expiresTime:r,hashId:t,hasAccessToken:s?"true":"false",isExpired:o?"true":"false"},[2,(N.track({elementType:"Page",eventType:"Unknown",message:"重新触发登录",extra:a}),"function"==typeof e&&e(),!1)];case 2:return[2,!0]}}))}))},s.loginMp=Q}));
//# sourceMappingURL=login.js.map