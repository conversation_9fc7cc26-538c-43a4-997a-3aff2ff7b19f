define("components/lead-generation/lead-generation.js",(function(e,t,n,r,o,u,s,a,l,c,i,d,f,p,b,y,v,h,m,j,g,w,x,k,T,C,D,I,E,O,_,S,A,F,U,L,H,P,q,B){"use strict";function G(e,t){var n,r,o,u,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return u={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function a(u){return function(a){return function(u){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&u[0]?r.return:u[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,u[1])).done)return o;switch(r=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return s.label++,{value:u[1],done:!1};case 5:s.label++,r=u[1],u=[0];continue;case 7:u=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==u[0]&&2!==u[0])){s=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){s.label=u[1];break}if(6===u[0]&&s.label<o[1]){s.label=o[1],o=u;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(u);break}o[2]&&s.ops.pop(),s.trys.pop();continue}u=t.call(e,s)}catch(e){u=[6,e],r=0}finally{n=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,a])}}}var M=Object.defineProperty,V=Object.getOwnPropertySymbols,z=Object.prototype.hasOwnProperty,J=Object.prototype.propertyIsEnumerable,K=function(e,t,n){return t in e?M(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},N=function(e,t){for(var n in t||(t={}))z.call(t,n)&&K(e,n,t[n]);var r=!0,o=!1,u=void 0;if(V)try{for(var s,a=V(t)[Symbol.iterator]();!(r=(s=a.next()).done);r=!0){n=s.value;J.call(t,n)&&K(e,n,t[n])}}catch(e){o=!0,u=e}finally{try{r||null==a.return||a.return()}finally{if(o)throw u}}return e};!function(){try{var e=void 0!==i?i:"undefined"!=typeof global?global:void 0!==p?p:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="e7a2a744-3c13-4533-be2b-bbc2d66e9b1f",e._sentryDebugIdIdentifier="sentry-dbid-e7a2a744-3c13-4533-be2b-bbc2d66e9b1f")}catch(e){}}();var Q=e("../../common/vendor.js"),R=e("../../utils/track.js"),W=e("./utils.js");e("../../stores/modules/user/index.js"),e("../../utils/appFetch.js"),e("../../utils/request.js"),e("../../stores/modules/history/index.js"),e("../../stores/modules/login/index.js"),e("../../utils/constant.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js"),e("../../stores/modules/purchaseInfo/index.js"),e("../../pages/manuscript/store.js"),e("../../stores/modules/clientInfo/index.js"),e("../../utils/utils.js"),e("../../writeConstant.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js"),e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js"),e("../../utils/appInit.js"),e("../../utils/onGetUdid.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),e("../../utils/pageHelper.js"),Array||Q.resolveComponent("view-log")(),Math;var X={__name:"lead-generation",props:{content:{type:Object,default:function(){return W.DEFAULT.content}},theme:{type:Object,default:function(){return W.DEFAULT.theme}},behavior:{type:Object,default:function(){return W.DEFAULT.behavior}}},emits:["copySuccess","action"],setup:function(e,t){var n=this,r=t.emit,u=e,s=Q.computed((function(){return W.mergeConfig(u.content,W.DEFAULT.content)})),a=Q.computed((function(){return W.mergeConfig(u.theme,W.DEFAULT.theme)})),l=Q.computed((function(){return W.mergeConfig(u.behavior,W.DEFAULT.behavior)})),c=Q.computed((function(){return s.value.code})),i=Q.computed((function(){return l.value.bpConfig})),d=Q.computed((function(){return{"--lead-background-color":a.value.background,"--lead-text-color":a.value.textColor,"--lead-button-background-color":a.value.buttonColor,"--lead-button-text-color":a.value.buttonTextColor}})),f=function(){return e=n,t=function(){return G(this,(function(e){switch(e.label){case 0:if(!c.value)return[3,6];e.label=1;case 1:return e.trys.push([1,5,,6]),"copy"!==l.value.type?[3,3]:[4,p(c.value)];case 2:return e.sent(),[3,4];case 3:r("action",{code:c.value,type:l.value.type}),e.label=4;case 4:return b(c.value),[3,6];case 5:return e.sent(),Q.index.showToast({icon:"none",title:"复制失败，请重试"}),[3,6];case 6:return[2]}}))},new o((function(n,r){var u=function(e){try{a(t.next(e))}catch(e){r(e)}},s=function(e){try{a(t.throw(e))}catch(e){r(e)}},a=function(e){return e.done?n(e.value):o.resolve(e.value).then(u,s)};a((t=t.apply(e,null)).next())}));var e,t},p=function(e){return new o((function(t,n){Q.index.setClipboardData({data:e,success:function(){Q.index.showToast({title:s.value.successTips||"复制成功",icon:"none"}),r("copySuccess",e),t()},fail:n})}))},b=function(e){var t=i.value("click",{data:e});t&&R.track(N({elementType:"Block",eventType:"Click"},t))},y=function(){var e=i.value("show");e&&R.track(N({elementType:"Block",eventType:"Show"},e))};return function(e,t){return Q.e({a:Q.unref(s).showShadow},(Q.unref(s).showShadow,{}),{b:Q.t(Q.unref(s).guideText),c:Q.t(Q.unref(s).buttonText),d:Q.o(f),e:Q.s(Q.unref(d)),f:Q.o(y)})}}},Y=Q._export_sfc(X,[["__scopeId","data-v-4de914e8"]]);tt.createComponent(Y)}));
//# sourceMappingURL=lead-generation.js.map