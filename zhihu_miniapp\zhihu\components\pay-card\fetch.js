define("components/pay-card/fetch.js",(function(e,t,r,n,a,o,u,i,l,c,s,f,p,d,y,b,h,v,w,g,I,m,j,O,x,P,k,D,_,S,A,E,q,C,G,M,N,R,T,z){"use strict";function B(e,t){var r,n,a,o,u={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function i(o){return function(i){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;u;)try{if(r=1,n&&(a=2&o[0]?n.return:o[0]?n.throw||((a=n.return)&&a.call(n),0):n.next)&&!(a=a.call(n,o[1])).done)return a;switch(n=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,n=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(!(a=u.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){u=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){u.label=o[1];break}if(6===o[0]&&u.label<a[1]){u.label=a[1],a=o;break}if(a&&u.label<a[2]){u.label=a[2],u.ops.push(o);break}a[2]&&u.ops.pop(),u.trys.pop();continue}o=t.call(e,u)}catch(e){o=[6,e],n=0}finally{r=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,i])}}}var F=Object.defineProperty,H=Object.getOwnPropertySymbols,J=Object.prototype.hasOwnProperty,K=Object.prototype.propertyIsEnumerable,L=function(e,t,r){return t in e?F(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r},Q=function(e,t){for(var r in t||(t={}))J.call(t,r)&&L(e,r,t[r]);var n=!0,a=!1,o=void 0;if(H)try{for(var u,i=H(t)[Symbol.iterator]();!(n=(u=i.next()).done);n=!0){r=u.value;K.call(t,r)&&L(e,r,t[r])}}catch(e){a=!0,o=e}finally{try{n||null==i.return||i.return()}finally{if(a)throw o}}return e},U=function(e,t,r){return new a((function(n,o){var u=function(e){try{l(r.next(e))}catch(e){o(e)}},i=function(e){try{l(r.throw(e))}catch(e){o(e)}},l=function(e){return e.done?n(e.value):a.resolve(e.value).then(u,i)};l((r=r.apply(e,t)).next())}))};!function(){try{var e=void 0!==s?s:"undefined"!=typeof global?global:void 0!==d?d:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="03406aa9-4001-43dd-a5df-12489d7e87fb",e._sentryDebugIdIdentifier="sentry-dbid-03406aa9-4001-43dd-a5df-12489d7e87fb")}catch(e){}}();var V=e("../../utils/request.js"),W=e("../../writeConstant.js");r.getOrderInfoApi=function(e){return U(r,null,(function(){var t;return B(this,(function(r){switch(r.label){case 0:return t={"payment-app-id":W.MINI_APPID},[4,V.api.post("/order/v1/purchase",e,{header:Q({},t)})];case 1:return[2,r.sent()]}}))}))},r.getPayResultApi=function(e){return U(r,null,(function(){return B(this,(function(t){switch(t.label){case 0:return[4,V.api.get("/order/v1/delivery/status",{params:e})];case 1:return[2,t.sent()]}}))}))}}));
//# sourceMappingURL=fetch.js.map