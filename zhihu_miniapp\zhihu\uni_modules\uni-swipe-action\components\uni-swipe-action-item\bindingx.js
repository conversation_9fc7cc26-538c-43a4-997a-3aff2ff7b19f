define("uni_modules/uni-swipe-action/components/uni-swipe-action-item/bindingx.js",(function(e,n,i,d,t,s,a,c,o,f,r,u,b,g,y,I,l,_,p,D,m,v,w,x,h,j,k,E,M,X,q,z,A,B,C,F,G,H,J,K){"use strict";!function(){try{var e=void 0!==r?r:"undefined"!=typeof global?global:void 0!==g?g:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="553fe4cf-fd4c-497c-aa2a-2e7eed60a4e9",e._sentryDebugIdIdentifier="sentry-dbid-553fe4cf-fd4c-497c-aa2a-2e7eed60a4e9")}catch(e){}}(),i.bindIngXMixins={}}));
//# sourceMappingURL=bindingx.js.map