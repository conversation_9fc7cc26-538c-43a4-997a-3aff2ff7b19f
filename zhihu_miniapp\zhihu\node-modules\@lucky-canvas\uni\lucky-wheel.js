define("node-modules/@lucky-canvas/uni/lucky-wheel.js",(function(t,n,e,r,i,o,a,u,c,l,s,f,y,h,d,p,b,m,g,v,w,k,x,S,L,I,A,j,O,C,P,W,H,_,z,B,R,T,$,D){"use strict";function U(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function E(t){return function(t){if(Array.isArray(t))return U(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(!t)return;if("string"==typeof t)return U(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return U(t,n)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(t,n){var e,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function u(o){return function(u){return function(o){if(e)throw new TypeError("Generator is already executing.");for(;a;)try{if(e=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=n.call(t,a)}catch(t){o=[6,t],r=0}finally{e=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,u])}}}var F=Object.defineProperty,q=Object.defineProperties,G=Object.getOwnPropertyDescriptors,Q=Object.getOwnPropertySymbols,X=Object.prototype.hasOwnProperty,J=Object.prototype.propertyIsEnumerable,K=function(t,n,e){return n in t?F(t,n,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[n]=e};!function(){try{var t=void 0!==s?s:"undefined"!=typeof global?global:void 0!==h?h:{},n=(new Error).stack;n&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[n]="669be275-c019-4f46-aa29-8f6818cabf26",t._sentryDebugIdIdentifier="sentry-dbid-669be275-c019-4f46-aa29-8f6818cabf26")}catch(t){}}();var N,V=t("../../../common/vendor.js"),Y=V.version.startsWith("3"),Z={name:"lucky-wheel",data:function(){return{imgSrc:"",myLucky:null,canvas:null,isShow:!1,boxWidth:100,boxHeight:100,btnWidth:0,btnHeight:0,dpr:1}},props:{width:{type:String,default:"600rpx"},height:{type:String,default:"600rpx"},blocks:{type:Array,default:function(){return[]}},prizes:{type:Array,default:function(){return[]}},buttons:{type:Array,default:function(){return[]}},defaultConfig:{type:Object,default:function(){return{}}},defaultStyle:{type:Object,default:function(){return{}}}},mounted:function(){this.initLucky()},watch:{blocks:function(t){this.myLucky&&(this.myLucky.blocks=t)},prizes:function(t){this.myLucky&&(this.myLucky.prizes=t)},buttons:function(t){this.myLucky&&(this.myLucky.buttons=t)},defaultStyle:function(t){this.myLucky&&(this.myLucky.defaultStyle=t)},defaultConfig:function(t){this.myLucky&&(this.myLucky.defaultConfig=t)}},methods:{imgBindload:function(t,n,e,r){return o=this,a=function(){var t;return M(this,(function(i){return t=this[n][e].imgs[r],V.resolveImage(t,N),[2]}))},new i((function(t,n){var e=function(t){try{u(a.next(t))}catch(t){n(t)}},r=function(t){try{u(a.throw(t))}catch(t){n(t)}},u=function(n){return n.done?t(n.value):i.resolve(n.value).then(e,r)};u((a=a.apply(o,null)).next())}));var o,a},getImage:function(){return V.getImage.call(this,"lucky-wheel",N)},hideCanvas:function(){var t=this;this.getImage().then((function(n){t.imgSrc=n.tempFilePath}))},initLucky:function(){var t=this;this.boxWidth=V.changeUnits(this.width),this.boxHeight=V.changeUnits(this.height),this.isShow=!0,this.$nextTick((function(){o((function(){t.draw()}))}))},draw:function(){var t=this,n=this;V.index.createSelectorQuery().in(this).select("#lucky-wheel").fields({node:!0,size:!0}).exec((function(e){var r=t;if(e[0]&&e[0].node){var i=e[0],l=i.node,s=i.width,f=i.height,y=N=l,h=t.ctx=y.getContext("2d"),d=t.dpr=V.index.getSystemInfoSync().pixelRatio;y.width=s*d,y.height=f*d,h.scale(d,d);var p,b=Math.min(s,f)/2,m=new V.T({flag:"MP-WX",ctx:h,dpr:d,setTimeout:o,clearTimeout:a,setInterval:u,clearInterval:c,unitFunc:function(t,n){return V.changeUnits(t+n)},beforeCreate:function(){h.translate(b,b)},beforeResize:function(){h.translate(-b,-b)},afterInit:function(){n.btnWidth=2*this.maxBtnRadius,n.btnHeight=2*this.maxBtnRadius,n.$forceUpdate()},afterStart:function(){t.imgSrc=""}},(p=function(t,n){for(var e in n||(n={}))X.call(n,e)&&K(t,e,n[e]);var r=!0,i=!1,o=void 0;if(Q)try{for(var a,u=Q(n)[Symbol.iterator]();!(r=(a=u.next()).done);r=!0){e=a.value;J.call(n,e)&&K(t,e,n[e])}}catch(t){i=!0,o=t}finally{try{r||null==u.return||u.return()}finally{if(i)throw o}}return t}({},t.$props),q(p,G({width:s,height:f,start:function(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];r.$emit.apply(r,["start"].concat(E(n)))},end:function(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];r.$emit.apply(r,["end"].concat(E(n))),r.hideCanvas()}}))));Y&&"function"==typeof V.shallowReactive?t.myLucky=V.shallowReactive(m):t.myLucky=m}}))},toPlay:function(t){this.myLucky.startCallback()},init:function(){this.myLucky.init()},play:function(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];var r;(r=this.myLucky).play.apply(r,E(n))},stop:function(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];var r;(r=this.myLucky).stop.apply(r,E(n))}}},nt=V._export_sfc(Z,[["render",function(t,n,e,r,i,o){var a;return V.e({a:i.isShow},i.isShow?V.e({b:i.boxWidth+"px",c:i.boxHeight+"px",d:i.imgSrc},i.imgSrc?{e:i.imgSrc,f:V.o((function(t){return i.myLucky.clearCanvas()})),g:i.boxWidth+"px",h:i.boxHeight+"px"}:{},{i:V.o((function(){for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];return o.toPlay&&(a=o).toPlay.apply(a,E(n))})),j:i.btnWidth+"px",k:i.btnHeight+"px",l:i.myLucky},i.myLucky?{m:V.f(e.blocks,(function(t,n,e){return V.e({a:t.imgs},t.imgs?{b:V.f(t.imgs,(function(t,e,r){return{a:e,b:t.src,c:V.o((function(t){return o.imgBindload(t,"blocks",n,e)}))}}))}:{},{c:n})})),n:V.f(e.prizes,(function(t,n,e){return V.e({a:t.imgs},t.imgs?{b:V.f(t.imgs,(function(t,e,r){return{a:e,b:t.src,c:V.o((function(t){return o.imgBindload(t,"prizes",n,e)}))}}))}:{},{c:n})})),o:V.f(e.buttons,(function(t,n,e){return V.e({a:t.imgs},t.imgs?{b:V.f(t.imgs,(function(t,e,r){return{a:e,b:t.src,c:V.o((function(t){return o.imgBindload(t,"buttons",n,e)}))}}))}:{},{c:n})}))}:{},{p:i.boxWidth+"px",q:i.boxHeight+"px"}):{})}],["__scopeId","data-v-54e5c56b"]]);tt.createComponent(nt)}));
//# sourceMappingURL=lucky-wheel.js.map