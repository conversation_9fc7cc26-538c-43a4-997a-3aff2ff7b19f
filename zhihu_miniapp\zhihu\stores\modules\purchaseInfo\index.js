define("stores/modules/purchaseInfo/index.js",(function(e,t,n,i,r,o,s,a,c,u,l,p,d,h,f,y,v,m,b,g,S,T,x,w,P,I,k,C,D,A,_,j,R,K,O,U,F,G,W,z){"use strict";function L(e,t){var n,i,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,i&&(r=2&o[0]?i.return:o[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,i=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}var N=Object.defineProperty,q=Object.getOwnPropertySymbols,E=Object.prototype.hasOwnProperty,M=Object.prototype.propertyIsEnumerable,V=function(e,t,n){return t in e?N(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},Z=function(e,t){for(var n in t||(t={}))E.call(t,n)&&V(e,n,t[n]);var i=!0,r=!1,o=void 0;if(q)try{for(var s,a=q(t)[Symbol.iterator]();!(i=(s=a.next()).done);i=!0){n=s.value;M.call(t,n)&&V(e,n,t[n])}}catch(e){r=!0,o=e}finally{try{i||null==a.return||a.return()}finally{if(r)throw o}}return e},B=function(e,t,n){return new r((function(i,o){var s=function(e){try{c(n.next(e))}catch(e){o(e)}},a=function(e){try{c(n.throw(e))}catch(e){o(e)}},c=function(e){return e.done?i(e.value):r.resolve(e.value).then(s,a)};c((n=n.apply(e,t)).next())}))};!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==h?h:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="66a732ee-cce4-43c4-aa52-e8d4fcf4d3ae",e._sentryDebugIdIdentifier="sentry-dbid-66a732ee-cce4-43c4-aa52-e8d4fcf4d3ae")}catch(e){}}();var H=e("../../../common/vendor.js"),J=e("../../../utils/appFetch.js"),Q=e("../../../pages/manuscript/store.js"),X=["ios","macos"],Y={0:"switch",1:"tile"},$=H.defineStore("purchaseInfo",{state:function(){return{memberInfo:{expireText:"",vipType:"guest",name:"",avatarUrl:""},expireText:"",vipType:"guest",name:"",avatarUrl:"",skuList:[],appletPackage:[],panelStyle:"",manuscriptPanelStyle:"",couponText:"",serverNowTime:0,promotionDescFirst:"",promotionDescSecond:"",couponAnnualCardInfo:null,dyAnnualCardSkuId:"1804247724238974977",remindText:"",prompt:"",discount:0,isGetPurchase:!1,isPaymentPending:!1,clickedSkuId:"",iosPrompt:"",iosTruncateText:"",activityKey:"",needShowSubscribeConfirm:!1,badRequestCb:function(){},isReceivedWelfare:!1,welfareType:"",prizeSkuId:""}},getters:{},actions:{getPayCards:function(){return B(this,arguments,(function(){var e,t,n,i,r,o,s=arguments;return L(this,(function(a){switch(a.label){case 0:return t=(e=s.length>0&&void 0!==s[0]?s[0]:{}).ignoreCoupon,n=void 0===t?1:t,i=e.ignoreSingle,r=void 0===i?1:i,o=e.contentId,[4,this.getPayCardsAllowed({ignoreCoupon:n,ignoreSingle:r,contentId:o})];case 1:return a.sent(),[2]}}))}))},getPayCardsAllowed:function(){return B(this,arguments,(function(){var e,t,n,i,r,o,s,a,c,u,l,p,d,h,f,y,v,m,b,g,S,T,x,w,P,I,k,C,D,A,_,j,R=arguments;return L(this,(function(K){switch(K.label){case 0:e=this,n=(t=R.length>0&&void 0!==R[0]?R[0]:{}).ignoreCoupon,i=void 0===n?1:n,r=t.ignoreSingle,o=void 0===r?1:r,s=t.contentId,a=Q.useManuscriptStore(),c=a.ascribeZxhAdid,u=Z({ignore_coupon:i,ignore_single:o,zxh_adid:c,content_id:s},this.activityKey&&{activity_key:this.activityKey}),K.label=1;case 1:return K.trys.push([1,3,4,5]),[4,J.getVipCardsApi(u)];case 2:return l=K.sent(),d=(p=l||{}).header,h=void 0===d?{}:d,f=p.payment,y=void 0===f?{}:f,v=p.couponText,m=p.now,b=p.prompt,g=p.promotionDescFirst,S=p.promotionDescSecond,T=p.discount,x=p.iosPrompt,w=p.iosTruncateText,P=p.remindText,I=p.panelStyle,k=p.manuscriptPanelStyle,this.memberInfo=Z({},h),D=(C=h||{}).expireText,A=C.vipType,_=C.name,j=C.avatarUrl,this.expireText=D,this.vipType=A,this.name=_,this.avatarUrl=j,this.skuList=y.package||[],this.panelStyle=Y[I]||"tile",this.manuscriptPanelStyle=Y[k]||"tile",this.appletPackage=y.appletPackage||[],this.serverNowTime=m,this.couponText=v,this.promotionDescFirst=g,this.promotionDescSecond=S,this.discount=T,this.remindText=P,this.prompt=b,this.iosPrompt=x||"",this.iosTruncateText=w||"",this.isGetPurchase=!0,this.couponAnnualCardInfo=this.skuList.find((function(t){if(t.coupon&&e.dyAnnualCardSkuId===t.skuId)return t.coupon}))||null,[3,5];case 3:return 401!==K.sent().statusCode&&(this.isGetPurchase=!1,this.badRequestCb()),[3,5];case 4:return this.setActivityKey(""),[7];case 5:return[2]}}))}))},resetPurchaseInfo:function(){this.memberInfo={expireText:"",vipType:"guest",name:"",avatarUrl:""},this.expireText="",this.vipType="guest",this.name="",this.avatarUrl="",this.skuList=[],this.couponText="",this.serverNowTime=0,this.promotionDescFirst="",this.promotionDescSecond="",this.prompt="",this.discount=0,this.isGetPurchase=!1,this.isPaymentPending=!1,this.badRequestCb=function(){}},setActivityKey:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.activityKey=e},changePaymentStatus:function(e){this.isPaymentPending=!!e},updateClickedSkuId:function(e){this.clickedSkuId=e},setNeedShowSubscribeConfirm:function(e){this.needShowSubscribeConfirm=e},figIsAllowedPay:function(e){return!!e&&!X.includes(e)},setIsReceivedWelfare:function(e){this.isReceivedWelfare=e},setWelfareType:function(e){this.welfareType=e},setPrizeSkuId:function(e){this.prizeSkuId=e}},persist:{enabled:!0,paths:["isReceivedWelfare","welfareType"]}});n.usePurchaseInfo=$}));
//# sourceMappingURL=index.js.map