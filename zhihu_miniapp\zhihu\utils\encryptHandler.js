define("utils/encryptHandler.js", (function(r, n, t, e, o, c, a, d, y, i, p, s, S, b, u, I, f, B, C, A, g, v, E, l, J, L, h, P, m, K, N, D, Q, k, G, Y, j, q, T, U) {
    "use strict";
    ! function() {
        try {
            var r = void 0 !== p ? p : "undefined" != typeof global ? global : void 0 !== b ? b : {},
                n = (new Error).stack;
            n && (r._sentryDebugIds = r._sentryDebugIds || {}, r._sentryDebugIds[n] = "90d59891-4a58-4466-853a-5677ebedb776", r._sentryDebugIdIdentifier = "sentry-dbid-90d59891-4a58-4466-853a-5677ebedb776")
        } catch (r) {}
    }();
    var W = r("../common/vendor.js"),
        H = W.CryptoJS.lib.WordArray.random(16),
        M = W.CryptoJS.lib.WordArray.random(16);
    t.AESDecrypt = function(r, n, t) {
        if (!r) return "";
        var e = W.CryptoJS.AES.decrypt(r, n, {
            iv: t,
            mode: W.CryptoJS.mode.CBC,
            padding: W.CryptoJS.pad.Pkcs7
        });
        return JSON.parse(e.toString(W.CryptoJS.enc.Utf8))
    }, t.RSAEncrypt = function(r, n, t) {
        if (!r) return {};
        try {
            var e = n.toString(W.CryptoJS.enc.Hex),
                o = t.toString(W.CryptoJS.enc.Hex),
                c = "".concat(e).concat(function(r, n) {
                    return W.CryptoJS.SHA256(r).toString(W.CryptoJS.enc.Hex).substring(0, n)
                }(e, 5)),
                a = {
                    data: r,
                    key: c,
                    iv: o
                },
                d = new W.JSEncrypt;
            d.setPublicKey("\n-----BEGIN PUBLIC KEY-----\nMIIBITANBgkqhkiG9w0BAQEFAAOCAQ4AMIIBCQKCAQBTeA5vtwjrhTPG4cO7eqgY\nn1u59sbvscqULjREdmTpf1yCGQPtqBrFwe+VoKlGhcvdrnQhKrBlePXMVtAXmJDj\n0LBrKCUzYPBNPnBiDaTVW1YWpPfMqfpBYVLyu0LcvKzhWdapSTNavmCsgo1EGXQD\nfX2rq3ZYF/FI4/tnrxQFrltT5Zh88kbpxLLHlKSXzKOeQ74+3LrNfeEWPS5NEZhb\nGXBDnAaXjTibNOLc5kWLi+IhSU47ecs5psudhPJIPI4v4NIVLRErj1LMJIy/IhYQ\nnmy8b+ppBelEKt0B9vumphp7bMfGdUb2FSY2iKmSIUbHkb/RwGnl++b+qjqWnsfB\nAgMBAAE=\n-----END PUBLIC KEY-----\n");
            var y = d.encryptLong(JSON.stringify(a));
            return {
                payload: "".concat("100").concat(y)
            }
        } catch (e) {
            return {}
        }
    }, t.aesIv = M, t.aeskey = H
}));
//# sourceMappingURL=encryptHandler.js.map