define("pages/points/fetch.js",(function(n,t,e,r,a,u,c,i,o,s,l,f,b,p,d,h,y,g,v,w,k,m,x,I,D,S,_,T,j,E,P,q,A,B,G,z,C,F,H,J){"use strict";function K(n,t){var e,r,a,u,c={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return u={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function i(u){return function(i){return function(u){if(e)throw new TypeError("Generator is already executing.");for(;c;)try{if(e=1,r&&(a=2&u[0]?r.return:u[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,u[1])).done)return a;switch(r=0,a&&(u=[2&u[0],a.value]),u[0]){case 0:case 1:a=u;break;case 4:return c.label++,{value:u[1],done:!1};case 5:c.label++,r=u[1],u=[0];continue;case 7:u=c.ops.pop(),c.trys.pop();continue;default:if(!(a=c.trys,(a=a.length>0&&a[a.length-1])||6!==u[0]&&2!==u[0])){c=0;continue}if(3===u[0]&&(!a||u[1]>a[0]&&u[1]<a[3])){c.label=u[1];break}if(6===u[0]&&c.label<a[1]){c.label=a[1],a=u;break}if(a&&c.label<a[2]){c.label=a[2],c.ops.push(u);break}a[2]&&c.ops.pop(),c.trys.pop();continue}u=t.call(n,c)}catch(n){u=[6,n],r=0}finally{e=a=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,i])}}}var L=function(n,t,e){return new a((function(r,u){var c=function(n){try{o(e.next(n))}catch(n){u(n)}},i=function(n){try{o(e.throw(n))}catch(n){u(n)}},o=function(n){return n.done?r(n.value):a.resolve(n.value).then(c,i)};o((e=e.apply(n,t)).next())}))};!function(){try{var n=void 0!==l?l:"undefined"!=typeof global?global:void 0!==p?p:{},t=(new Error).stack;t&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[t]="3bf7f63b-c6c9-4d1a-acf0-c3ce6496658c",n._sentryDebugIdIdentifier="sentry-dbid-3bf7f63b-c6c9-4d1a-acf0-c3ce6496658c")}catch(n){}}();var M=n("../../utils/request.js");e.completeAdTask=function(n){return L(e,null,(function(){return K(this,(function(t){switch(t.label){case 0:return[4,M.api.post("/dalaran/task/complete",n,{custom:{useSignature:!0}})];case 1:return[2,t.sent()]}}))}))},e.getBalance=function(){return L(e,null,(function(){return K(this,(function(n){switch(n.label){case 0:return[4,M.api.get("/dalaran/points/balance")];case 1:return[2,n.sent()]}}))}))},e.getPointsSkus=function(){return L(e,null,(function(){return K(this,(function(n){switch(n.label){case 0:return[4,M.api.get("/dalaran/points/skus")];case 1:return[2,n.sent()]}}))}))},e.getPointsTasks=function(){return L(e,null,(function(){return K(this,(function(n){switch(n.label){case 0:return[4,M.api.get("/dalaran/points/tasks")];case 1:return[2,n.sent()]}}))}))}}));
//# sourceMappingURL=fetch.js.map