define("hooks/useRewardedVideoAd.js",(function(e,n,t,r,a,o,u,l,c,i,s,f,d,v,b,h,y,p,w,g,x,k,m,A,E,I,L,j,D,_,C,R,V,S,T,G,U,q,z,B){"use strict";function F(e,n){var t,r,a,o,u={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function l(o){return function(l){return function(o){if(t)throw new TypeError("Generator is already executing.");for(;u;)try{if(t=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,r=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(!(a=u.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){u=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){u.label=o[1];break}if(6===o[0]&&u.label<a[1]){u.label=a[1],a=o;break}if(a&&u.label<a[2]){u.label=a[2],u.ops.push(o);break}a[2]&&u.ops.pop(),u.trys.pop();continue}o=n.call(e,u)}catch(e){o=[6,e],r=0}finally{t=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,l])}}}!function(){try{var e=void 0!==s?s:"undefined"!=typeof global?global:void 0!==v?v:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="5e92bbd8-ed83-43cc-bcf2-a19cdb4dae6e",e._sentryDebugIdIdentifier="sentry-dbid-5e92bbd8-ed83-43cc-bcf2-a19cdb4dae6e")}catch(e){}}();var H=e("../common/vendor.js");t.useRewardedVideoAd=function(e,n){var t=this,r=H.ref(null),o=H.ref(!1),u=H.ref(!1),l=function(){r.value&&!o.value&&(o.value=!0,r.value.onLoad((function(e){})),r.value.onClose((function(n){u.value=!1,n.isEnded&&"function"==typeof e&&e()})),r.value.onError((function(e){u.value=!1,H.index.showToast({title:"广告加载失败",icon:"none"}),"function"==typeof n&&n(e)})))};return{initAd:function(e){if(e&&!r.value)try{r.value=H.index.createRewardedVideoAd({adUnitId:e}),l()}catch(e){}},showAd:function(){return e=t,n=function(){var e;return F(this,(function(n){switch(n.label){case 0:if(!r.value)return[2,a.reject({message:"广告未初始化"})];if(u.value)return[2,a.reject({message:"广告正在显示中"})];u.value=!0,H.index.showLoading({title:"加载中"}),n.label=1;case 1:return n.trys.push([1,3,9,10]),[4,r.value.show()];case 2:return n.sent(),[3,10];case 3:n.sent(),n.label=4;case 4:return n.trys.push([4,7,,8]),[4,r.value.load()];case 5:return n.sent(),[4,r.value.show()];case 6:return n.sent(),[3,8];case 7:throw e=n.sent(),u.value=!1,e;case 8:return[3,10];case 9:return H.index.hideLoading(),[7];case 10:return[2]}}))},new a((function(t,r){var o=function(e){try{l(n.next(e))}catch(e){r(e)}},u=function(e){try{l(n.throw(e))}catch(e){r(e)}},l=function(e){return e.done?t(e.value):a.resolve(e.value).then(o,u)};l((n=n.apply(e,null)).next())}));var e,n},destroyAd:function(){r.value&&(r.value.offLoad&&r.value.offLoad(),r.value.offClose&&r.value.offClose(),r.value.offError&&r.value.offError(),r.value.destroy&&r.value.destroy(),r.value=null,o.value=!1,u.value=!1)}}}}));
//# sourceMappingURL=useRewardedVideoAd.js.map