define("components/confirm-and-pay-modal/confirm-and-pay-modal.js",(function(e,s,o,t,n,r,i,d,a,u,l,c,p,f,j,m,y,b,P,g,h,x,I,_,v,M,w,q,D,k,A,H,S,T,C,E,B,F,G,O){"use strict";!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==f?f:{},s=(new Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="ef3b3d98-6f78-4eee-b96d-31e1635e3f7c",e._sentryDebugIdIdentifier="sentry-dbid-ef3b3d98-6f78-4eee-b96d-31e1635e3f7c")}catch(e){}}();var U=e("../../common/vendor.js"),V=e("../../utils/jumpToProtocolPage.js");e("../../utils/track.js"),e("../../stores/modules/user/index.js"),e("../../utils/appFetch.js"),e("../../utils/request.js"),e("../../stores/modules/history/index.js"),e("../../stores/modules/login/index.js"),e("../../utils/constant.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js"),e("../../stores/modules/purchaseInfo/index.js"),e("../../pages/manuscript/store.js"),e("../../stores/modules/clientInfo/index.js"),e("../../utils/utils.js"),e("../../writeConstant.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js"),e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js"),e("../../utils/appInit.js"),e("../../utils/onGetUdid.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),e("../../utils/pageHelper.js");var z={__name:"confirm-and-pay-modal",props:{isShowPayModal:{type:Boolean,required:!0},confirmAndPay:{type:t,required:!0},closePayModal:{type:t,required:!0}},setup:function(e){var s=e,o=function(e){e.stopPropagation()},t=function(){s.confirmAndPay&&s.confirmAndPay()},n=function(){s.closePayModal&&s.closePayModal()};return function(s,r){return U.e({a:e.isShowPayModal},e.isShowPayModal?{b:U.o(n),c:U.o((function(){return U.unref(V.jumpToProtocolPage)(1)})),d:U.o((function(){return U.unref(V.jumpToProtocolPage)(6)})),e:U.o(t),f:U.o(o)}:{})}}},J=U._export_sfc(z,[["__scopeId","data-v-70a4bd4c"]]);tt.createComponent(J)}));
//# sourceMappingURL=confirm-and-pay-modal.js.map