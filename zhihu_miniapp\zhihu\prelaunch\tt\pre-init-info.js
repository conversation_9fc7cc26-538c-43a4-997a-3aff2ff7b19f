define("prelaunch/tt/pre-init-info.js",(function(n,e,t,r,a,o,u,c,i,l,s,f,d,y,p,b,h,v,g,w,k,S,x,I,_,m,D,j,A,C,E,G,J,L,N,O,T,q,z,B){"use strict";function F(n,e){var t,r,a,o,u={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function c(o){return function(c){return function(o){if(t)throw new TypeError("Generator is already executing.");for(;u;)try{if(t=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,r=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(!(a=u.trys,(a=a.length>0&&a[a.length-1])||6!==o[0]&&2!==o[0])){u=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){u.label=o[1];break}if(6===o[0]&&u.label<a[1]){u.label=a[1],a=o;break}if(a&&u.label<a[2]){u.label=a[2],u.ops.push(o);break}a[2]&&u.ops.pop(),u.trys.pop();continue}o=e.call(n,u)}catch(n){o=[6,n],r=0}finally{t=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,c])}}}!function(){try{var n=void 0!==s?s:"undefined"!=typeof global?global:void 0!==y?y:{},e=(new Error).stack;e&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[e]="9a774966-29ad-46c8-813f-addcd5f3d9d0",n._sentryDebugIdIdentifier="sentry-dbid-9a774966-29ad-46c8-813f-addcd5f3d9d0")}catch(n){}}(),n("../../common/vendor.js"),n("../../writeConstant.js");var H=function(n){return"pre_init:".concat(n)},K="login_code";t.getLoginCodeAsync=function(){return n=t,e=function(){var n,e;return F(this,(function(t){switch(t.label){case 0:return[4,function(n){var e=tt.getStorageInfoSync().keys,t=H(n);return Array.isArray(e)&&e.includes(t)?new a((function(n){tt.getStorage({key:t,success:function(e){var t=(e.data?JSON.parse(e.data):null)||{},r=t.data,a=t.expire,o=Date.now();n(void 0!==r&&o<a?r:null)},fail:function(e){n(null)}})})):null}(K)];case 1:if(!(n=t.sent()))return[2,null];t.label=2;case 2:return t.trys.push([2,4,,5]),[4,(e=K,new a((function(n,t){tt.removeStorage({key:H(e),success:function(e){n(e)},fail:t})})))];case 3:return t.sent(),[3,5];case 4:return t.sent(),[2,null];case 5:return[2,n]}}))},new a((function(t,r){var o=function(n){try{c(e.next(n))}catch(n){r(n)}},u=function(n){try{c(e.throw(n))}catch(n){r(n)}},c=function(n){return n.done?t(n.value):a.resolve(n.value).then(o,u)};c((e=e.apply(n,null)).next())}));var n,e}}));
//# sourceMappingURL=pre-init-info.js.map