define("stores/modules/trackInfo/index.js",(function(e,t,d,r,n,s,a,i,o,f,u,c,I,g,h,b,v,y,k,l,p,S,D,_,m,j,T,w,x,E,P,q,z,A,B,C,F,G,H,J){"use strict";!function(){try{var e=void 0!==u?u:"undefined"!=typeof global?global:void 0!==g?g:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="8e70e4d9-ff01-4f03-89d5-be3af0a507d6",e._sentryDebugIdIdentifier="sentry-dbid-8e70e4d9-ff01-4f03-89d5-be3af0a507d6")}catch(e){}}();var K=e("../../../common/vendor.js"),L={isShare:!1,shareId:"",pageId:""},M=K.defineStore("trackInfo",{state:function(){return L},getters:{},actions:{updatePageId:function(e){this.pageId=e},updateTrackInfo:function(e){var t=e.isShare,d=void 0!==t&&t,r=e.shareId,n=void 0===r?"":r;this.isShare=d,this.shareId=n}}});d.useTrackStore=M}));
//# sourceMappingURL=index.js.map