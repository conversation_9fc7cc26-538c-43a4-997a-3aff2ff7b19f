define("utils/adManager.js",(function(e,t,n,r,a,o,i,s,d,u,c,l,f,y,h,v,g,b,w,p,I,A,_,x,S,j,m,k,E,T,O,L,M,D,P,R,V,C,U,K){"use strict";function q(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var z=Object.defineProperty,B=Object.getOwnPropertySymbols,F=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable,H=function(e,t,n){return t in e?z(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==y?y:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="fcf8c339-49df-4dc7-9525-4ef5617e73e5",e._sentryDebugIdIdentifier="sentry-dbid-fcf8c339-49df-4dc7-9525-4ef5617e73e5")}catch(e){}}();var J=e("../common/vendor.js"),N=e("./gift.js"),Q=new(function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.adInstances=new Map,this.loadingStatus=new Map}var t,n,r;return t=e,n=[{key:"createRewardedVideoAd",value:function(e){var t=this,n=e.adUnitId,r=e.name,o=e.type,i=e.loadingText,s=void 0===i?"广告加载中...":i,d=e.errorText,u=void 0===d?"广告加载失败，请稍后再试~":d;this.adInstances.has(r)&&this._destroyAd(r);try{var c;return c=N.isMiniKS()?ks.createRewardedVideoAd({unitId:n,type:o}):J.index.createRewardedVideoAd({adUnitId:n}),this.loadingStatus.set(r,!1),this.adInstances.set(r,c),this._setupEventListeners(c,r),{instance:c,destroyAd:function(){return t._destroyAd(r)},show:function(e){return t._showAd((null==e?void 0:e.name)||r,function(e,t){for(var n in t||(t={}))F.call(t,n)&&H(e,n,t[n]);var r=!0,a=!1,o=void 0;if(B)try{for(var i,s=B(t)[Symbol.iterator]();!(r=(i=s.next()).done);r=!0)n=i.value,G.call(t,n)&&H(e,n,t[n])}catch(e){a=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(a)throw o}}return e}({loadingText:s,errorText:u},e||{}))}}}catch(e){return{instance:null,destroyAd:function(){return t._destroyAd(r)},show:function(){return a.reject(e)}}}}},{key:"_showAd",value:function(e,t){var n=this,r=t.loadingText,o=t.errorText,i=this.adInstances.get(e);return i?this.loadingStatus.get(e)?void 0:(J.index.showLoading({title:r,icon:"none"}),this.loadingStatus.set(e,!0),i.load().then((function(){return i.show()})).catch((function(t){return n.loadingStatus.set(e,!1),J.index.showToast({title:o,icon:"none"}),a.reject(t)})).finally((function(){J.index.hideLoading()}))):a.reject(new Error("广告实例不存在"))}},{key:"_destroyAd",value:function(e){var t=this.adInstances.get(e);if(t)try{t.offLoad(),t.offError(),t.offClose(),t.destroy(),this.adInstances.delete(e),this.loadingStatus.delete(e)}catch(e){}}},{key:"destroyAll",value:function(){var e=!0,t=!1,n=void 0;try{for(var r,a=this.adInstances.keys()[Symbol.iterator]();!(e=(r=a.next()).done);e=!0){var o=r.value;this._destroyAd(o)}}catch(e){t=!0,n=e}finally{try{e||null==a.return||a.return()}finally{if(t)throw n}}}},{key:"_setupEventListeners",value:function(e,t){var n=this;e.onLoad((function(){n.loadingStatus.set(t,!1)})),e.onError((function(e){n.loadingStatus.set(t,!1)}))}}],n&&q(t.prototype,n),r&&q(t,r),e}());n.adManager=Q}));
//# sourceMappingURL=adManager.js.map