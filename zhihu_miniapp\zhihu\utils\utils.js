define("utils/utils.js",(function(n,t,e,r,o,i,u,a,c,l,f,d,s,v,g,y,b,h,p,m,D,w,I,j,A,S,x,T,M,C,F,O,_,k,E,U,N,P,R,Y){"use strict";function Q(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function $(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var r,o,i=[],u=!0,a=!1;try{for(e=e.call(n);!(u=(r=e.next()).done)&&(i.push(r.value),!t||i.length!==t);u=!0);}catch(n){a=!0,o=n}finally{try{u||null==e.return||e.return()}finally{if(a)throw o}}return i}}(n,t)||function(n,t){if(!n)return;if("string"==typeof n)return Q(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);"Object"===e&&n.constructor&&(e=n.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return Q(n,t)}(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}!function(){try{var n=void 0!==f?f:"undefined"!=typeof global?global:void 0!==v?v:{},t=(new Error).stack;t&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[t]="6744bb85-4c24-4355-8f1d-9b533e878830",n._sentryDebugIdIdentifier="sentry-dbid-6744bb85-4c24-4355-8f1d-9b533e878830")}catch(n){}}();var q=n("../common/vendor.js");n("../writeConstant.js"),e.centToDiamond=function(n){return(n/10).toString()},e.centToField=function(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,e=n/100;return Number.isInteger(e)?e.toString():e.toFixed(t)},e.centToYuan=function(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,e=n/100;return Number.isInteger(e)?e.toString():e.toFixed(t)},e.convertObjectToQueryString=function(n){return Object.entries(n).map((function(n){var t=$(n,2),e=t[0],r=t[1];return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(r))})).join("&")},e.debounce=function(n){var t,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return function(o){var a=o,c=arguments;if(t&&u(t),r){var l=!t;t=i((function(){t=null}),e),l&&n.apply(a,c)}else t=i((function(){n.apply(a,c)}),e)}},e.getData=function(n){var t=new Date(n),e=t.getFullYear(),r=t.getMonth()+1,o=t.getDate();return e+"-"+(r<10?"0"+r:r)+"-"+(o<10?"0"+o:o)},e.getDateDiff=function(n){var t=new Date(n),e=new Date,r=Math.abs(t-e);return Math.floor(r/864e5)},e.isFunction=function(n){return"function"==typeof n},e.isTokenExpired=function(n,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:18e5;return!n||!t||Date.now()>t-e},e.jumpPage=function(n){var t=n.url,e=n.callback;if(t){var r=getCurrentPages();(null==r?void 0:r.length)>7?q.index.redirectTo({url:t,success:function(){e&&e()}}):q.index.navigateTo({url:t,success:function(){e&&e()}})}},e.onAccelerate=function(n){var t=!1,e=null;return q.index.onAccelerometerChange((function(r){Math.abs(r.x)>.9&&Math.abs(r.y)>.9&&(t||(t=!0,"function"==typeof n&&n(),e=i((function(){t=!1}),3e3)))})),function(){u(e),q.index.stopAccelerometer()}},e.randomID=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:2;return Date.now()+new Array(n).fill(void 0).map((function(){return Math.random().toString(36).slice(2)})).join("")},e.sleep=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3;return new o((function(t){i((function(){t(!0)}),n)}))},e.throttle=function(n){var t,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,o=0;return function(u){var a=u,c=arguments;if(1===r){var l=Date.now();l-o>e&&(n.apply(a,c),o=l)}else 2===r&&(t||(t=i((function(){t=null,n.apply(a,c)}),e)))}}}));
//# sourceMappingURL=utils.js.map