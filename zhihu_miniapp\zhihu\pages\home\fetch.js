define("pages/home/<USER>",(function(e,t,n,r,a,u,o,c,i,f,l,s,b,h,d,p,y,v,g,w,D,k,x,I,m,_,j,E,S,T,q,F,G,z,A,B,C,H,J,<PERSON>){"use strict";function L(e,t){var n,r,a,u,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return u={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function c(u){return function(c){return function(u){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,r&&(a=2&u[0]?r.return:u[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,u[1])).done)return a;switch(r=0,a&&(u=[2&u[0],a.value]),u[0]){case 0:case 1:a=u;break;case 4:return o.label++,{value:u[1],done:!1};case 5:o.label++,r=u[1],u=[0];continue;case 7:u=o.ops.pop(),o.trys.pop();continue;default:if(!(a=o.trys,(a=a.length>0&&a[a.length-1])||6!==u[0]&&2!==u[0])){o=0;continue}if(3===u[0]&&(!a||u[1]>a[0]&&u[1]<a[3])){o.label=u[1];break}if(6===u[0]&&o.label<a[1]){o.label=a[1],a=u;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(u);break}a[2]&&o.ops.pop(),o.trys.pop();continue}u=t.call(e,o)}catch(e){u=[6,e],r=0}finally{n=a=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,c])}}}var M=function(e,t,n){return new a((function(r,u){var o=function(e){try{i(n.next(e))}catch(e){u(e)}},c=function(e){try{i(n.throw(e))}catch(e){u(e)}},i=function(e){return e.done?r(e.value):a.resolve(e.value).then(o,c)};i((n=n.apply(e,t)).next())}))};!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==h?h:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f6a31ff8-07ea-4b78-b25f-2ca8674f0f88",e._sentryDebugIdIdentifier="sentry-dbid-f6a31ff8-07ea-4b78-b25f-2ca8674f0f88")}catch(e){}}();var N=e("../../utils/request.js");n.fetchFeedData=function(e){return M(n,null,(function(){return L(this,(function(t){switch(t.label){case 0:return[4,N.api.get(e)];case 1:return[2,t.sent()]}}))}))},n.fetchTabData=function(){return M(n,null,(function(){return L(this,(function(e){switch(e.label){case 0:return[4,N.api.get("/dalaran/home/<USER>")];case 1:return[2,e.sent()]}}))}))}}));
//# sourceMappingURL=fetch.js.map