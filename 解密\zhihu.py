import requests
import gzip
import json
import brotli
from urllib.parse import quote

# 搜索函数 - 使用原始API但应用新请求头
def search_zhihu(keyword):
    # 使用原始搜索API
    url = "https://api.zhihu.com/search_v3"
    
    # 查询参数
    params = {
        "gk_version": "gz-gaokao",
        "q": keyword,
        "t": "general",
        "search_source": "Normal",
        "is_real_time": "0",
        "correction": "1",
        "advert_count": "",
        "show_all_topics": "0",
        "pin_flow": "false",
        "restricted_scene": "",
        "restricted_field": "",
        "restricted_value": "",
        "offset": "0",
        "limit": "20",
        "lc_idx": "0"
    }

    # 结合新旧请求头
    headers = {
        "host": "api.zhihu.com",
        "x-client-info": "react-native/amadeus-rn/2.295.0",
        "x-api-version": "3.0.93",  # 使用新版本
        "x-b3-traceid": "b3ca5e34681940509e6ec3974259f92a",  # 使用新的traceid
        "x-client-ri": "3747188415",
        "user-agent": "com.zhihu.android/Futureve/8.47.0 com.zhihu.vip.android/zhvip/1.67.1 Mozilla/5.0 (Linux; Android 10; M2003J15SC Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.1000.10 Mobile Safari/537.36",
        "x-app-version": "1.67.1",  # 使用新的版本
        "x-app-za": "OS=Android&Release=10&Model=M2003J15SC&VersionName=1.67.1&VersionCode=8030&Product=com.zhihu.android&Width=1080&Height=2110&Installer=bdsd&DeviceType=AndroidPhone&Brand=Redmi",
        "x-app-bundleid": "com.zhihu.vip.android",
        "x-app-flavor": "bdsd",
        "x-app-build": "release",
        "x-network-type": "WiFi",
        "x-zst-82": "2.0dZCT3fBw1BoMAAAAYAkAADIuMISKh2gAAAAACwWgznfsZKxAKBKMGSg1HgxQCgc=",
        "x-zst-81": "PKTTM6xg1Bp9N2GlwgMt9_4axXZUuhXyyF9Oow==",  # 保留原来的zst-81
        "x-udid": "ZfITJuJv1BpgCYw7auaZ-sYY9PGZkka6L2E=",  # 使用新的udid
        "authorization": "Bearer 2.1eLqhUwAAAABl8hMm4m_UGj8AAABgAlVNyRavaADXXiyu82r3zNp4DDsmsjwC9bo5wA",  # 使用新的authorization
        "x-ms-id": "DU4s5SXY5o1pLPwikgaikElxX-udbzRtM14fRFU0czVTWFk1bzFwTFB3aWtnYWlrRWx4WC11ZGJ6UnRNMTRmc2h1",
        "accept-encoding": "br,gzip",
        "cookie": "BEC=d892da65acb7e34c89a3073e8fa2254f; _xsrf=aOCLaZPtJ79WQeTxpeTUlryeNnz9tg55; z_c0=2|1:0|10:1753713098|4:z_c0|92:Mi4xZUxxaFV3QUFBQUJsOGhNbTRtX1VHajhBQUFCZ0FsVk55UmF2YUFEWFhpeXU4MnIzek5wNEREc21zandDOWJvNXdB|98868f1de2313465a9baf3e1302e34414885aeec2e9a58f3e58796023b0ea5e8; capsion_ticket=2|1:0|10:1753713080|14:capsion_ticket|44:N2NmNzdjOTc5YWY0NDYzZmFkZWE4ODkyOTBhMDE5ZmY=|9aff3ecc6bca422b686873f560d5131d2fa7f71ff7bff35aabbe4cd1a32693f4",
        "x-zse-96": "1.0_x/OT6T6yvK/50JWaci1qz+TFtYwts3eRvzaiJ3f5AjlmW9o6QaClSNCdLV4RQ6QM",
        "x-zse-93": "204_1_3.0"  # 使用新的zse-93
    }

    response = requests.get(url, headers=headers, params=params)
    
    if response.status_code == 200:
        # 尝试处理可能的压缩内容
        content = response.content
        try:
            # 尝试各种解压方法
            if response.headers.get('Content-Encoding') == 'gzip':
                content = gzip.decompress(content)
                data = json.loads(content.decode('utf-8'))
            elif response.headers.get('Content-Encoding') == 'br':
                # 先尝试直接解析
                try:
                    data = response.json()
                except:
                    # 如果失败，尝试用brotli解压
                    try:
                        content = brotli.decompress(content)
                        data = json.loads(content.decode('utf-8'))
                    except:
                        # 如果还失败，尝试直接用content解码
                        data = json.loads(content.decode('utf-8'))
            elif response.headers.get('Content-Encoding') == 'deflate':
                content = gzip.decompress(content)
                data = json.loads(content.decode('utf-8'))
            else:
                # 默认情况，直接尝试解析
                data = response.json()
            
            items = data.get("data", [])
            if not items:
                print("未找到相关结果")
                return
                
            for i, item in enumerate(items, 1):
                # 获取object对象
                target = item.get("object", {})
                
                # 提取标题
                title = target.get("title", "")
                if not title and "highlight" in target and target["highlight"]:
                    title = target["highlight"].get("title", "")
                if not title:
                    title = "无标题"
                
                # 清理HTML标签
                title = title.replace("<em>", "").replace("</em>", "")
                
                # 提取摘要/描述
                excerpt = target.get("excerpt", "")
                description = target.get("description", "")
                
                # 提取ID用于构建链接
                obj_id = target.get("id", "")
                
                # 根据不同类型构建链接
                link = "暂无链接"
                obj_type = target.get("type", "")
                
                if obj_type == "question":
                    # 问题类型
                    url_token = target.get("url_token", "")
                    if url_token:
                        link = f"https://www.zhihu.com/question/{url_token}"
                    elif obj_id:
                        link = f"https://www.zhihu.com/question/{obj_id}"
                elif obj_type == "article":
                    # 文章类型
                    if obj_id:
                        link = f"https://zhuanlan.zhihu.com/p/{obj_id}"
                elif obj_type == "paid_column":
                    # 付费专栏
                    paid_info = target.get("paid_column", {})
                    paid_id = paid_info.get("paid_column_id", "")
                    if paid_id:
                        link = f"https://www.zhihu.com/column/{paid_id}"
                    elif obj_id:
                        link = f"https://www.zhihu.com/column/{obj_id}"
                elif obj_type:
                    # 其他类型，尝试通用链接格式
                    if obj_id:
                        link = f"https://www.zhihu.com/{obj_type}/{obj_id}"
                
                # 打印结果
                print(f"{i}. 标题: {title}")
                if excerpt:
                    # 清理摘要中的HTML标签
                    excerpt = excerpt.replace("<em>", "").replace("</em>", "")
                    print(f"   摘要: {excerpt}")
                if description:
                    # 清理描述中的HTML标签
                    description = description.replace("<em>", "").replace("</em>", "")
                    print(f"   描述: {description}")
                print(f"   链接: {link}")
                print(f"   类型: {obj_type if obj_type else '未知'}")
                print()
                
        except Exception as e:
            print(f"解析错误: {e}")
            # 打印前500个字符以供调试
            print("响应内容(前500字符):", content[:500] if isinstance(content, (bytes, str)) else str(content)[:500])
    else:
        print("请求失败，状态码:", response.status_code)
        print("响应内容:", response.text)

# 主程序入口
if __name__ == "__main__":
    keyword = input("请输入要搜索的关键词: ")
    search_zhihu(keyword)