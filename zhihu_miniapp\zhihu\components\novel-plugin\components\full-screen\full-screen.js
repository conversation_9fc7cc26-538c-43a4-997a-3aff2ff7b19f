define("components/novel-plugin/components/full-screen/full-screen.js",(function(e,n,t,r,o,s,u,a,c,i,l,f,b,d,g,v,p,m,y,_,h,I,D,j,C,w,M,N,S,k,E,F,P,q,x,z,A,B,G,H){"use strict";!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==d?d:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="343e2c6a-9306-4e80-a6e4-46299b3008ba",e._sentryDebugIdIdentifier="sentry-dbid-343e2c6a-9306-4e80-a6e4-46299b3008ba")}catch(e){}}();var J=e("../../../../common/vendor.js");Math||K();var K=function(){return"../../../recharge-tips/recharge-tips.js"},L={__name:"full-screen",setup:function(e){var n=function(){J.NovelPlugin.getCurrentNovelManager().setFullScreenComponentStatus({show:!1})};return function(e,t){return{a:J.o(n),b:J.p({visible:!0,fullscreen:!0})}}}};tt.createComponent(L)}));
//# sourceMappingURL=full-screen.js.map