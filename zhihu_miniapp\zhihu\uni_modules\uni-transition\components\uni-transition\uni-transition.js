define("uni_modules/uni-transition/components/uni-transition/uni-transition.js",(function(t,n,i,o,e,r,a,s,c,u,l,f,m,d,h,y,p,b,g,v,w,S,j,O,C,I,A,T,D,k,_,z,X,Y,$,E,P,x,L,M){"use strict";function B(t,n){(null==n||n>t.length)&&(n=t.length);for(var i=0,o=new Array(n);i<n;i++)o[i]=t[i];return o}function F(t){return function(t){if(Array.isArray(t))return B(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(!t)return;if("string"==typeof t)return B(t,n);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(i);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return B(t,n)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var N=Object.defineProperty,R=Object.defineProperties,U=Object.getOwnPropertyDescriptors,Z=Object.getOwnPropertySymbols,q=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable,H=function(t,n,i){return n in t?N(t,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[n]=i};!function(){try{var t=void 0!==l?l:"undefined"!=typeof global?global:void 0!==d?d:{},n=(new Error).stack;n&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[n]="dd250e21-b7a6-445e-a02b-5d5fc25c29df",t._sentryDebugIdIdentifier="sentry-dbid-dd250e21-b7a6-445e-a02b-5d5fc25c29df")}catch(t){}}();var J=t("./createAnimation.js"),K=t("../../../../common/vendor.js"),Q={name:"uniTransition",emits:["click","change"],props:{show:{type:Boolean,default:!1},modeClass:{type:[Array,String],default:function(){return"fade"}},duration:{type:Number,default:300},styles:{type:Object,default:function(){return{}}},customClass:{type:String,default:""},onceRender:{type:Boolean,default:!1}},data:function(){return{isShow:!1,transform:"",opacity:1,animationData:{},durationTime:300,config:{}}},watch:{show:{handler:function(t){t?this.open():this.isShow&&this.close()},immediate:!0}},computed:{stylesObject:function(){var t,n,i=(t=function(t,n){for(var i in n||(n={}))q.call(n,i)&&H(t,i,n[i]);var o=!0,e=!1,r=void 0;if(Z)try{for(var a,s=Z(n)[Symbol.iterator]();!(o=(a=s.next()).done);o=!0){i=a.value;G.call(n,i)&&H(t,i,n[i])}}catch(t){e=!0,r=t}finally{try{o||null==s.return||s.return()}finally{if(e)throw r}}return t}({},this.styles),n={"transition-duration":this.duration/1e3+"s"},R(t,U(n))),o="";for(var e in i)o+=this.toLine(e)+":"+i[e]+";";return o},transformStyles:function(){return"transform:"+this.transform+";opacity:"+this.opacity+";"+this.stylesObject}},created:function(){this.config={duration:this.duration,timingFunction:"ease",transformOrigin:"50% 50%",delay:0},this.durationTime=this.duration},methods:{init:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};t.duration&&(this.durationTime=t.duration),this.animation=J.createAnimation(Object.assign(this.config,t),this)},onClick:function(){this.$emit("click",{detail:this.isShow})},step:function(t){var n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.animation){for(var o in t)try{var e;"object"==((n=t[o])&&"undefined"!=typeof Symbol&&n.constructor===Symbol?"symbol":typeof n)?(e=this.animation)[o].apply(e,F(t[o])):this.animation[o](t[o])}catch(t){}return this.animation.step(i),this}},run:function(t){this.animation&&this.animation.run(t)},open:function(){var t=this;a(this.timer),this.transform="",this.isShow=!0;var n=this.styleInit(!1),i=n.opacity,o=n.transform;void 0!==i&&(this.opacity=i),this.transform=o,this.$nextTick((function(){t.timer=r((function(){t.animation=J.createAnimation(t.config,t),t.tranfromInit(!1).step(),t.animation.run(),t.$emit("change",{detail:t.isShow})}),20)}))},close:function(t){var n=this;this.animation&&this.tranfromInit(!0).step().run((function(){n.isShow=!1,n.animationData=null,n.animation=null;var t=n.styleInit(!1),i=t.opacity,o=t.transform;n.opacity=i||1,n.transform=o,n.$emit("change",{detail:n.isShow})}))},styleInit:function(t){var n=this,i={transform:""},o=function(t,o){"fade"===o?i.opacity=n.animationType(t)[o]:i.transform+=n.animationType(t)[o]+" "};return"string"==typeof this.modeClass?o(t,this.modeClass):this.modeClass.forEach((function(n){o(t,n)})),i},tranfromInit:function(t){var n=this,i=function(t,i){var o=null;"fade"===i?o=t?0:1:(o=t?"-100%":"0","zoom-in"===i&&(o=t?.8:1),"zoom-out"===i&&(o=t?1.2:1),"slide-right"===i&&(o=t?"100%":"0"),"slide-bottom"===i&&(o=t?"100%":"0")),n.animation[n.animationMode()[i]](o)};return"string"==typeof this.modeClass?i(t,this.modeClass):this.modeClass.forEach((function(n){i(t,n)})),this.animation},animationType:function(t){return{fade:t?1:0,"slide-top":"translateY(".concat(t?"0":"-100%",")"),"slide-right":"translateX(".concat(t?"0":"100%",")"),"slide-bottom":"translateY(".concat(t?"0":"100%",")"),"slide-left":"translateX(".concat(t?"0":"-100%",")"),"zoom-in":"scaleX(".concat(t?1:.8,") scaleY(").concat(t?1:.8,")"),"zoom-out":"scaleX(".concat(t?1:1.2,") scaleY(").concat(t?1:1.2,")")}},animationMode:function(){return{fade:"opacity","slide-top":"translateY","slide-right":"translateX","slide-bottom":"translateY","slide-left":"translateX","zoom-in":"scale","zoom-out":"scale"}},toLine:function(t){return t.replace(/([A-Z])/g,"-$1").toLowerCase()}}},V=K._export_sfc(Q,[["render",function(t,n,i,o,e,r){var a;return{a:e.isShow,b:e.animationData,c:K.n(i.customClass),d:K.s(r.transformStyles),e:K.o((function(){for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return r.onClick&&(a=r).onClick.apply(a,F(n))}))}}]]);tt.createComponent(V)}));
//# sourceMappingURL=uni-transition.js.map