define("utils/request.js",(function(e,r,t,n,o,s,a,i,u,l,c,d,f,p,y,b,v,m,h,g,I,j,S,x,A,D,O,w,P,_,z,E,k,M,R,U,L,T,q,H){"use strict";function N(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}function V(e,r){return function(e){if(Array.isArray(e))return e}(e)||function(e,r){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var n,o,s=[],a=!0,i=!1;try{for(t=t.call(e);!(a=(n=t.next()).done)&&(s.push(n.value),!r||s.length!==r);a=!0);}catch(e){i=!0,o=e}finally{try{a||null==t.return||t.return()}finally{if(i)throw o}}return s}}(e,r)||function(e,r){if(!e)return;if("string"==typeof e)return N(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return N(e,r)}(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var C=Object.defineProperty,X=Object.defineProperties,K=Object.getOwnPropertyDescriptors,B=Object.getOwnPropertySymbols,G=Object.prototype.hasOwnProperty,J=Object.prototype.propertyIsEnumerable,$=function(e,r,t){return r in e?C(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t},F=function(e,r){for(var t in r||(r={}))G.call(r,t)&&$(e,t,r[t]);var n=!0,o=!1,s=void 0;if(B)try{for(var a,i=B(r)[Symbol.iterator]();!(n=(a=i.next()).done);n=!0){t=a.value;J.call(r,t)&&$(e,t,r[t])}}catch(e){o=!0,s=e}finally{try{n||null==i.return||i.return()}finally{if(o)throw s}}return e},Q=function(e,r){return X(e,K(r))};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==p?p:{},r=(new Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="15b7f6bc-21a5-471d-a293-9b1aaf2b9429",e._sentryDebugIdIdentifier="sentry-dbid-15b7f6bc-21a5-471d-a293-9b1aaf2b9429")}catch(e){}}();var W=e("../common/vendor.js"),Y=e("../stores/modules/user/index.js");e("../stores/modules/history/index.js");var Z=e("../stores/modules/login/index.js");e("../stores/modules/search-history/index.js"),e("../stores/modules/koc-ascription/index.js");var ee=e("../stores/modules/purchaseInfo/index.js"),re=e("../stores/modules/clientInfo/index.js");e("../stores/modules/systenInfo/index.js");var te=e("../stores/modules/globalVariable/index.js");e("../stores/modules/trackInfo/index.js");var ne=e("../stores/modules/devDebug/index.js"),oe=e("../writeConstant.js"),se=e("./appInit.js"),ae=e("./encryptHandler.js"),ie=e("./requstErrorMiddlewares.js"),ue="oauth c3cef7c66a1843f8b3a9e6a1e3160e20",le=function(e){return W.humpsExports.camelizeKeys(e)},ce=function(e){var r,t,n,o=e.data;return(null==(t=null==(r=null==e?void 0:e.config)?void 0:r.custom)?void 0:t.useSignature)&&(o=ae.AESDecrypt(null==(n=e.data)?void 0:n.data,ae.aeskey,ae.aesIv),e.config.custom=Q(F({},e.config.custom),{originRes:o}),de(e)),o},de=function(e){var r,t;if(null==(t=null==(r=null==e?void 0:e.config)?void 0:r.custom)?void 0:t.useSignature){var n=e.config,o=(n.fullPath,n.header,n.method,n.custom);o.originData,o.originRes}},fe=function(e){var r,t=Z.useLoginStore(),n=re.useClientStore(),o=e.url,s=e.custom,a=e.data,i=(null!=s?s:{}).useSignature,u=t.accessToken,l=n.udid,c=n.appName;e.header=null!=(r=e.header)?r:{},i&&(e.data=ae.RSAEncrypt(a,ae.aeskey,ae.aesIv),e.custom=Q(F({},s),{originData:a}));var d=se.getMiniVersion(),f=F(F({},{Authorization:u||ue,"X-UDID":l,"x-dy-app-name":c,"X-Mini-App-Version":d||""}),function(e){var r=e.authorization,t=e.udid;if(!e.useSignature)return{};var n="201_7_1.0",o=function(e){var r=e.match(/^((https?):)?(\/\/([^/?#:]*))?([^?#]*)(\?([^#]*))?(#(.*))?/)||[],t=r[5]||"",n=r[6]?r[6].substring(1):"";return n?"".concat(t,"?").concat(n):t}(e.url),s=[n,o,r,t].filter(Boolean).join("+");return{"x-zse-93":n,"x-zse-96":"1.0_".concat(W.encrypt(W.cryptoJsExports.MD5(s)))}}({url:o,authorization:u||ue,udid:l,useSignature:i})),p=!0,y=!1,b=void 0;try{for(var v,m=Object.entries(f)[Symbol.iterator]();!(p=(v=m.next()).done);p=!0){var h=V(v.value,2),g=h[0],I=h[1];I&&(e.header[g]=I)}}catch(e){y=!0,b=e}finally{try{p||null==m.return||m.return()}finally{if(y)throw b}}return e},pe=function(e){return e.baseURL=function(){var e=te.useGlobalVariable().isRelease,r=ne.useDevDebugStore().sandboxId;return!e&&r?/^https?:\/\//.test(r)?r:"https://km-dalaran--box-".concat(r,"--api-zhihu-com.zpres.zhihu.com"):oe.API_HOST}(),e},ye=function(){var e=new W.Request({baseURL:oe.API_HOST,timeout:15e3,header:{"X-MINI-TOKEN":oe.MINI_APPID}});return e.interceptors.request.use(fe),e.interceptors.request.use(pe),e.interceptors.response.use(ce,ie.responseInterceptorApiHandle),e.interceptors.response.use(le),e}();t.api=ye,t.printApiLog=de,t.resetUserLoginInfo=function(){var e=Z.useLoginStore(),r=Y.useUserStore(),t=ee.usePurchaseInfo();e.updateAccessToken(),t.resetPurchaseInfo(),r.resetUserInfo()}}));
//# sourceMappingURL=request.js.map