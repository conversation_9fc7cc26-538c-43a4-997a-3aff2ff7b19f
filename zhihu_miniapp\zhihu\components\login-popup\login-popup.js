define("components/login-popup/login-popup.js",(function(e,n,t,o,r,s,u,a,l,i,c,p,d,f,g,m,v,h,P,_,y,b,j,I,T,k,x,w,C,M,A,N,R,L,F,O,D,U,q,B){"use strict";function E(e,n){var t,o,r,s,u={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;u;)try{if(t=1,o&&(r=2&s[0]?o.return:s[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,s[1])).done)return r;switch(o=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return u.label++,{value:s[1],done:!1};case 5:u.label++,o=s[1],s=[0];continue;case 7:s=u.ops.pop(),u.trys.pop();continue;default:if(!(r=u.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){u=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){u.label=s[1];break}if(6===s[0]&&u.label<r[1]){u.label=r[1],r=s;break}if(r&&u.label<r[2]){u.label=r[2],u.ops.push(s);break}r[2]&&u.ops.pop(),u.trys.pop();continue}s=n.call(e,u)}catch(e){s=[6,e],o=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}}var G=function(e,n,t){return new r((function(o,s){var u=function(e){try{l(t.next(e))}catch(e){s(e)}},a=function(e){try{l(t.throw(e))}catch(e){s(e)}},l=function(e){return e.done?o(e.value):r.resolve(e.value).then(u,a)};l((t=t.apply(e,n)).next())}))};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==f?f:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="70e58aec-97cc-4c42-87f3-70158eea6dd7",e._sentryDebugIdIdentifier="sentry-dbid-70e58aec-97cc-4c42-87f3-70158eea6dd7")}catch(e){}}();var H=e("../../common/vendor.js"),S=e("../../utils/appInit.js"),z=e("../../utils/track.js"),Q=e("../../utils/jumpToProtocolPage.js"),V=e("../../utils/forceUpgradeApp.js"),J=e("../../utils/utils.js");e("../../stores/modules/user/index.js"),e("../../stores/modules/history/index.js"),e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js"),e("../../stores/modules/purchaseInfo/index.js"),e("../../stores/modules/clientInfo/index.js"),e("../../stores/modules/systenInfo/index.js");var K=e("../../stores/modules/globalVariable/index.js");e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js");var W=e("../../writeConstant.js");e("../../utils/appFetch.js"),e("../../utils/request.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),e("../../utils/pageHelper.js"),e("../../utils/constant.js"),e("../../utils/onGetUdid.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../pages/manuscript/store.js"),Array||H.resolveComponent("uni-popup")(),Math;var X={__name:"login-popup",props:{sourcePage:{type:String,required:!0},beforePurchase:{type:Boolean,required:!0}},emits:["loginCallback","handleMaskClick"],setup:function(e,n){var t=this,o=n.expose,r=n.emit,s=K.useGlobalVariable(),u=H.ref(!1),a={home:"首页",manuscript:"文稿页",setting:"设置页"},l=H.ref(),i=H.ref(!1),c=H.reactive({code:"",encrypted_data:"",iv:""}),p=function(e){e.stopPropagation()},d=function(){z.track({elementType:"Button",eventType:"Click",moduleId:"login_popup_zh",message:"知乎账号登录按钮点击"}),H.index.navigateTo({url:"/sub_pages/login/login"})},f=function(){i.value=!0},g=function(){i.value?(u.value?(s.setIsQuickLogin(!0),z.track({elementType:"Button",eventType:"Click",message:"".concat(a[e.sourcePage],"快捷登录按钮点击"),moduleId:"".concat(e.sourcePage,"_login")})):z.track({elementType:"Button",eventType:"Click",moduleId:"login_popup_ks",message:"".concat(W.MINI_APP_PLATFORM_CN,"账号授权登录按钮点击")}),H.index.login({success:function(e){var n=e.code;c.code=n}})):H.index.showToast({icon:"none",title:"请同意相关协议"})},m=H.getCurrentInstance(),v=function(e){return G(t,null,(function(){var n,t,o,s,u,a,l,i,p,d,f;return E(this,(function(g){switch(g.label){case 0:return n=this,s=e.detail,u=s.encryptedData,a=s.iv,l=s.errMsg,c.encrypted_data=u,c.iv=a,(null==l?void 0:l.includes("fail"))?(z.track({elementType:"Page",eventType:"Unknown",moduleId:"get_phone_number_err",message:"".concat(W.MINI_APP_PLATFORM_CN,"账号授权登录失败"),extra:{detail:e.detail}}),(null==l?void 0:l.includes("platform auth deny"))||(null==l?void 0:l.includes("no permission"))?(H.index.showToast({icon:"none",title:"此功能暂未开通",success:function(){return G(n,null,(function(){return E(this,(function(e){switch(e.label){case 0:return[4,J.sleep(1e3)];case 1:return e.sent(),h(),[2]}}))}))}}),[3,5]):[3,1]):[3,6];case 1:return(null==l?void 0:l.includes("not login"))?[4,J.sleep(1e3)]:[3,3];case 2:return g.sent(),i=h(),[3,4];case 3:i=H.index.showToast({icon:"none",title:"登录失败",success:function(){return G(n,null,(function(){return E(this,(function(e){switch(e.label){case 0:return[4,J.sleep(1e3)];case 1:return e.sent(),h(),[2]}}))}))}}),g.label=4;case 4:g.label=5;case 5:return[2,void 0];case 6:if(!c.code)return[3,10];g.label=7;case 7:return g.trys.push([7,9,,10]),[4,S.authorizedLogin(c)];case 8:return p=g.sent(),p.data,(null==(d=p.baseResp)?void 0:d.code)&&(null==d?void 0:d.msg)&&(H.index.showToast({icon:"none",title:d.msg,duration:3e3}),z.track({elementType:"Page",eventType:"Unknown",moduleId:"get_phone_number_err",message:"".concat(W.MINI_APP_PLATFORM_CN,"账号授权登录失败"),extra:{errorInfo:d,loginParams:c}})),null==(o=null==(t=m.refs)?void 0:t.popupRef)||o.close(),r("loginCallback"),[3,10];case 9:return f=g.sent(),z.track({elementType:"Page",eventType:"Unknown",moduleId:"get_phone_number_err",message:"".concat(W.MINI_APP_PLATFORM_CN,"账号授权登录失败"),extra:{errorInfo:f,loginParams:c}}),h(),[3,10];case 10:return[2]}}))}))},h=function(){H.index.navigateTo({url:"/sub_pages/login/login"})},P=function(){i.value=!1,u.value&&("home"===e.sourcePage&&V.forceUpgradeApp({pageName:"首页"}),z.track({elementType:"Card",eventType:"Unknown",message:"".concat(a[e.sourcePage],"快捷登录弹窗关闭"),moduleId:"".concat(e.sourcePage,"_login")}))};return o({open:function(e){var n,t;u.value=(null==e?void 0:e.isQuickLogin)||!1,null==(t=null==(n=m.refs)?void 0:n.popupRef)||t.open("bottom")},close:function(){var e,n;u.value=!1,null==(n=null==(e=m.refs)?void 0:e.popupRef)||n.close()}}),function(n,t){return H.e({a:u.value},(u.value,{}),{b:i.value},i.value?{c:H.t("抖音"==H.unref(W.MINI_APP_PLATFORM_CN)?"抖音":""),d:H.o(v),e:H.o(g)}:{f:H.t("抖音"==H.unref(W.MINI_APP_PLATFORM_CN)?"抖音":""),g:H.o(g)},{h:!u.value},u.value?{}:{i:H.o(d)},{j:e.beforePurchase},e.beforePurchase?{k:H.o((function(){return H.unref(Q.jumpToProtocolPage)(4)})),l:H.o((function(){return H.unref(Q.jumpToProtocolPage)(3)})),m:H.o((function(){return H.unref(Q.jumpToProtocolPage)(1)})),n:H.o((function(){return H.unref(Q.jumpToProtocolPage)(6)}))}:{o:H.o((function(){return H.unref(Q.jumpToProtocolPage)(4)})),p:H.o((function(){return H.unref(Q.jumpToProtocolPage)(3)}))},{q:H.t(H.unref(W.MINI_APP_PLATFORM_CN)),r:H.o(f),s:H.sr(l,"67779149-0",{k:"popupRef"}),t:H.o(P),v:H.o(p),w:H.p({"mask-click":!0,animationDuration:300})})}}},Y=H._export_sfc(X,[["__scopeId","data-v-67779149"]]);tt.createComponent(Y)}));
//# sourceMappingURL=login-popup.js.map