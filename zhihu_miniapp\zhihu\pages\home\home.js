define("pages/home/<USER>",(function(e,n,t,r,a,u,o,l,s,i,c,v,d,f,p,h,m,g,y,b,w,T,x,j,k,I,_,S,C,P,D,O,B,U,A,E,H,R,q,F){"use strict";function L(e,n){var t,r,a,u,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return u={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function l(u){return function(l){return function(u){if(t)throw new TypeError("Generator is already executing.");for(;o;)try{if(t=1,r&&(a=2&u[0]?r.return:u[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,u[1])).done)return a;switch(r=0,a&&(u=[2&u[0],a.value]),u[0]){case 0:case 1:a=u;break;case 4:return o.label++,{value:u[1],done:!1};case 5:o.label++,r=u[1],u=[0];continue;case 7:u=o.ops.pop(),o.trys.pop();continue;default:if(!(a=o.trys,(a=a.length>0&&a[a.length-1])||6!==u[0]&&2!==u[0])){o=0;continue}if(3===u[0]&&(!a||u[1]>a[0]&&u[1]<a[3])){o.label=u[1];break}if(6===u[0]&&o.label<a[1]){o.label=a[1],a=u;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(u);break}a[2]&&o.ops.pop(),o.trys.pop();continue}u=n.call(e,o)}catch(e){u=[6,e],r=0}finally{t=a=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,l])}}}var M=Object.defineProperty,G=Object.defineProperties,J=Object.getOwnPropertyDescriptors,K=Object.getOwnPropertySymbols,N=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable,V=function(e,n,t){return n in e?M(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t},z=function(e,n){for(var t in n||(n={}))N.call(n,t)&&V(e,t,n[t]);var r=!0,a=!1,u=void 0;if(K)try{for(var o,l=K(n)[Symbol.iterator]();!(r=(o=l.next()).done);r=!0){t=o.value;Q.call(n,t)&&V(e,t,n[t])}}catch(e){a=!0,u=e}finally{try{r||null==l.return||l.return()}finally{if(a)throw u}}return e},W=function(e,n,t){return new a((function(r,u){var o=function(e){try{s(t.next(e))}catch(e){u(e)}},l=function(e){try{s(t.throw(e))}catch(e){u(e)}},s=function(e){return e.done?r(e.value):a.resolve(e.value).then(o,l)};s((t=t.apply(e,n)).next())}))};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==f?f:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="b74b63b2-0de1-4587-8926-159fce3b127c",e._sentryDebugIdIdentifier="sentry-dbid-b74b63b2-0de1-4587-8926-159fce3b127c")}catch(e){}}();var X=e("../../common/vendor.js"),Y=e("../../utils/track.js"),Z=e("../../utils/forceUpgradeApp.js"),$=e("../../utils/pageHelper.js"),ee=e("../../stores/modules/user/index.js"),ne=e("../../stores/modules/history/index.js");e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js");var te=e("../../stores/modules/koc-ascription/index.js");e("../../stores/modules/purchaseInfo/index.js"),e("../../stores/modules/clientInfo/index.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js");var re=e("../../stores/modules/trackInfo/index.js");e("../../stores/modules/devDebug/index.js");var ae=e("../../utils/appFetch.js"),ue=e("./fetch.js"),oe=e("../../components/novel-plugin/novelPluginJump.js"),le=e("../../utils/image.js");e("../../utils/utils.js"),e("../../writeConstant.js"),e("../../utils/constant.js"),e("../manuscript/store.js"),e("../../utils/request.js"),e("../../utils/appInit.js"),e("../../utils/onGetUdid.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),Array||(X.resolveComponent("tabs")+X.resolveComponent("view-log")+X.resolveComponent("empty"))(),Math||(function(){return"../../components/tabs/tabs.js"}+function(){return"../../components/view-log/view-log.js"}+function(){return"../../components/empty/empty.js"}+se+X.unref(ie)+X.unref(ce))();var se=function(){return"../../components/loading-more/loading-more.js"},ie=function(){return"../../components/novel-plugin/components/charge-dialog/charge-dialog.js"},ce=function(){return"../../components/novel-plugin/components/full-screen/full-screen.js"},ve={__name:"home",setup:function(e){var n=this,t=re.useTrackStore(),r=te.useKocAscriptionStore(),l=ne.useHistoryStore(),s=ee.useUserStore(),i=X.storeToRefs(s).isLogin,c=X.storeToRefs(s).renewTips,v=X.ref(0),d=X.ref(!1),f=X.ref(!0),p=X.ref(""),h=X.ref([]),m=X.ref([]),g=X.ref(!1),y=X.ref(!1),b=X.ref(!1),w=X.ref(!1),T=X.ref(!1),x=X.ref(!1),j=X.ref(),k=X.ref({}),I=X.ref(!0),_=X.ref(!1),S=X.ref(null),C=X.ref(0),P={},D=X.ref("/dalaran/home/<USER>/list");X.watch((function(){return c.value.showUnreadTip}),(function(e){e&&Y.track({elementType:"Button",eventType:"Show",message:"续费提醒「我的」icon曝光"})}),{immediate:!0});var O=X.watch((function(){return i.value}),(function(e,t){return W(n,null,(function(){var n,t,r,a,u;return L(this,(function(o){switch(o.label){case 0:if(!e)return[3,5];o.label=1;case 1:return o.trys.push([1,,4,5]),k.value=(null==(n=l.historyList)?void 0:n[0])||{},(a=(null==(t=k.value)?void 0:t.id)||"")?[4,ae.getGreyList({scene:2,section_ids:[a]})]:[3,3];case 2:(null==(u=o.sent())?void 0:u[a])&&!(null==(r=k.value)?void 0:r.is_finished)&&K(),o.label=3;case 3:return[3,5];case 4:return O&&O(),[7];case 5:return[2]}}))}))}),{immediate:!0}),B=function(e){var n=e.title,t=e.sectionId,r=e.index;Y.track({elementType:"Card",eventType:"Show",elementText:n,moduleIndex:r,moduleId:"feed_item_card",message:"首页feed卡片曝光",extra:{tab_name:p.value||"全部",section_id:t}})},U=function(e){var n=e.sectionId,t=e.wxBookId,r=e.contentType,a=void 0===r?0:r,u="/pages/novel_plugin/index?id=".concat(n,"&bookId=").concat(t,"&content_type=").concat(a);oe.NovelPluginJump({url:u})},A=function(){return W(n,null,(function(){var e,n,t,r;return L(this,(function(a){switch(a.label){case 0:b.value=!0,h.value=[],e=p.value,a.label=1;case 1:return a.trys.push([1,3,4,5]),[4,ue.fetchFeedData("/dalaran/home/<USER>/list?tab=".concat(e))];case 2:return n=a.sent(),t=n.data,r=n.paging,D.value=r.next,h.value=t,T.value=r.isEnd,[3,5];case 3:return a.sent(),h.value=[],[3,5];case 4:return b.value=!1,[7];case 5:return[2]}}))}))},E=function(){Y.track({elementType:"Button",eventType:"Click",message:"续费提醒「我的」icon点击"}),X.index.switchTab({url:"/pages/mine/mine"})},H=function(){Y.track({elementType:"Input",eventType:"Click",moduleId:"home_search_box",message:"首页搜索框点击"}),X.index.navigateTo({url:"/sub_pages/search/search"})},R=function(e){P={},b.value=!1,x.value=!1,T.value=!1,p.value=e,d.value&&X.index.pageScrollTo({scrollTop:v.value,duration:0,success:function(){d.value=!1}}),A()},q=function(){var e;null==(e=X.index.createSelectorQuery().select(".tabs-wrap"))||e.boundingClientRect((function(e){e&&(null==e?void 0:e.top)>=0&&(v.value=parseInt(null==e?void 0:e.top))})).exec()},F=function(){Y.track({elementType:"Card",eventType:"Click",moduleId:"continue-reading-card",message:"继续阅读卡片点击",extra:{section_id:k.value.id}});var e=k.value,n=e.id,t=e.wxBookId,r=e.contentType;U({sectionId:n,wxBookId:t,contentType:r})},M=function(){I.value=!1,u((function(){_.value=!1,o(S.value)}),300)},K=function(){_.value=!0,I.value=!0,Y.track({elementType:"Card",eventType:"Show",moduleId:"continue-reading-card",message:"继续阅读卡片曝光",extra:{section_id:k.value.id}}),S.value=u((function(){M()}),3e3)};return X.onLoad((function(e){j.value=e;var u=e.shareId,o=void 0===u?"":u,l=e.isShare,s=void 0!==l&&l;s&&t.updateTrackInfo({shareId:o,isShare:!!s}),W(n,null,(function(){return L(this,(function(e){return a.all([A(),W(n,null,(function(){var e;return L(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,ue.fetchTabData()];case 1:return e=n.sent(),m.value=e,[3,3];case 2:return n.sent(),[3,3];case 3:return[2]}}))}))]).then((function(){g.value=!0,y.value=!1,b.value=!1,q()})),[2]}))})),r.updateKocAscription({page_query:z(z({},r.page_query),e)}),W(n,null,(function(){return L(this,(function(e){return i.value&&Z.forceUpgradeApp({pageName:"首页"}),[2]}))}))})),X.onShow((function(){return W(n,null,(function(){var e,n,r,a,u;return L(this,(function(o){return f.value=!0,e=j.value,n=e.shareId,r=void 0===n?"":n,a=e.isShare,(u=void 0!==a&&a)&&t.updateTrackInfo({shareId:r,isShare:!!u}),Y.track({elementType:"Page",eventType:"Show",message:"首页曝光"}),[2]}))}))})),X.onHide((function(){f.value=!1})),X.onShareAppMessage((function(){return Y.track({elementType:"Button",eventType:"Click",message:"首页分享按钮点击"}),$.getAppShareMessage()})),X.onPullDownRefresh((function(){return W(n,null,(function(){return L(this,(function(e){switch(e.label){case 0:if(y.value)return[3,5];y.value=!0,P={},X.index.pageScrollTo({scrollTop:0,success:function(){d.value=!1}}),e.label=1;case 1:return e.trys.push([1,3,4,5]),[4,A()];case 2:return e.sent(),[3,5];case 3:return e.sent(),[3,5];case 4:return b.value=!1,x.value=!1,y.value=!1,d.value=!1,X.index.stopPullDownRefresh(),[7];case 5:return[2]}}))}))})),X.onReachBottom((function(){T.value||b.value||!D.value||W(n,null,(function(){var e,n,t;return L(this,(function(r){switch(r.label){case 0:if(P[D.value])return[3,5];P[D.value]=D.value,w.value=!0,x.value=!0,r.label=1;case 1:return r.trys.push([1,3,4,5]),[4,ue.fetchFeedData(D.value)];case 2:return e=r.sent(),n=e.data,t=e.paging,D.value=t.next,h.value=h.value.concat(n),T.value=t.isEnd,[3,5];case 3:return r.sent(),X.index.showToast({title:"加载失败，请重试",icon:"none"}),[3,5];case 4:return w.value=!1,[7];case 5:return[2]}}))}))})),X.onPageScroll((function(e){C.value=parseInt(e.scrollTop),v.value<36&&q();var n=v.value>0&&C.value>=v.value;if(d.value===n)return!1;d.value=n})),X.onTabItemTap((function(){Y.track({elementText:"推荐",elementType:"Button",eventType:"Click",message:"推荐icon点击"})})),function(e,n){return X.e({a:X.unref(c).showUnreadTip},X.unref(c).showUnreadTip?{b:X.o(E)}:{},{c:X.o(H),d:m.value.length},m.value.length?{e:X.o(R),f:X.p({list:m.value,type:"0",isFixedTop:d.value})}:{},{g:b.value},(b.value,{}),{h:h.value.length},h.value.length?{i:X.f(h.value,(function(e,n,t){var r,a,u,o,l;return X.e({a:X.t(e.title),b:X.t(e.content),c:e.artwork},e.artwork?{d:X.unref(le.getOptimalImageUrl)(e.artwork)}:{},{e:null==(r=null==e?void 0:e.labels)?void 0:r.length},(null==(a=null==e?void 0:e.labels)?void 0:a.length)?{f:X.f(null==(u=e.labels)?void 0:u.slice(0,2),(function(e,n,t){return{a:X.t(n?" · ":""),b:X.t(e),c:n}}))}:{},{g:"5aadaf2e-1-"+t,h:X.p({data:(o=z({},e),l={index:n},G(o,J(l)))}),i:n,j:X.o((function(){return function(e){var n=e.title,t=e.sectionId,r=e.index,a=e.wxBookId,u=e.contentType;Y.track({elementType:"Card",eventType:"Click",elementText:n,moduleIndex:r,moduleId:"feed_item_card",message:"首页feed卡片点击",extra:{tab_name:p.value,section_id:t}}),U({sectionId:t,wxBookId:a,contentType:u})}(e)}))})})),j:X.o(B)}:{},{k:!h.value.length&&!b.value},h.value.length||b.value?b.value||!x.value&&!T.value?{}:{n:X.p({"is-loading-more":w.value,"no-more-data":T.value})}:{l:X.p({text:"内容为空"})},{m:!b.value&&(x.value||T.value),o:_.value&&X.unref(i)},_.value&&X.unref(i)?X.e({p:k.value.artwork},k.value.artwork?{q:k.value.artwork}:{},{r:X.t(k.value.title),s:X.o(M),t:X.n("continue-reading "+(I.value?"fade-in":"fade-out")),v:X.o(F)}):{},{},{})}}},de=X._export_sfc(ve,[["__scopeId","data-v-5aadaf2e"]]);ve.__runtimeHooks=3,tt.createPage(de)}));
//# sourceMappingURL=home.js.map