define("uni_modules/uni-swipe-action/components/uni-swipe-action/uni-swipe-action.js",(function(e,n,t,i,o,c,s,r,u,d,f,a,h,l,b,p,m,I,y,_,g,v,w,D,E,j,A,k,x,z,C,O,S,q,B,F,G,H,J,K){"use strict";!function(){try{var e=void 0!==f?f:"undefined"!=typeof global?global:void 0!==l?l:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="e74695c2-4e10-499b-86c0-c55b20b400e5",e._sentryDebugIdIdentifier="sentry-dbid-e74695c2-4e10-499b-86c0-c55b20b400e5")}catch(e){}}();var L=e("../../../../common/vendor.js")._export_sfc({name:"uniSwipeAction",data:function(){return{}},created:function(){this.children=[]},methods:{resize:function(){this.children.forEach((function(e){e.init()}))},closeAll:function(){this.children.forEach((function(e){e.close()}))},closeOther:function(e){this.openItem&&this.openItem!==e&&this.openItem.close(),this.openItem=e}}},[["render",function(e,n,t,i,o,c){return{}}]]);tt.createComponent(L)}));
//# sourceMappingURL=uni-swipe-action.js.map