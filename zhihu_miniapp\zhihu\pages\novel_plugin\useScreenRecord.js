define("pages/novel_plugin/useScreenRecord.js",(function(e,n,t,r,o,s,i,u,c,a,d,l,f,b,y,p,v,m,x,h,k,g,S,j,U,w,I,R,T,F,D,P,_,C,E,G,L,M,V,q){"use strict";function z(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}function A(e,n){var t,r,o,s,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return s={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function u(s){return function(u){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;i;)try{if(t=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return i.label++,{value:s[1],done:!1};case 5:i.label++,r=s[1],s=[0];continue;case 7:s=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){i=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){i.label=s[1];break}if(6===s[0]&&i.label<o[1]){i.label=o[1],o=s;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(s);break}o[2]&&i.ops.pop(),i.trys.pop();continue}s=n.call(e,i)}catch(e){s=[6,e],r=0}finally{t=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}}!function(){try{var e=void 0!==d?d:"undefined"!=typeof global?global:void 0!==b?b:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="b1030a2b-438e-4b1d-98b6-936868bad32c",e._sentryDebugIdIdentifier="sentry-dbid-b1030a2b-438e-4b1d-98b6-936868bad32c")}catch(e){}}();var B=e("../../common/vendor.js"),H=e("../../utils/utils.js"),J=e("../../utils/track.js");e("../../stores/modules/user/index.js"),e("../../stores/modules/history/index.js"),e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js"),e("../../stores/modules/purchaseInfo/index.js"),e("../../stores/modules/clientInfo/index.js");var K=e("../../stores/modules/systenInfo/index.js");e("../../stores/modules/globalVariable/index.js"),e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js"),t.useScreenRecord=function(e,n){var r=K.useSystemInfoStore(),s=B.ref(!1),i=function(){J.track({elementType:"Page",eventType:"Unknown",message:"文稿页截屏",extra:{sectionId:e.value,title:n.value}})},u=function(){var t=B.index;"object"==("undefined"==typeof tt?"undefined":z(tt))?t=tt:"object"==("undefined"==typeof ks?"undefined":z(ks))&&(t=ks),r.isIos?H.isFunction(t.onUserScreenRecord)&&t.onUserScreenRecord((function(t){!function(t){var r={sectionId:e.value,title:n.value};"start"===t.state?(J.track({elementType:"Page",eventType:"Unknown",message:"文稿页开始录屏",extra:r}),s.value=!0):"end"!==t.state&&"stop"!==t.state||(J.track({elementType:"Page",eventType:"Unknown",message:"文稿页结束录屏",extra:r}),s.value=!1)}(t)})):H.isFunction(t.disableUserScreenRecord)&&t.disableUserScreenRecord()};return B.onLoad((function(){!function(){return e=t,n=function(){return A(this,(function(e){switch(e.label){case 0:return[4,H.sleep(2e3)];case 1:e.sent(),u();try{r.isIos&&H.isFunction(B.index.onUserCaptureScreen)&&B.index.onUserCaptureScreen(i)}catch(e){J.track({elementType:"Page",eventType:"Unknown",message:"文稿页截屏事件监听失败",extra:{errorInfo:e}})}return[2]}}))},new o((function(t,r){var s=function(e){try{u(n.next(e))}catch(e){r(e)}},i=function(e){try{u(n.throw(e))}catch(e){r(e)}},u=function(e){return e.done?t(e.value):o.resolve(e.value).then(s,i)};u((n=n.apply(e,null)).next())}));var e,n}()})),B.onUnload((function(){!function(){var e=B.index;"object"==("undefined"==typeof tt?"undefined":z(tt))?e=tt:"object"==("undefined"==typeof ks?"undefined":z(ks))&&(e=ks),r.isIos?(H.isFunction(e.offUserScreenRecord)&&e.offUserScreenRecord(),H.isFunction(B.index.offUserCaptureScreen)&&B.index.offUserCaptureScreen(i)):H.isFunction(e.enableUserScreenRecord)&&e.enableUserScreenRecord()}()})),{showProtectMask:s}}}));
//# sourceMappingURL=useScreenRecord.js.map