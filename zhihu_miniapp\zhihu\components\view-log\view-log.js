define("components/view-log/view-log.js",(function(e,t,n,r,o,a,l,c,i,u,s,d,f,v,b,y,p,w,g,m,h,I,O,j,_,D,S,C,P,R,V,x,E,M,k,A,B,T,q,z){"use strict";var F=Object.defineProperty,G=Object.getOwnPropertySymbols,H=Object.prototype.hasOwnProperty,J=Object.prototype.propertyIsEnumerable,K=function(e,t,n){return t in e?F(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n};!function(){try{var e=void 0!==s?s:"undefined"!=typeof global?global:void 0!==v?v:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="07ae453c-4c34-40c7-8c34-0d8050f74b1e",e._sentryDebugIdIdentifier="sentry-dbid-07ae453c-4c34-40c7-8c34-0d8050f74b1e")}catch(e){}}();var L=e("../../common/vendor.js");Array||L.resolveComponent("slot-wrap")(),Math;var N={__name:"view-log",props:{data:{default:{}},once:{type:Boolean,default:!0}},emits:["handleViewShow"],setup:function(e,t){var n=t.emit,r=e,o=L.ref(null),a=L.toRefs(r),l=a.once,c=a.data;return L.onMounted((function(){var e;o.value=L.getCurrentInstance(),null==(e=L.index.createIntersectionObserver(o.value))||e.relativeToViewport(),null==e||e.observe(".slot-content",(function(t){t.intersectionRatio>0&&t.dataset.data&&(n("handleViewShow",function(e,t){for(var n in t||(t={}))H.call(t,n)&&K(e,n,t[n]);var r=!0,o=!1,a=void 0;if(G)try{for(var l,c=G(t)[Symbol.iterator]();!(r=(l=c.next()).done);r=!0)n=l.value,J.call(t,n)&&K(e,n,t[n])}catch(e){o=!0,a=e}finally{try{r||null==c.return||c.return()}finally{if(o)throw a}}return e}({},t.dataset.data)),l.value&&e.disconnect())}))})),function(e,t){return{a:L.unref(c),b:L.sr("abRef","4c3f2d6f-0")}}}};tt.createComponent(N)}));
//# sourceMappingURL=view-log.js.map