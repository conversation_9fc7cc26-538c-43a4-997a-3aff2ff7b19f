define("uni_modules/uni-popup/components/uni-popup/uni-popup.js",(function(t,o,i,s,n,e,a,r,p,l,h,u,c,f,d,m,g,k,y,b,C,w,x,v,P,A,T,D,S,I,M,j,H,_,B,$,W,O,z,E){"use strict";function V(t,o){(null==o||o>t.length)&&(o=t.length);for(var i=0,s=new Array(o);i<o;i++)s[i]=t[i];return s}function N(t){return function(t){if(Array.isArray(t))return V(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,o){if(!t)return;if("string"==typeof t)return V(t,o);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(i);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return V(t,o)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}!function(){try{var t=void 0!==h?h:"undefined"!=typeof global?global:void 0!==f?f:{},o=(new Error).stack;o&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[o]="ad3ba2d7-c595-4fe7-9705-88640889cb16",t._sentryDebugIdIdentifier="sentry-dbid-ad3ba2d7-c595-4fe7-9705-88640889cb16")}catch(t){}}();var U=t("../../../../common/vendor.js"),q={name:"uniPopup",components:{},emits:["change","maskClick"],props:{animation:{type:Boolean,default:!0},animationDuration:{type:Number,default:300},type:{type:String,default:"center"},isMaskClick:{type:Boolean,default:null},maskClick:{type:Boolean,default:null},backgroundColor:{type:String,default:"none"},safeArea:{type:Boolean,default:!0},maskBackgroundColor:{type:String,default:"rgba(0, 0, 0, 0.4)"}},watch:{type:{handler:function(t){this.config[t]&&this[this.config[t]](!0)},immediate:!0},isDesktop:{handler:function(t){this.config[t]&&this[this.config[this.type]](!0)},immediate:!0},maskClick:{handler:function(t){this.mkclick=t},immediate:!0},isMaskClick:{handler:function(t){this.mkclick=t},immediate:!0},showPopup:function(t){}},data:function(){return{duration:300,ani:[],showPopup:!1,showTrans:!1,popupWidth:0,popupHeight:0,config:{top:"top",bottom:"bottom",center:"center",left:"left",right:"right",message:"top",dialog:"center",share:"bottom"},maskClass:{position:"fixed",bottom:0,top:0,left:0,right:0,backgroundColor:"rgba(0, 0, 0, 0.4)"},transClass:{position:"fixed",left:0,right:0},maskShow:!0,mkclick:!0,popupstyle:this.isDesktop?"fixforpc-top":"top"}},computed:{isDesktop:function(){return this.popupWidth>=500&&this.popupHeight>=500},bg:function(){return""===this.backgroundColor||"none"===this.backgroundColor?"transparent":this.backgroundColor}},mounted:function(){var t=this;!function(){var o=U.index.getSystemInfoSync(),i=o.windowWidth,s=o.windowHeight,n=o.windowTop,e=o.safeArea,a=(o.screenHeight,o.safeAreaInsets);t.popupWidth=i,t.popupHeight=s+(n||0),e&&t.safeArea?t.safeAreaInsets=a.bottom:t.safeAreaInsets=0}()},unmounted:function(){this.setH5Visible()},created:function(){null===this.isMaskClick&&null===this.maskClick?this.mkclick=!0:this.mkclick=null!==this.isMaskClick?this.isMaskClick:this.maskClick,this.animation?this.duration=this.animationDuration||300:this.duration=0,this.messageChild=null,this.clearPropagation=!1,this.maskClass.backgroundColor=this.maskBackgroundColor},methods:{setH5Visible:function(){},closeMask:function(){this.maskShow=!1},disableMask:function(){this.mkclick=!1},clear:function(t){t.stopPropagation(),this.clearPropagation=!0},open:function(t){this.showPopup||(t&&-1!==["top","center","bottom","left","right","message","dialog","share"].indexOf(t)||(t=this.type),this.config[t]&&(this[this.config[t]](),this.$emit("change",{show:!0,type:t})))},close:function(t){var o=this;this.showTrans=!1,this.$emit("change",{show:!1,type:this.type}),a(this.timer),this.timer=e((function(){o.showPopup=!1}),100)},touchstart:function(){this.clearPropagation=!1},onTap:function(){this.clearPropagation?this.clearPropagation=!1:(this.$emit("maskClick"),this.mkclick&&this.close())},top:function(t){var o=this;this.popupstyle=this.isDesktop?"fixforpc-top":"top",this.ani=["slide-top"],this.transClass={position:"fixed",left:0,right:0,backgroundColor:this.bg},t||(this.showPopup=!0,this.showTrans=!0,this.$nextTick((function(){o.messageChild&&"message"===o.type&&o.messageChild.timerClose()})))},bottom:function(t){this.popupstyle="bottom",this.ani=["slide-bottom"],this.transClass={position:"fixed",left:0,right:0,bottom:0,backgroundColor:this.bg,transition:"all 200ms linear 0ms"},t||(this.showPopup=!0,this.showTrans=!0)},center:function(t){this.popupstyle="center",this.ani=["zoom-out","fade"],this.transClass={position:"fixed",display:"flex",flexDirection:"column",bottom:0,left:0,right:0,top:0,justifyContent:"center",alignItems:"center"},t||(this.showPopup=!0,this.showTrans=!0)},left:function(t){this.popupstyle="left",this.ani=["slide-left"],this.transClass={position:"fixed",left:0,bottom:0,top:0,backgroundColor:this.bg,display:"flex",flexDirection:"column"},t||(this.showPopup=!0,this.showTrans=!0)},right:function(t){this.popupstyle="right",this.ani=["slide-right"],this.transClass={position:"fixed",bottom:0,right:0,top:0,backgroundColor:this.bg,display:"flex",flexDirection:"column"},t||(this.showPopup=!0,this.showTrans=!0)}}};Array||U.resolveComponent("uni-transition")(),Math;var F=U._export_sfc(q,[["render",function(t,o,i,s,n,e){var a,r;return U.e({a:n.showPopup},n.showPopup?U.e({b:n.maskShow},n.maskShow?{c:U.o(e.onTap),d:U.p({customClass:"uni-popup-custom-1",name:"mask","mode-class":"fade",styles:n.maskClass,duration:n.duration,show:n.showTrans})}:{},{e:e.bg,f:U.n(n.popupstyle),g:U.o((function(){for(var t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];return e.clear&&(a=e).clear.apply(a,N(o))})),h:U.o(e.onTap),i:U.p({"mode-class":n.ani,customClass:"uni-popup-custom-2",name:"content",styles:n.transClass,duration:n.duration,show:n.showTrans}),j:U.o((function(){for(var t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];return e.touchstart&&(r=e).touchstart.apply(r,N(o))})),k:U.n(n.popupstyle),l:U.n(e.isDesktop?"fixforpc-z-index":"")}):{})}]]);tt.createComponent(F)}));
//# sourceMappingURL=uni-popup.js.map