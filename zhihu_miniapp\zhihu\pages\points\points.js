define("pages/points/points.js",(function(e,t,n,r,o,a,i,u,s,l,c,d,f,p,v,h,m,b,y,x,g,k,T,j,w,A,C,S,_,I,P,E,D,U,L,$,B,H,M,Y){"use strict";function q(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function O(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a=[],i=!0,u=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(a.push(r.value),!t||a.length!==t);i=!0);}catch(e){u=!0,o=e}finally{try{i||null==n.return||n.return()}finally{if(u)throw o}}return a}}(e,t)||V(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(e){return function(e){if(Array.isArray(e))return q(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||V(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(e,t){if(e){if("string"==typeof e)return q(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?q(e,t):void 0}}function G(e,t){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function u(a){return function(u){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=i.trys,(o=o.length>0&&o[o.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}}!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==p?p:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="616dd6ef-217f-4803-b464-860e19ee4168",e._sentryDebugIdIdentifier="sentry-dbid-616dd6ef-217f-4803-b464-860e19ee4168")}catch(e){}}();var N=e("../../common/vendor.js"),W=e("../../utils/constant.js"),F=e("../../utils/utils.js"),z=e("../../hooks/useRewardedVideoAd.js");e("../../stores/modules/user/index.js"),e("../../stores/modules/history/index.js"),e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js"),e("../../stores/modules/purchaseInfo/index.js"),e("../../stores/modules/clientInfo/index.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js");var J=e("../../stores/modules/trackInfo/index.js");e("../../stores/modules/devDebug/index.js");var K=e("./trace.js"),Q=e("./fetch.js");e("../../writeConstant.js"),e("../../utils/appFetch.js"),e("../../utils/request.js"),e("../../utils/appInit.js"),e("../../utils/onGetUdid.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),e("../../utils/pageHelper.js"),e("../manuscript/store.js"),e("../../utils/track.js"),Math||(X+Z)();var X=function(){return"../../commonPackage/components/exchange-section/exchange-section.js"},Z=function(){return"../../commonPackage/components/pay-tips/pay-tips.js"},ee={__name:"points",setup:function(e){var t=this,n=this,r="",a=N.ref({}),i=J.useTrackStore(),u=N.ref(!1),s=N.ref({package:[],appletPackage:[]}),l=N.ref({}),c=N.computed((function(){var e,t;return(null==(t=null==(e=l.value)?void 0:e.tasks)?void 0:t[0])||{}})),d=N.computed((function(){return(c.value.subTaskComplete||0)>=(c.value.subTaskLimit||0)})),f=N.computed((function(){var e=c.value.desc||"";if(!e)return{beforeText:"",pointText:"",afterText:""};var t=e.match(/^(.*?)(\d+\s*积分)(.*)$/);return t?{beforeText:t[1],pointText:t[2],afterText:t[3]}:{beforeText:e,pointText:"",afterText:""}})),p=N.computed((function(){var e=c.value.buttonUrl;if(!e||!e.startsWith("ad://"))return null;try{var t=e.split("?")[1];return t?new URLSearchParams(t).get("adUnitId")||e.split("?")[1].split("=")[1]:null}catch(t){return null}})),v=z.useRewardedVideoAd((function(){"pages/points/points"===getCurrentPages()[0].route&&Q.completeAdTask({task_id:c.value.id,ts:Math.floor(Date.now()/1e3),once:r}).then((function(e){var t,n;g(!1),N.index.showToast({title:"获得 ".concat((null==(t=null==e?void 0:e.points)?void 0:t.amount)||""," 积分"),icon:"none"}),K.trackAdComplete((null==(n=null==e?void 0:e.points)?void 0:n.amount)||0)})).catch((function(e){N.index.showToast({title:"完成任务失败，请稍后再试",icon:"none"})}))}),K.trackAdError),h=v.initAd,m=v.showAd,b=F.throttle((function(){var e;if(r=F.randomID(1),K.trackTaskClick((null==(e=c.value)?void 0:e.subTaskComplete)||0),d.value)N.index.showToast({title:"今日任务完成数量到达上限",icon:"none"});else{var t=c.value.buttonUrl;t&&t.startsWith("ad://")&&m().catch((function(e){var t=(null==e?void 0:e.message)||"广告播放失败";158886===(null==e?void 0:e.errorCode)&&(t="活动太火爆了，请稍后再来"),N.index.showToast({title:t,icon:"none"}),K.trackAdError(e)}))}}),1e3),y=function(){K.trackIncomeClick(a.value),N.index.navigateTo({url:"/sub_pages/points/income"})},x=function(){K.trackExchangeClick(a.value),N.index.navigateTo({url:"/sub_pages/points/exchange"})},g=function(){var e,t,r=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return e=n,t=function(){return G(this,(function(e){return r&&N.index.showLoading({title:"加载中"}),o.allSettled([Q.getBalance(),Q.getPointsSkus(),Q.getPointsTasks()]).then((function(e){var t,n=O(e.map((function(e){return e.value}))||[],3),r=n[0],o=n[1],i=n[2];a.value=(null==r?void 0:r.data)||{},s.value=(null==o?void 0:o.payment)||[],l.value=(null==(t=null==i?void 0:i.data)?void 0:t[0])||{}})).then((function(){p.value&&h(p.value),u.value||(K.trackTaskShow(a.value),u.value=!0)})).finally((function(){r&&N.index.hideLoading()})),[2]}))},new o((function(n,r){var a=function(e){try{u(t.next(e))}catch(e){r(e)}},i=function(e){try{u(t.throw(e))}catch(e){r(e)}},u=function(e){return e.done?n(e.value):o.resolve(e.value).then(a,i)};u((t=t.apply(e,null)).next())}))};return N.onShow((function(){i.updatePageId("60748"),g(!1),K.handlePageShow(),N.index.$on(W.PAY_SUCCESS_CB,g),N.index.$on(W.PAY_CANCEL_CB,g)})),N.onHide((function(){N.index.$off(W.PAY_SUCCESS_CB,g),N.index.$off(W.PAY_CANCEL_CB,g)})),function(e,n){var r=t;return N.e({a:N.t(a.value.points),b:N.o(y),c:N.t(a.value.cashDeduction),d:N.o(y),e:N.o(x),f:N.t(N.unref(c).title),g:N.unref(f).beforeText},N.unref(f).beforeText?{h:N.t(N.unref(f).beforeText)}:{},{i:N.unref(f).pointText},N.unref(f).pointText?{j:N.t(N.unref(f).pointText)}:{},{k:N.unref(f).afterText},N.unref(f).afterText?{l:N.t(N.unref(f).afterText)}:{},{m:N.t(N.unref(c).subTaskComplete),n:N.t(N.unref(c).subTaskLimit),o:N.t(N.unref(c).buttonText),p:N.unref(d)?1:"",q:N.o((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return N.unref(b)&&N.unref(b).apply(r,R(t))})),r:N.p({title:"兑换体验会员",tip:"仅当前小程序可用",data:s.value.appletPackage}),s:N.p({title:"兑换盐选会员",tip:"与「知乎APP」 权益互通",data:s.value.package})})}}},te=N._export_sfc(ee,[["__scopeId","data-v-4c1a9f1d"]]);tt.createPage(te)}));
//# sourceMappingURL=points.js.map