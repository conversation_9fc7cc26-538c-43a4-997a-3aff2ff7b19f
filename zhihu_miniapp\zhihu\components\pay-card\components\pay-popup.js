define("components/pay-card/components/pay-popup.js",(function(e,s,o,t,n,i,r,d,u,l,p,a,c,y,j,m,f,g,v,h,P,_,I,x,b,k,q,w,D,C,T,B,H,E,L,U,F,G,M,O){"use strict";!function(){try{var e=void 0!==p?p:"undefined"!=typeof global?global:void 0!==y?y:{},s=(new Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="15ed091e-12f8-4278-8945-dd1d635df29d",e._sentryDebugIdIdentifier="sentry-dbid-15ed091e-12f8-4278-8945-dd1d635df29d")}catch(e){}}();var S=e("../../../common/vendor.js"),V=e("../../../utils/track.js");e("../../../stores/modules/user/index.js"),e("../../../utils/appFetch.js"),e("../../../utils/request.js"),e("../../../stores/modules/history/index.js"),e("../../../stores/modules/login/index.js"),e("../../../utils/constant.js"),e("../../../stores/modules/search-history/index.js"),e("../../../stores/modules/koc-ascription/index.js"),e("../../../stores/modules/purchaseInfo/index.js"),e("../../../pages/manuscript/store.js"),e("../../../stores/modules/clientInfo/index.js"),e("../../../utils/utils.js"),e("../../../writeConstant.js"),e("../../../stores/modules/systenInfo/index.js"),e("../../../stores/modules/globalVariable/index.js"),e("../../../stores/modules/trackInfo/index.js"),e("../../../stores/modules/devDebug/index.js"),e("../../../utils/appInit.js"),e("../../../utils/onGetUdid.js"),e("../../../utils/launchOptionsHelper.js"),e("../../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../../utils/encryptHandler.js"),e("../../../utils/requstErrorMiddlewares.js"),e("../../../utils/pageHelper.js");var z={__name:"pay-popup",props:{type:{type:String,required:!0},show:{type:Boolean,required:!0},closePayPopup:{type:t,required:!0},showPayLoadingPopup:{type:t,required:!0}},setup:function(e){var s=e,o=function(e){e.preventDefault()},t=function(){var e;V.track({elementType:"Block",eventType:"Click",moduleId:"vipmini_inquiry_popup",message:"还没有"}),s.closePayPopup&&(null==(e=s.closePayPopup)||e.call(s))},n=function(){var e;V.track({elementType:"Block",eventType:"Click",moduleId:"vipmini_inquiry_popup",message:"是的"}),null==(e=null==s?void 0:s.showPayLoadingPopup)||e.call(s)};return S.onUnmounted((function(){var e;s.closePayPopup&&(null==(e=s.closePayPopup)||e.call(s))})),function(s,i){return S.e({a:e.show},e.show?S.e({b:"dialog"==e.type},"dialog"==e.type?{c:S.o(t),d:S.o(t),e:S.o(n)}:{},{f:"loading"==e.type},"loading"==e.type?{g:S.t("支付查询中,请稍后···")}:{},{h:S.o(o)}):{})}}},A=S._export_sfc(z,[["__scopeId","data-v-c165ffde"]]);tt.createComponent(A)}));
//# sourceMappingURL=pay-popup.js.map