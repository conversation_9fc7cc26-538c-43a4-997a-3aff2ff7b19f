define("hooks/useSendGift.js",(function(t,e,r,n,i,a,o,u,c,s,l,f,y,d,p,b,h,g,v,m,w,_,I,j,O,S,G,k,P,A,E,T,x,D,C,M,q,L,R,K){"use strict";function U(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function B(t){return function(t){if(Array.isArray(t))return U(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return U(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return U(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(t,e){var r,n,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function u(a){return function(u){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,n&&(i=2&a[0]?n.return:a[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,a[1])).done)return i;switch(n=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,n=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=e.call(t,o)}catch(t){a=[6,t],n=0}finally{r=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,u])}}}var z=Object.defineProperty,F=Object.defineProperties,H=Object.getOwnPropertyDescriptors,J=Object.getOwnPropertySymbols,N=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable,V=function(t,e,r){return e in t?z(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r},W=function(t,e,r){return new i((function(n,a){var o=function(t){try{c(r.next(t))}catch(t){a(t)}},u=function(t){try{c(r.throw(t))}catch(t){a(t)}},c=function(t){return t.done?n(t.value):i.resolve(t.value).then(o,u)};c((r=r.apply(t,e)).next())}))};!function(){try{var t=void 0!==l?l:"undefined"!=typeof global?global:void 0!==d?d:{},e=(new Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="cd51de87-7baa-48cb-b7c1-b9900ac93288",t._sentryDebugIdIdentifier="sentry-dbid-cd51de87-7baa-48cb-b7c1-b9900ac93288")}catch(t){}}();var X=t("../utils/request.js"),Y=t("../utils/gift.js"),Z=t("../common/vendor.js"),tt=t("../utils/payment.js"),et=t("../utils/commonPay.js");function rt(t,e){return t||e}r.useSendGift=function(t){var e=this,r=t.giftAggregationId,n=t.autoGetGiftList,i=void 0!==n&&n,a=t.tansformGiftList,o=void 0===a?rt:a,u=t.beforeCheckstand,c=void 0===u?function(t){return t}:u,s=this,l=Z.isRef(i)?i:Z.ref(i),f=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return W(s,B(e),(function(){var t,e,r,n,i,a=arguments;return $(this,(function(u){switch(u.label){case 0:t=a.length>0&&void 0!==a[0]?a[0]:{},u.label=1;case 1:return u.trys.push([1,3,,4]),[4,function(t){return W(this,null,(function(){return $(this,(function(e){return[2,X.api.get("/order/v2/gift/panel",{params:t})]}))}))}((n=function(t,e){for(var r in e||(e={}))N.call(e,r)&&V(t,r,e[r]);var n=!0,i=!1,a=void 0;if(J)try{for(var o,u=J(e)[Symbol.iterator]();!(n=(o=u.next()).done);n=!0){r=o.value;Q.call(e,r)&&V(t,r,e[r])}}catch(t){i=!0,a=t}finally{try{n||null==u.return||u.return()}finally{if(i)throw a}}return t}({},t),i={panel_key:Y.getPanelKeyToGift(),panel_type:Y.getPanelKeyToGift(),ordering_rule:"CUSTOM"},F(n,H(i))))];case 2:return e=u.sent(),[2,o(e)];case 3:return r=u.sent(),[2,o(r)];case 4:return[2]}}))}))};return Z.watchEffect((function(){return W(e,null,(function(){return $(this,(function(t){switch(t.label){case 0:return l.value?[4,f()]:[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}}))}))})),{getGiftList:f,sendGift:function(t){return W(e,[t],(function(t){var e,n,i,a,o,u,s,l;return $(this,(function(f){switch(f.label){case 0:e=t.skuId,n=t.quantity,i=void 0===n?1:n,a=t.giftId,o=t.giftAggregationId,f.label=1;case 1:return f.trys.push([1,4,,5]),[4,function(t){return W(this,null,(function(){return $(this,(function(e){return[2,X.api.post("/order/v2/gift/purchase",t)]}))}))}({sku_data:{sku_id:e,quantity:i},wallet_id:"1010",payment:{payment_channel:Y.getPaymentChannelToGift()},gift_properties:{gift_purchase_scene:Y.getScenceToGift(),gift_aggregation_id:o||r,type_of_recipient:"MEMBER",gift_id:a}})];case 2:return u=f.sent(),u=c(u)||u,[4,tt.pay(u.paymentOutParams,{dealId:u.dealId,isRenew:!1,iosParams:{imId:"1110282023",immediate:!1}})];case 3:return f.sent(),[3,5];case 4:if(s=f.sent(),!(l=et.formatError(s)).message.includes("cancel"))throw l;return[3,5];case 5:return[2]}}))}))}}}}));
//# sourceMappingURL=useSendGift.js.map