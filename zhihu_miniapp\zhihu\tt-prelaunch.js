var prelaunchConfig={appId:"tt9d69ebd4025fd5ea01"},TMAConfig={};define("tt-prelaunch.js",(function(n,e,t,o,r,i,a,c,u,l,f,d,s,g,p,h,v,y,S,m,_,O,k,C,w,T,x,A,N,q,D,E,I,L,P,b,J,M,U,G,H,z,j){"use strict";!function(){var n="__DC_STAT_UUID",e=function(n){return"pre_init:".concat(n)},t=function(){return new r((function(n,t){s.log("log =>  ~ initLoginCodeAsync => ");var o=function(){f.login({success:function(o){s.log("login success ->",o),o.isLogin&&o.code?(function(n,t){var o=Date.now();f.setStorage({key:e("login_code"),data:JSON.stringify({data:t,expire:o+3e4})})}(0,o.code),n({type:1,code:o.code})):t(new Error("get login code fail"))},fail:function(n){s.error("login fail ->",n),t(n)}})};f.getStorage({key:"login",success:function(e){var t=e.data?JSON.parse(e.data):{};s.log("log => ~ LOGIN_CACHE_KEY - loginCache:",t);var r=t.accessToken,i=function(n,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:18e5;return!n||!e||Date.now()>e-t}(r,t.expiresTime);s.log("log =>  ~ initLoginCodeAsync ~ isExpired:",i),i?o():n({type:2,token:r})},fail:function(n){s.error("initLoginCodeAsync getStorage fail ->",n),o()}})}))},o=function(n){return"https://api.zhihu.com".concat(n)},i=function(n){var e={"x-mini-token":"tt9d69ebd4025fd5ea01","X-Mini-App-Version":"1.10.0"};return n&&(e.authorization=n),e};function a(n,e){var t=n.split("&"),o=!0,r=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(o=(a=c.next()).done);o=!0){var u=a.value,l=u.indexOf("=");if((-1===l?u:u.substring(0,l))===e){var f=-1===l?"":u.substring(l+1);try{return decodeURIComponent(f.replace(/\+/g," "))}catch(n){return f}}}}catch(n){r=!0,i=n}finally{try{o||null==c.return||c.return()}finally{if(r)throw i}}return null}var c=function(n){var e=a(n,"id"),t=a(n,"content_type");return s.log("预取文稿:id---\x3e",e),new r((function(n,r){f.request({method:"POST",url:o("/dalaran/manuscript/truncate_section"),data:{payload:e,content_type:t||"0"},id:"manuscript_prefetch",header:i(),success:function(e){s.log("预取文稿页接口成功",e),n(e)},fail:function(n){s.error("预取文稿页接口失败",n),r(n)}})}))};d("app",(function(e){e.path,e.query;f.getStorage({key:n,success:function(e){var t=e.data?JSON.parse(e.data):null;s.log("log =>  ~ registerOnPrelaunch ~ deviceId:",t),t||f.setStorage({key:n,data:Date.now()+""+Math.floor(1e7*Math.random())})},fail:function(n){s.error("getStorage fail ->",n)}}),new r((function(n,e){var t=function(){f.request({method:"POST",url:o("/wx-minapp-account/udid"),id:"udid_prefetch",success:function(t){var o;s.log("预取 udid 结果 ->",t);var r=null==(o=t.data)?void 0:o.udid;r?n(r):e(new Error("udid 接口获取失败"))},fail:function(n){s.error("预取 udid 失败 ->",n),e(n)}})};f.getStorage({key:"clientInfo",success:function(n){var e=(n.data?JSON.parse(n.data):{}).udid;s.log("本地 udid ->",e),e||t()},fail:function(n){s.error("getStorage fail ->",n),t()}})})),function(){try{var n=f.getLaunchOptionsSync()||{};return s.log("预取 launchOptions 结果 ->",n),f.setStorage({key:"ZH_LAUNCH_OPTIONS",data:JSON.stringify(n),success:function(){s.log("启动参数缓存成功")},fail:function(n){s.error("存储启动参数失败 ->",n)}}),n}catch(n){return s.error("预取启动参数失败 ->",n),null}}();var a={loginCode:t()}.loginCode;new r((function(n,e){f.request({method:"GET",url:o("/dalaran/common/config"),id:"common-config_prefetch",header:i(),success:function(e){s.log("预取通用配置成功",e),n(e)},fail:function(n){s.error("预取通用配置失败",n),e(n)}})})),a.then((function(n){var e;s.log("log =>  ~ registerOnPrelaunch ~ loginData:",n),2===n.type&&n.token&&(s.log("log =>  ~ registerOnPrelaunch ~ loginData.token:",n.token),e=n.token,new r((function(n,t){f.request({method:"GET",url:o("/dalaran/user_info"),id:"user-info_prefetch",header:i(e),success:function(e){s.log("预取用户信息成功",e),n(e)},fail:function(n){s.error("预取用户信息失败",n),t(n)}})})))}))})),d("pages/manuscript/manuscript",(function(n){var e=n.query;c(e)})),d("pages/novel_plugin/index",(function(n){var e=n.query;c(e)}))}()}));
//# sourceMappingURL=tt-prelaunch.js.map