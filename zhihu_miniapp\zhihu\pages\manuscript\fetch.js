define("pages/manuscript/fetch.js",(function(t,n,e,r,a,i,c,u,o,s,l,f,p,d,y,h,b,v,g,_,w,m,I,S,x,C,D,k,E,T,j,z,F,R,L,M,P,G,q,A){"use strict";function B(t,n){var e,r,a,i,c={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(e)throw new TypeError("Generator is already executing.");for(;c;)try{if(e=1,r&&(a=2&i[0]?r.return:i[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,i[1])).done)return a;switch(r=0,a&&(i=[2&i[0],a.value]),i[0]){case 0:case 1:a=i;break;case 4:return c.label++,{value:i[1],done:!1};case 5:c.label++,r=i[1],i=[0];continue;case 7:i=c.ops.pop(),c.trys.pop();continue;default:if(!(a=c.trys,(a=a.length>0&&a[a.length-1])||6!==i[0]&&2!==i[0])){c=0;continue}if(3===i[0]&&(!a||i[1]>a[0]&&i[1]<a[3])){c.label=i[1];break}if(6===i[0]&&c.label<a[1]){c.label=a[1],a=i;break}if(a&&c.label<a[2]){c.label=a[2],c.ops.push(i);break}a[2]&&c.ops.pop(),c.trys.pop();continue}i=n.call(t,c)}catch(t){i=[6,t],r=0}finally{e=a=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}}var N=function(t,n,e){return new a((function(r,i){var c=function(t){try{o(e.next(t))}catch(t){i(t)}},u=function(t){try{o(e.throw(t))}catch(t){i(t)}},o=function(t){return t.done?r(t.value):a.resolve(t.value).then(c,u)};o((e=e.apply(t,n)).next())}))};!function(){try{var t=void 0!==l?l:"undefined"!=typeof global?global:void 0!==d?d:{},n=(new Error).stack;n&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[n]="30fba6de-7357-451b-bcd6-165b4c9a2d8a",t._sentryDebugIdIdentifier="sentry-dbid-30fba6de-7357-451b-bcd6-165b4c9a2d8a")}catch(t){}}();var Q=t("../../utils/request.js"),U=t("../../writeConstant.js"),O=t("../../utils/constant.js");e.callLottery=function(){return N(e,null,(function(){return B(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,Q.api.post("/dalaran/lottery/start")];case 1:return[2,t.sent()];case 2:return t.sent(),[3,3];case 3:return[2]}}))}))},e.createGuideCode=function(t){return N(e,[t],(function(t){var n,e,r,a,i;return B(this,(function(c){switch(c.label){case 0:n=t.sectionId,e=t.contentType,r=void 0===e?0:e,a=t.leadQuery,i=void 0===a?"":a,c.label=1;case 1:return c.trys.push([1,3,,4]),[4,Q.api.post("/dalaran/content/guide_code",{section_id:n,content_type:String(r),lead_query:i})];case 2:return[2,c.sent()];case 3:return c.sent(),[3,4];case 4:return[2]}}))}))},e.getBenefitConfig=function(){return N(e,null,(function(){return B(this,(function(t){switch(t.label){case 0:return[4,Q.api.get("/dalaran/april_fools_day/benefit_config")];case 1:return[2,t.sent()]}}))}))},e.getCommentList=function(t){return N(e,[t],(function(t){var n,e,r;return B(this,(function(a){switch(a.label){case 0:n=t.sectionId,e=t.contentType,r=void 0===e?0:e,a.label=1;case 1:return a.trys.push([1,3,,4]),[4,Q.api.get("/dalaran/content/comments",{params:{section_id:n,content_type:String(r)}})];case 2:return[2,a.sent()];case 3:return a.sent(),[3,4];case 4:return[2]}}))}))},e.getContentRetention=function(){return N(e,null,(function(){return B(this,(function(t){switch(t.label){case 0:return[4,Q.api.get("/dalaran/manuscript/retention_popup/content")];case 1:return[2,t.sent()]}}))}))},e.getLotteryInfo=function(){return N(e,null,(function(){var t,n,e;return B(this,(function(r){switch(r.label){case 0:return[4,Q.api.get("/dalaran/cancel_payment/activity")];case 1:return e=r.sent(),[2,((null==(n=null==(t=null==e?void 0:e.data)?void 0:t.lottery)?void 0:n.prizes)&&(e.data.lottery.prizes=e.data.lottery.prizes.map((function(t){return{id:t.id,fonts:[{text:t.desc,top:"50%",fontSize:"38.15rpx",fontColor:"#ED5848"},{text:t.name,top:"10%",fontSize:"30.52rpx",fontColor:"#FFB700"}],background:"#FFF9ED"}}))),e)]}}))}))},e.getManuscript=function(t){return N(e,[t],(function(t){var n,e,r;return B(this,(function(a){switch(a.label){case 0:return n=t.contentId,e=t.contentType,r=void 0===e?0:e,[4,Q.api.post("/dalaran/manuscript/encrypt_section",{id:n,content_type:String(r)},{custom:{useSignature:!0,isReport:!0}})];case 1:return[2,a.sent()]}}))}))},e.getOrderRetention=function(){return N(e,null,(function(){return B(this,(function(t){switch(t.label){case 0:return[4,Q.api.get("/dalaran/manuscript/retention_popup/purchase")];case 1:return[2,t.sent()]}}))}))},e.preLoadManuscript=function(t){return N(e,[t],(function(t){var n,e,r;return B(this,(function(a){switch(a.label){case 0:n=t.contentId,e=t.contentType,r=void 0===e?0:e,a.label=1;case 1:return a.trys.push([1,3,,4]),[4,Q.api.post("/dalaran/manuscript/truncate_section",{payload:n,content_type:String(r)},{id:O.GET_MANUSCRIPT_REQUEST_ID,usePrefetchCache:!0})];case 2:return[2,a.sent()];case 3:return a.sent(),[3,4];case 4:return[2]}}))}))},e.receiveWelfare=function(t){return N(e,null,(function(){return B(this,(function(n){switch(n.label){case 0:return[4,Q.api.post("/dalaran/april_fools_day/receive_benefit",{benefit_type:t})];case 1:return[2,n.sent()]}}))}))},e.trackDeviceEvent=function(t){return N(e,null,(function(){return B(this,(function(n){return[2,Q.api.get("/api/dalaran-nodejs/open/track/device-info",{params:{channel_id:U.MINI_APPID,content_id:t}}).catch((function(){}))]}))}))}}));
//# sourceMappingURL=fetch.js.map