define("stores/modules/search-history/index.js",(function(e,s,t,r,i,n,o,a,c,d,f,h,y,u,l,b,H,g,L,v,I,p,D,S,_,m,j,k,w,x,A,E,q,z,B,C,F,G,<PERSON>,<PERSON>){"use strict";!function(){try{var e=void 0!==f?f:"undefined"!=typeof global?global:void 0!==u?u:{},s=(new Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="dfb17872-add7-45be-9af5-83f77a50e61f",e._sentryDebugIdIdentifier="sentry-dbid-dfb17872-add7-45be-9af5-83f77a50e61f")}catch(e){}}();var M=e("../../../common/vendor.js"),N={searchHistoryList:[]},O=M.defineStore("searchHistory",{state:function(){return N},getters:{},actions:{addSearchHistoryList:function(e){if(e=e.replace(/\s*/g,"")){var s=[e].concat(this.searchHistoryList.filter((function(s){return s!==e})));this.searchHistoryList=s,this.searchHistoryList.length>=20&&this.searchHistoryList.splice(20)}},clearAllHistory:function(){this.searchHistoryList=[]}},persist:{enabled:!0}});t.useSearchHistoryStore=O}));
//# sourceMappingURL=index.js.map