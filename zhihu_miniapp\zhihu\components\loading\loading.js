define("components/loading/loading.js",(function(n,e,t,a,o,d,r,i,s,u,c,f,b,g,l,y,p,_,D,I,m,v,M,h,j,k,w,C,E,L,q,x,z,A,B,F,G,H,J,<PERSON>){"use strict";!function(){try{var n=void 0!==c?c:"undefined"!=typeof global?global:void 0!==g?g:{},e=(new Error).stack;e&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[e]="7276488a-fdb9-4a6f-a695-127a8b5d8a6c",n._sentryDebugIdIdentifier="sentry-dbid-7276488a-fdb9-4a6f-a695-127a8b5d8a6c")}catch(n){}}();tt.createComponent({__name:"loading",props:{isLoadingMore:{},noMoreData:{}},setup:function(n){return function(n,e){return{}}}})}));
//# sourceMappingURL=loading.js.map