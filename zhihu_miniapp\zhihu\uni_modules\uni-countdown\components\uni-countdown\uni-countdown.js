define("uni_modules/uni-countdown/components/uni-countdown/uni-countdown.js",(function(t,e,n,o,i,s,r,u,a,d,c,h,l,f,m,p,y,g,w,S,b,x,D,C,v,T,_,F,I,N,z,M,k,j,U,B,W,q,E,H){"use strict";!function(){try{var t=void 0!==c?c:"undefined"!=typeof global?global:void 0!==f?f:{},e=(new Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="43d89fac-d6ae-4074-97c9-c6a8fed5c804",t._sentryDebugIdIdentifier="sentry-dbid-43d89fac-d6ae-4074-97c9-c6a8fed5c804")}catch(t){}}();var R=t("../../../../common/vendor.js"),V=t("./i18n/index.js"),$=R.initVueI18n(V.messages).t,A={name:"UniCountdown",emits:["timeup"],props:{showDay:{type:Boolean,default:!0},showColon:{type:Boolean,default:!0},start:{type:Boolean,default:!0},backgroundColor:{type:String,default:""},color:{type:String,default:"#333"},timeWidth:{type:String,default:""},fontSize:{type:Number,default:14},borderColor:{type:String,default:""},splitorMargin:{type:Number,default:2},splitorColor:{type:String,default:"#333"},day:{type:Number,default:0},hour:{type:Number,default:0},minute:{type:Number,default:0},second:{type:Number,default:0},timestamp:{type:Number,default:0}},data:function(){return{timer:null,syncFlag:!1,d:"00",h:"00",i:"00",s:"00",leftTime:0,seconds:0}},computed:{dayText:function(){return $("uni-countdown.day")},hourText:function(t){return $("uni-countdown.h")},minuteText:function(t){return $("uni-countdown.m")},secondText:function(t){return $("uni-countdown.s")},timeStyle:function(){var t=this,e=t.color,n=t.backgroundColor,o=t.fontSize,i=t.borderColor,s=t.timeWidth;return{color:e,backgroundColor:n,boxSizing:"border-box",border:i?"0.5px solid ".concat(i):"none",fontSize:"".concat(o,"px"),width:s||24*o/14+"px",height:22*o/14+"px",lineHeight:22*o/14+"px",borderRadius:4*o/14+"px"}},splitorStyle:function(){var t=this,e=t.splitorColor,n=t.fontSize,o=t.splitorMargin;return{color:e,fontSize:12*n/14+"px",margin:o>-1?"".concat(o,"px"):""}}},watch:{day:function(t){this.changeFlag()},hour:function(t){this.changeFlag()},minute:function(t){this.changeFlag()},second:function(t){this.changeFlag()},start:{immediate:!0,handler:function(t,e){if(t)this.startData();else{if(!e)return;a(this.timer)}}}},created:function(t){this.seconds=this.toSeconds(this.timestamp,this.day,this.hour,this.minute,this.second),this.countDown()},unmounted:function(){a(this.timer)},methods:{toSeconds:function(t,e,n,o,i){return t?t-parseInt((new Date).getTime()/1e3,10):60*e*60*24+60*n*60+60*o+i},timeUp:function(){a(this.timer),this.$emit("timeup")},countDown:function(){var t=this.seconds,e=0,n=0,o=0,i=0;t>0?(e=Math.floor(t/86400),n=Math.floor(t/3600)-24*e,o=Math.floor(t/60)-24*e*60-60*n,i=Math.floor(t)-24*e*60*60-60*n*60-60*o):this.timeUp(),e<10&&(e="0"+e),n<10&&(n="0"+n),o<10&&(o="0"+o),i<10&&(i="0"+i),this.d=e,this.h=n,this.i=o,this.s=i},startData:function(){var t=this;if(this.seconds=this.toSeconds(this.timestamp,this.day,this.hour,this.minute,this.second),this.seconds<=0)return this.seconds=this.toSeconds(0,0,0,0,0),void this.countDown();a(this.timer),this.countDown(),this.timer=u((function(){t.seconds--,t.seconds<0?t.timeUp():t.countDown()}),1e3)},update:function(){this.startData()},changeFlag:function(){this.syncFlag||(this.seconds=this.toSeconds(this.timestamp,this.day,this.hour,this.minute,this.second),this.startData(),this.syncFlag=!0)}}},G=R._export_sfc(A,[["render",function(t,e,n,o,i,s){return R.e({a:n.showDay},n.showDay?{b:R.t(i.d),c:R.s(s.timeStyle)}:{},{d:n.showDay},n.showDay?{e:R.t(s.dayText),f:R.s(s.splitorStyle)}:{},{g:R.t(i.h),h:R.s(s.timeStyle),i:R.t(n.showColon?":":s.hourText),j:R.s(s.splitorStyle),k:R.t(i.i),l:R.s(s.timeStyle),m:R.t(n.showColon?":":s.minuteText),n:R.s(s.splitorStyle),o:R.t(i.s),p:R.s(s.timeStyle),q:!n.showColon},n.showColon?{}:{r:R.t(s.secondText),s:R.s(s.splitorStyle)})}],["__scopeId","data-v-52575053"]]);tt.createComponent(G)}));
//# sourceMappingURL=uni-countdown.js.map