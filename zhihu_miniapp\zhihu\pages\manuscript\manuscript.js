define("pages/manuscript/manuscript.js",(function(e,n,t,r,u,o,a,i,l,s,c,v,p,f,d,m,h,y,g,_,T,b,I,k,w,x,C,P,S,j,R,A,L,O,M,q,E,B,F,N){"use strict";function U(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function D(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,u,o=[],a=!0,i=!1;try{for(t=t.call(e);!(a=(r=t.next()).done)&&(o.push(r.value),!n||o.length!==n);a=!0);}catch(e){i=!0,u=e}finally{try{a||null==t.return||t.return()}finally{if(i)throw u}}return o}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return U(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return U(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(e,n){var t,r,u,o,a={label:0,sent:function(){if(1&u[0])throw u[1];return u[1]},trys:[],ops:[]};return o={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function i(o){return function(i){return function(o){if(t)throw new TypeError("Generator is already executing.");for(;a;)try{if(t=1,r&&(u=2&o[0]?r.return:o[0]?r.throw||((u=r.return)&&u.call(r),0):r.next)&&!(u=u.call(r,o[1])).done)return u;switch(r=0,u&&(o=[2&o[0],u.value]),o[0]){case 0:case 1:u=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(u=a.trys,(u=u.length>0&&u[u.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!u||o[1]>u[0]&&o[1]<u[3])){a.label=o[1];break}if(6===o[0]&&a.label<u[1]){a.label=u[1],u=o;break}if(u&&a.label<u[2]){a.label=u[2],a.ops.push(o);break}u[2]&&a.ops.pop(),a.trys.pop();continue}o=n.call(e,a)}catch(e){o=[6,e],r=0}finally{t=u=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,i])}}}var z=Object.defineProperty,H=Object.defineProperties,G=Object.getOwnPropertyDescriptors,Y=Object.getOwnPropertySymbols,X=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable,J=function(e,n,t){return n in e?z(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t},V=function(e,n){for(var t in n||(n={}))X.call(n,t)&&J(e,t,n[t]);var r=!0,u=!1,o=void 0;if(Y)try{for(var a,i=Y(n)[Symbol.iterator]();!(r=(a=i.next()).done);r=!0){t=a.value;W.call(n,t)&&J(e,t,n[t])}}catch(e){u=!0,o=e}finally{try{r||null==i.return||i.return()}finally{if(u)throw o}}return e},Q=function(e,n){return H(e,G(n))},K=function(e,n,t){return new u((function(r,o){var a=function(e){try{l(t.next(e))}catch(e){o(e)}},i=function(e){try{l(t.throw(e))}catch(e){o(e)}},l=function(e){return e.done?r(e.value):u.resolve(e.value).then(a,i)};l((t=t.apply(e,n)).next())}))};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==f?f:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="5357576b-33a8-440e-af25-084ab4bad690",e._sentryDebugIdIdentifier="sentry-dbid-5357576b-33a8-440e-af25-084ab4bad690")}catch(e){}}();var Z=e("../../common/vendor.js"),ee=e("../../utils/track.js"),ne=e("../../stores/modules/user/index.js"),te=e("../../stores/modules/history/index.js"),re=e("../../stores/modules/login/index.js");e("../../stores/modules/search-history/index.js");var ue=e("../../stores/modules/koc-ascription/index.js"),oe=e("../../stores/modules/purchaseInfo/index.js");e("../../stores/modules/clientInfo/index.js");var ae=e("../../stores/modules/systenInfo/index.js"),ie=e("../../stores/modules/globalVariable/index.js"),le=e("../../stores/modules/trackInfo/index.js");e("../../stores/modules/devDebug/index.js");var se=e("../../utils/constant.js"),ce=e("../../utils/pageHelper.js"),ve=e("./fetch.js"),pe=e("../../utils/utils.js"),fe=e("../../utils/jumpToProtocolPage.js"),de=e("../../utils/onGetUdid.js"),me=e("./hooks.js"),he=e("./useScreenRecord.js"),ye=e("./store.js"),ge=e("../../hooks/usePayment.js"),_e=e("../../components/lead-generation/index.js");e("../../utils/appFetch.js"),e("../../utils/request.js"),e("../../writeConstant.js"),e("../../utils/appInit.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),e("../../utils/showToast.js"),e("../../utils/payUtils.js"),e("../../components/pay-card/fetch.js"),Array||(Z.resolveComponent("empty")+Z.resolveComponent("article-content")+Z.resolveComponent("viwe")+Z.resolveComponent("view-log")+Z.resolveComponent("lead-generation")+Z.resolveComponent("pay-part-read-page")+Z.resolveComponent("loading")+Z.resolveComponent("login-popup"))(),Math||(function(){return"../../components/empty/empty.js"}+function(){return"../../components/article-content/article-content.js"}+be+function(){return"../../components/view-log/view-log.js"}+we+function(){return"../../components/lead-generation/lead-generation.js"}+function(){return"../../components/pay-part-read-page/pay-part-read-page.js"}+function(){return"../../components/loading/loading.js"}+Pe+xe+function(){return"../../components/login-popup/login-popup.js"}+Se+ke+Te+Ce+Ie+Z.unref(je))();var Te=function(){return"../../components/pay-card/components/pay-popup.js"},be=function(){return"../../components/pay-card/components/promotion-card.js"},Ie=function(){return"../../commonPackage/components/confirm-modal/confirm-modal.js"},ke=function(){return"../../commonPackage/components/content-retention-popup/content-retention-popup.js"},we=function(){return"../../commonPackage/components/reward/reward.js"},xe=function(){return"../../commonPackage/components/comment-list/comment-list.js"},Ce=function(){return"../../commonPackage/components/member-expire-popup/member-expire-popup.js"},Pe=function(){return"../../commonPackage/components/desktop-shortcut/desktop-shortcut.js"},Se=function(){return"../../commonPackage/components/pay-retention-popup/pay-retention-popup.js"},je=function(){return"../../commonPackage/components/lucky-wheel/lucky-wheel.js"},Re={__name:"manuscript",setup:function(e){var n=this,t=Z.ref(null),r=Z.ref(null),u=Z.ref(null),a=Z.ref(!1),s=Z.ref(!1),c=Z.ref(!1),v=Z.ref(!1),p=Z.ref({}),f=Z.ref(!1),d=Z.ref([{radius:"91rpx",imgs:[{src:"https://picx.zhimg.com/v2-3717434440d6e45e56788822ea3511da.png",width:"182rpx",height:"210rpx",top:"-105rpx"}]}]),m=Z.ref([{padding:"56rpx",imgs:[{src:"https://picx.zhimg.com/v2-48442a8cfb04990f6413db63e1330610.png",width:"100%",height:"100%"}]},{background:"#FFD171"}]),h=te.useHistoryStore(),y=le.useTrackStore(),g=re.useLoginStore(),_=ue.useKocAscriptionStore(),T=ie.useGlobalVariable(),b=oe.usePurchaseInfo(),I=ne.useUserStore(),k=ae.useSystemInfoStore(),w=ye.useManuscriptStore(),x=Z.storeToRefs(T),C=(x.isFromBuyPage,x.isFromManuscriptPaySuccess),P=x.isQuickLogin,S=x.desktop,j=x.iosDefaultTruncate,R=x.iosConfigTruncate,A=Z.storeToRefs(g).isFromLoginPage,L=Z.storeToRefs(w),O=L.ascribeZxhAdid,M=L.hasPermission,q=L.prizeList,E=Z.storeToRefs(b),B=E.isPaymentPending,F=E.clickedSkuId,N=E.manuscriptPanelStyle,U=E.skuList,z=E.appletPackage,H=E.serverNowTime,G=E.couponAnnualCardInfo,Y=E.dyAnnualCardSkuId,X=E.isReceivedWelfare,W=Z.storeToRefs(I),J=W.isLogin,Te=W.isVip,be=W.isGuest,Ie=Z.ref({}),ke=Z.ref(""),we=Z.ref(""),xe=Z.ref(!1),Ce=Z.ref(""),Pe=Z.ref(!1),Se=Z.ref(!1),je=Z.ref(!1),Re=Z.ref(),Ae=Z.ref(""),Le=Z.ref(!0),Oe=Z.getCurrentInstance(),Me=Z.ref(!1),qe=Z.ref(!1),Ee=Z.ref(!1),Be=Z.ref(0),Fe=Z.ref([]),Ne=Z.ref(!1),Ue=Z.ref({}),De=Z.ref(null),$e=Z.ref(null),ze=Z.ref({}),He=Z.ref(""),Ge=D(_e.useLeadGeneration({contentType:Be,sectionId:ke,leadQuery:He,isLogin:J}),2),Ye=Ge[0],Xe=Ge[1],We=Z.computed((function(){return we.value&&Ye.value&&Pe.value})),Je=Z.computed((function(){return Q(V({},Xe),{showShadow:!0})})),Ve=Z.computed((function(){return{bpConfig:function(e){var n=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).data;return{moduleId:"cut_text_drainage",extra:V({mini_content_type:Be.value,section_id:ke.value},"click"===e?{drainage_query:n,search_source:JSON.stringify({search_query:_.search_query,share_query:_.share_query,search_timestamp:_.time_stamp,attributes:{koc:_.koc_query}}),mini_page_params:JSON.stringify(V({},_.page_query))}:{})}}}})),Qe=Z.ref({type:"loading",isPayPopup:!1}),Ke=Z.reactive({isPayFinished:!1});qe.value=!0;var Ze=null,en="",nn=0,tn={},rn=0,un=0,on=0,an=!1,ln=!0,sn=null,cn=Z.ref(""),vn=Z.ref(!1),pn=Z.computed((function(){var e=Ie.value||{},n=e.title,t=void 0===n?"":n,r=e.titleType,u=(tn||{}).share_query;if(!u)return t;switch(r){case 0:default:return t;case 1:return t?"".concat(u," (别名：").concat(t,")"):"";case 2:return u}}));Z.watch(pn,(function(){T.setArticleTitle(pn.value)}));var fn=Z.computed((function(){return vn.value&&Ne.value&&Pe.value&&!X.value&&!cn.value})),dn=function(){var e,n;null==(n=null==(e=Oe.refs)?void 0:e.moreCommentModalRef)||n.open(),mn()},mn=function(){return K(n,null,(function(){var e,n,t;return $(this,(function(r){switch(r.label){case 0:return[4,ve.createGuideCode({sectionId:ke.value,contentType:Be.value})];case 1:return e=r.sent(),n=(null==e?void 0:e.code)||pn.value,Z.index.setClipboardData({data:n,success:function(){}}),t={sectionId:ke.value,miniContentType:Be.value,drainage_query:n,page_params:JSON.stringify(V({},_.page_query)),search_source:JSON.stringify({search_query:_.search_query,share_query:_.share_query,search_timestamp:_.time_stamp,attributes:{koc:_.koc_query}})},ee.track({elementType:"Card",eventType:"Click",message:"文稿页文末引流主站弹窗曝光",extra:t}),[2]}}))}))},hn=ge.usePayment(Ke,Qe,!0),yn=hn.startPay,gn=hn.closePayPopup,_n=hn.showPayLoadingPopup,Tn=Z.computed((function(){return"android"===Rn.value&&!O.value&&!Te.value})),bn=Z.computed((function(){return Tn.value&&M.value})),In=function(){f.value=!1},kn=function(e){var n=e.requestRes;p.value={requestRes:n},ee.track({elementType:"Button",eventType:"Click",message:"文稿页取消支付",extra:{requestRes:n}}),gn(),bn.value&&(f.value=!0)},wn=function(){return K(n,null,(function(){var e;return $(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,ve.callLottery()];case 1:return[2,(0===(e=n.sent()).code&&ve.getLotteryInfo().then((function(e){var n=e.code;w.setHasPermission(0===n)})),e)];case 2:return[2,{code:-1,message:n.sent().message}];case 3:return[2]}}))}))},xn=function(){return K(n,null,(function(){return $(this,(function(e){return Tn.value&&ve.getLotteryInfo().then((function(e){var n=e.data,t=e.code,r=((n||{}).lottery||{}).prizes,u=void 0===r?[]:r;w.setHasPermission(0===t),w.setPrizeList(u.length?u:q.value)})),[2]}))}))},Cn=function(e){var n=q.value.map((function(n){return n.id===e.id?Q(V({},n),{background:"#FFDF94"}):n}));b.setPrizeSkuId(e.skuId),w.setPrizeList(n),En(0)};me.useSetNavigationBarTitle(pn);var Pn=he.useScreenRecord(ke,pn).showProtectMask,Sn=Z.storeToRefs(k),jn=Sn.screenHeight,Rn=Sn.osName;me.useTrackDeviceEvent();var An=function(){var e,n;null==(n=null==(e=Oe.refs)?void 0:e.loginPopupRef)||n.open(),ee.track({elementType:"Block",eventType:"Click",message:"游客点击购买卡片登录弹窗曝光",extra:{sectionId:ke.value}})},Ln=!1;Z.onMounted((function(){y.updatePageId("60348"),Z.index.$on("manuscript_get_sku_info",On),Z.index.$on("zh_login_event_before_purchase",An)})),Z.onUnmounted((function(){y.updatePageId(""),Z.index.$off("manuscript_get_sku_info",On),Z.index.$off("zh_login_event_before_purchase",An),Z.index.$off(se.PAY_CANCEL_CB,kn)})),Z.watch([function(){return Re},function(){return J.value}],(function(e){var n=D(e,2);n[0];if(n[1]){un=jn.value/3*2;var t=h.historyList.find((function(e){return e.id===ke.value})),r=(null==t?void 0:t.scrollTop)||0;o((function(){var e=Z.index.createIntersectionObserver(Oe);null==e||e.relativeToViewport(),null==e||e.observe(".rich-text",(function(e){var n=e.boundingClientRect;if(rn=n.height,n.height&&!an){var t=r;t&&Z.index.pageScrollTo({scrollTop:t,duration:100,success:function(){an=!0,Z.index.showToast({icon:"none",title:"定位至上次阅读"})}})}}))}),300)}}),{deep:!0,immediate:!0}),Z.watch((function(){return J.value}),(function(e){return K(n,null,(function(){var n;return $(this,(function(t){switch(t.label){case 0:return e?(I.updateUserInfo(),[4,ve.getBenefitConfig()]):[3,2];case 1:n=t.sent(),Ue.value=n.contentBottomPromotion,t.label=2;case 2:return[2]}}))}))}),{immediate:!0}),Z.watch((function(){return be.value}),(function(e,t){return K(n,null,(function(){return $(this,(function(n){switch(n.label){case 0:return t&&!ln&&J.value&&!e?(Z.index.$emit(se.PAY_SUCCESS_CB),A.value&&(Z.index.showToast({title:"登录成功"}),g.setIsFromLoginPage(!1)),[4,En(0)]):[3,2];case 1:n.sent(),Bn(),n.label=2;case 2:return[2]}}))}))}),{immediate:!0}),Z.watch((function(){var e;return null==(e=G.value)?void 0:e.coupon}),(function(e,t){return K(n,null,(function(){return $(this,(function(n){return Ee.value=!!e,[2]}))}))}),{immediate:!0}),Z.watch((function(){return J.value}),(function(e,t){return K(n,null,(function(){var n,t,r,u;return $(this,(function(o){switch(o.label){case 0:return e?ln?(Kn(tn),[3,4]):[3,1]:[3,4];case 1:return o.trys.push([1,3,,4]),[4,ve.getManuscript({contentId:ke.value,contentType:Be.value})];case 2:return n=o.sent(),t=n.manuscriptInfo,r=n.manuscriptContent,u=r.data,we.value=u.replace(/<p[^>]*>/g,'<p class="custom-p">'),Pe.value=t.isFold,!Ln&&En(1),I.updateUserInfo(),[3,4];case 3:return o.sent(),[3,4];case 4:return[2]}}))}))}));var On=function(){return K(n,null,(function(){return $(this,(function(e){switch(e.label){case 0:return[4,pe.sleep(500)];case 1:return e.sent(),En(1),[2]}}))}))},Mn=function(){ee.track({elementType:"Card",eventType:"Show",message:"文末活动卡片曝光"})},qn=function(){var e;Ke.sku_id=null==(e=Ue.value)?void 0:e.skuId,ee.track({elementType:"Card",eventType:"Click",message:"文末活动卡片点击立即购买"}),yn()},En=function(e){return K(n,null,(function(){return $(this,(function(n){switch(n.label){case 0:return[4,b.getPayCards({ignoreCoupon:e,ignoreSingle:ke.value?0:1,contentId:ke.value})];case 1:return n.sent(),[2]}}))}))},Bn=function(){P.value,T.setIsQuickLogin(!1),!Te.value&&F.value&&Z.index.$emit("login_success_continue_pay")},Fn=function(){},Nn=function(){v.value=!0},Un=function(){v.value=!1};Z.onShareAppMessage((function(){ee.track({elementType:"Button",eventType:"Click",message:"文稿页分享按钮点击",extra:{sectionId:ke.value,miniContentType:Be.value}});var e="/pages/novel_plugin/index?id=".concat(ke.value,"&content_type=").concat(Be.value),n="继续观看《".concat(en,"》"),t=tn||{},r=t.is_koc,u=t.search_query,o=u&&"1"===r?u:"",a=o?"&share_query=".concat(o):"";return Ae.value&&(a="&share_query=".concat(Ae.value),n="继续观看《".concat(Ae.value,"》")),o&&(n="继续观看《".concat(o,"》")),a&&(e="".concat(e).concat(a)),e="".concat(e,"&utm_source=ttugc"),ce.getAppShareMessage({path:e,title:n})})),de.onGetUdid((function(e){return K(n,null,(function(){return $(this,(function(n){return ee.track({elementType:"Page",eventType:"Show",message:"文稿页曝光",extra:{jump_source:null==e?void 0:e.jump_source,jump_source_id:null==e?void 0:e.jump_source_id,sectionId:ke.value,miniContentType:Be.value}}),[2]}))}))})),Z.onLoad((function(e){tn=e,ke.value=null==e?void 0:e.id,Ae.value=(null==e?void 0:e.share_query)||"",cn.value=(null==e?void 0:e.zxh_adid)||"",He.value=(null==e?void 0:e.search_query)||(null==e?void 0:e.query)||(null==e?void 0:e.share_query)||"",Be.value=Number((null==e?void 0:e.content_type)||"0"),Kn(e,!J.value),0!==Be.value&&Dn(),vn.value=!0,function(){var e=tn||{},n=e.share_query,t=void 0===n?"":n,r=e.search_query,u=void 0===r?"":r,o=e.is_koc,a=void 0===o?"":o,i=e.isShare,l=void 0===i?"":i,s=e.type,c=void 0===s?"":s,v=e.utm_division,p=void 0===v?"":v,f=e.clickid,d=void 0===f?"":f,m=e.demand_id,h=void 0===m?"":m,y="1"===a,g=_.order_source,T=(new Date).getTime();l||"link"===c||p||d||h?g.link_timestamp=T:u&&y?g.koc_timestamp=T:g.section_timestamp=T,_.updateKocAscription(Q(V({},t&&{share_query:t}),{page_query:V(V({},_.page_query),tn),order_source:g}))}(),cn.value&&(w.setAscribeZxhAdid(cn.value),w.setAscribeSectionId(ke.value)),w.setLastSectionId(ke.value),Z.index.$on(se.PAY_SUCCESS_CB,(function(n){return function(e){return K(this,null,(function(){return $(this,(function(n){return C.value&&(Z.index.showToast({title:"购买成功"}),T.setIsFromManuscriptPaySuccess(!1)),Kn(e),I.updateUserInfo(),En(1),Vn(),b.setIsReceivedWelfare(!0),"xima"===(null==e?void 0:e.benefitType)&&Zn(),[2]}))}))}(Q(V({},e),{benefitType:null==n?void 0:n.benefitType}))})),Z.index.$on(se.PAY_CANCEL_CB,kn)}));var Dn=function(){return K(n,null,(function(){var e;return $(this,(function(n){switch(n.label){case 0:return[4,ve.getCommentList({sectionId:ke.value,contentType:Be.value})];case 1:return e=n.sent(),ze.value=e,[2]}}))}))};Z.onShow((function(){return K(n,null,(function(){var e,n,t,r;return $(this,(function(u){return e=tn.shareId,n=void 0===e?"":e,t=tn.isShare,r=void 0!==t&&t,n&&y.updateTrackInfo({shareId:n,isShare:!!r}),Te.value&&tt(),!B.value&&J.value&&!Ln&&En(1),C.value&&Z.index.$emit(se.PAY_SUCCESS_CB),Z.nextTick$1((function(){T.setIsFromBuyPage(!1),T.setIsFromManuscript(!1),T.setIsFromManuscriptPaySuccess(!1)})),[2]}))}))})),Z.onUnload((function(){zn(),Z.index.$off(se.PAY_SUCCESS_CB),rt(),b.resetPurchaseInfo()}));var $n=pe.throttle((function(){zn()}),3e3,2),zn=function(){var e;if(!(rn<100)){var n=(on=(nn+un)/rn)>.99?1:on;(null==(e=Ie.value)?void 0:e.title)&&h.addHistoryList(Q(V({},Ie.value),{progress:n,scrollTop:nn,is_finished:!Pe.value&&n>.99}))}},Hn=function(){Ze||(c.value=!0,Ze=o((function(){c.value=!1,Ze=null}),1500))},Gn=function(){Ee.value=!1},Yn=function(){var e,n;Ke.sku_id=Y.value,Ke.couponId=null==(n=null==(e=G.value)?void 0:e.coupon)?void 0:n.id,ee.track({elementType:"Card",eventType:"Click",message:"文稿页文末优惠券立即使用点击"}),b.setNeedShowSubscribeConfirm(!1),b.updateClickedSkuId(Y.value),yn()},Xn=function(){ee.track({elementType:"Card",eventType:"Show",message:"文稿页文末优惠券曝光"}),Le.value=!1,En(0),Ln=!0},Wn=function(){ee.track({elementType:"Card",eventType:"Show",message:"文稿页文末引流主站曝光",extra:{sectionId:ke.value,miniContentType:Be.value}})};Z.onPageScroll((function(e){0!==e.scrollTop&&(nn=e.scrollTop,$n())}));var Jn=function(){return K(n,null,(function(){var e,n;return $(this,(function(t){switch(t.label){case 0:return e=this,ee.track({elementType:"Card",eventType:"Show",moduleId:"pay_card",message:"会员购买截断卡片曝光",extra:{sectionId:ke.value,canPay:!s.value,title:pn.value}}),ee.track({elementType:"Block",eventType:"Show",moduleId:"vipmini_recharge_panel",message:"会员购买截断卡片曝光",extra:{sectionId:ke.value,sectionName:pn.value,mini_content_type:Ie.value.contentType}}),Le.value=!1,J.value?(xn(),[4,En(0)]):[3,3];case 1:return t.sent(),[4,Vn()];case 2:return t.sent(),[3,4];case 3:n=Z.watch((function(){return J.value}),(function(t){return K(e,null,(function(){return $(this,(function(e){switch(e.label){case 0:return t?(xn(),[4,En(0)]):[3,3];case 1:return e.sent(),[4,Vn()];case 2:e.sent(),n&&n(),e.label=3;case 3:return[2]}}))}))})),t.label=4;case 4:return Ln=!0,a.value=!0,[2]}}))}))},Vn=function(){return K(n,null,(function(){var e;return $(this,(function(n){switch(n.label){case 0:return[4,ve.getBenefitConfig()];case 1:return e=n.sent(),Fe.value=e.truncationBenefit.list,Ne.value=e.truncationBenefit.isShow,Ue.value=e.contentBottomPromotion,[2]}}))}))},Qn=function(){Z.index.setClipboardData({data:ke.value,showToast:!1})};function Kn(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return K(this,null,(function(){var t,r,u,o,a,i,l,c,v,p,f,d;return $(this,(function(m){switch(m.label){case 0:Se.value=!1,ee.track({elementType:"Page",eventType:"Unknown",moduleId:"manuscript_loading_start",message:"文稿页 loading 开始",extra:{sectionId:null==e?void 0:e.id,miniContentType:null==e?void 0:e.content_type}}),o=n?ve.preLoadManuscript:ve.getManuscript,m.label=1;case 1:return m.trys.push([1,3,4,5]),je.value=!0,ke.value=null==e?void 0:e.id,a=null,[4,o({contentId:null==e?void 0:e.id,contentType:null==e?void 0:e.content_type})];case 2:return a=m.sent(),n&&!a?[2]:(i=a.manuscriptInfo,l=a.manuscriptContent,c=l.data,v=g.hashId,ee.track({elementType:"Page",eventType:"Unknown",moduleId:"manuscript_res_success",message:"文稿页接口返回成功",extra:{hashId:v,manuscriptInfo:i}}),p=h.historyList.find((function(e){return e.id===ke.value}))||{},i.title&&h.addHistoryList(V(V({},p),i)),on=0,ke.value=i.id,Ce.value=null==(t=i.truncate)?void 0:t.text,s.value=!!(null==i?void 0:i.isIosDevice),we.value=c.replace(/<p[^>]*>/g,'<p class="custom-p">'),Pe.value=i.isFold,xe.value=i.offline,Ie.value=i,en=i.title,ln&&Z.trackEvent({event_type:1,event_name:"pageshow",event_value:{section_id:ke.value,section_name:i.title}}),ln=!1,[3,5]);case 3:return f=m.sent(),d=g.hashId,ee.track({elementType:"Page",eventType:"Unknown",moduleId:"manuscript_res_fail",message:"文稿页接口返回失败",extra:{hashId:d,errorInfo:f}}),401!==(null==f?void 0:f.statusCode)&&(400===(null==f?void 0:f.statusCode)&&40400===(null==(u=null==(r=null==f?void 0:f.data)?void 0:r.error)?void 0:u.code)?Me.value=!0:Se.value=!0),[3,5];case 4:return je.value=!1,ee.track({elementType:"Page",eventType:"Unknown",moduleId:"manuscript_loading_end",message:"文稿页 loading 结束",extra:{sectionId:null==e?void 0:e.id,miniContentType:null==e?void 0:e.content_type}}),[7];case 5:return[2]}}))}))}var Zn=function(){var e,n;null==(n=null==(e=Oe.refs)?void 0:e.confirmModalRef)||n.open()},et=function(){Z.index.setClipboardData({data:se.XIMA_WELFARE_URL,success:function(){Z.index.showToast({title:"复制成功",icon:"none"})}})},nt=function(){var e,n,t,r;if(ee.track({elementText:"返回推荐",elementType:"Button",eventType:"Click",moduleId:"bottom_button",message:"返回推荐按钮点击"}),!Te.value&&Pe.value&&J.value)return a.value?void(null==(n=null==(e=Oe.refs)?void 0:e.payRetentionPopupRef)||n.open()):void(null==(r=null==(t=Oe.refs)?void 0:t.contentRetentionPopupRef)||r.open());Z.index.switchTab({url:"/pages/home/<USER>"})},tt=function(){rt(),sn=i((function(){ee.track({elementText:"文稿页有效用户数据上报",elementType:"Page",moduleIndex:0,message:"文稿页有效用户数据上报",extra:{section_id:ke.value,mini_content_type:Be.value,service_time:"30"}})}),3e4)},rt=function(){sn&&(l(sn),sn=null)},ut=function(){Z.index.$emit(se.PAY_SUCCESS_CB)},ot=function(){Ne.value=!1},at=function(e){return K(n,null,(function(){var n;return $(this,(function(t){switch(t.label){case 0:return Ne.value=!1,[4,ve.receiveWelfare(e)];case 1:return(null==(n=t.sent())?void 0:n.success)?(Z.index.showToast({title:"领取成功"}),[4,En(0)]):[3,3];case 2:return t.sent(),b.setWelfareType(e),[3,4];case 3:Z.index.showToast({title:n.msg||"领取失败"}),t.label=4;case 4:return[2]}}))}))},it=Z.computed((function(){var e;return Te.value&&!!we.value&&!Ee.value&&!(null==(e=Ue.value)?void 0:e.isShow)}));return function(e,n){var o,a,i,l,h,y,g,_,T,b,I,k,w,x,C,P,A,L,O,M;return Z.e({a:!Se.value&&!Me.value},Se.value||Me.value?{}:Z.e({b:je.value},(je.value,{}),{c:xe.value},xe.value?{d:Z.p({text:"该内容已下架"})}:0===Be.value?Z.e({f:Z.sr(Re,"2f230fc3-1",{k:"articleRef"}),g:Z.o(Hn),h:Z.o(Qn),i:Z.p({articleTitle:Z.unref(pn),contentType:Be.value,manuscriptInfo:Ie.value,manuscriptContent:we.value}),j:c.value},c.value?{k:Z.t(Z.unref(se.TOAST_TIP_TEXT))}:{},{l:!Pe.value&&(null==(o=Ie.value)?void 0:o.publishedAt)},!Pe.value&&(null==(a=Ie.value)?void 0:a.publishedAt)?Z.e({m:Z.t(Ie.value.publishedAt),n:v.value},v.value?{o:Z.t(Z.unref(se.COPYRIGHT_TEXT)),p:Z.o((function(e){return Z.unref(fe.jumpToProtocolPage)(5)}))}:{},{q:Z.o(Nn)}):{},{r:!Pe.value},Pe.value?{}:Z.e({s:we.value&&Ee.value&&!(null==(i=Ue.value)?void 0:i.isShow)},we.value&&Ee.value&&!(null==(l=Ue.value)?void 0:l.isShow)?{t:Z.p({vipType:"discounts-coupon",expireTime:null==(y=null==(h=Z.unref(G))?void 0:h.coupon)?void 0:y.expireAt,serverNowTime:Z.unref(H),countdownEnds:Gn,splitorColor:"#FF3838"}),v:Z.o(Yn),w:Z.o(Xn)}:(Z.unref(it)||Ee.value&&!(null==(g=Ue.value)?void 0:g.isShow)||Z.unref(it),{}),{x:Z.unref(it),y:(!Ee.value||(null==(_=Ue.value)?void 0:_.isShow))&&!Z.unref(it)}),{z:!Pe.value&&we.value&&(null==(T=Ue.value)?void 0:T.isShow)},!Pe.value&&we.value&&(null==(b=Ue.value)?void 0:b.isShow)?{A:s.value?null==(k=null==(I=Ue.value)?void 0:I.cardBackgroundImage)?void 0:k.ios:null==(x=null==(w=Ue.value)?void 0:w.cardBackgroundImage)?void 0:x.android,B:Z.o(qn),C:Z.o(Mn)}:{},{D:Z.o(Un),E:Z.unref(J)},Z.unref(J)?Z.e({F:Z.unref(We)},Z.unref(We)?{G:Z.p({content:Z.unref(Je),behavior:Z.unref(Ve)})}:Pe.value?{I:Z.sr("payPartReadPageRef","2f230fc3-9,2f230fc3-8"),J:Z.p({payCardType:(null==(C=Z.unref(U))?void 0:C.length)&&(null==(P=Z.unref(z))?void 0:P.length)?Z.unref(N):"tile",sectionId:ke.value,title:Z.unref(pn),contentType:null==(A=Ie.value)?void 0:A.contentType,toolTipText:Ce.value,showIosTruncate:0==(null==(L=Z.unref(R))?void 0:L.configType),ifShowPayCard:Pe.value||s.value,iosTruncateText:null==(O=Z.unref(j))?void 0:O.truncateText,iosTruncateImage:null==(M=Z.unref(j))?void 0:M.truncateImage,isIos:s.value,couponAnnualCardInfo:Z.unref(G),handleTimeUp:On}),K:Z.o(Jn)}:{},{H:Pe.value}):{},{L:Le.value},Le.value?{M:Z.p({"page-type":"manuscript",desktopSwitchList:Z.unref(S)})}:{}):Z.e({N:we.value},we.value?{O:Z.sr(Re,"2f230fc3-12",{k:"articleRef"}),P:Z.o(Hn),Q:Z.p({articleTitle:Z.unref(pn),contentType:Be.value,manuscriptInfo:Ie.value,manuscriptContent:we.value})}:{},{R:we.value},we.value?{S:Z.o(dn),T:Z.p({commentData:ze.value}),U:Z.o(Wn)}:{}),{e:0===Be.value,V:Z.o(nt),W:Z.o((function(){}))}),{X:Se.value},Se.value?{Y:Z.o(ut),Z:Z.p({text:"网络请求失败，请再次尝试",btnText:"重新加载"})}:{},{aa:Me.value},Me.value?{ab:Z.o(nt),ac:Z.p({text:"你似乎来到了没有知识存在的荒原",btnText:"返回首页"})}:{},{ad:Z.unref(Pn)},(Z.unref(Pn),{}),{ae:!Z.unref(J)||Z.unref(be)},!Z.unref(J)||Z.unref(be)?{af:Z.sr(t,"2f230fc3-17",{k:"loginPopupRef"}),ag:Z.o(Fn),ah:Z.p({"before-purchase":!0,sourcePage:"manuscript"})}:{},{ai:Z.sr(r,"2f230fc3-18",{k:"payRetentionPopupRef"}),aj:Z.p({isShowInManuscript:"manuscript"}),ak:Z.sr(u,"2f230fc3-19",{k:"contentRetentionPopupRef"}),al:Z.p({isShowInManuscript:"manuscript",sectionId:ke.value}),am:!Pe.value},Pe.value?{}:{an:Z.p({show:Qe.value.isPayPopup,type:Qe.value.type,closePayPopup:Z.unref(gn),showPayLoadingPopup:Z.unref(_n)})},{ao:Z.o(ot),ap:Z.o(at),aq:Z.p({show:Z.unref(fn),benefitList:Fe.value,isIosDevice:s.value}),ar:Z.sr(De,"2f230fc3-22",{k:"confirmModalRef"}),as:Z.o(et),at:Z.p({title:Z.unref(se.PROMOTION_CONFIRM_TITLE),confirmText:Z.unref(se.PROMOTION_CONFIRM_TEXT),cancelText:Z.unref(se.PROMOTION_CANCEL_TEXT)}),av:Z.sr($e,"2f230fc3-23",{k:"moreCommentModalRef"}),aw:Z.p({title:Z.unref(se.MORE_COMMENT_TITLE),confirmText:Z.unref(se.MORE_COMMENT_CONFIRM_TEXT)}),ax:Z.p({visible:f.value,prizeList:Z.unref(q),buttons:d.value,blocks:m.value,handleClose:In,callLottery:wn,endCallBack:Cn,extraData:p.value})})}}},Ae=Z._export_sfc(Re,[["__scopeId","data-v-2f230fc3"]]);Re.__runtimeHooks=3,tt.createPage(Ae)}));
//# sourceMappingURL=manuscript.js.map