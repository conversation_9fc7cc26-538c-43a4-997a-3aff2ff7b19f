define("components/pay-card/components/sku-card.js",(function(e,n,t,r,i,o,u,s,a,l,c,d,p,f,y,v,m,x,_,h,T,b,k,P,g,w,j,I,C,S,D,E,M,$,A,N,O,K,q,F){"use strict";function R(e,n){var t,r,i,o,u={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(t)throw new TypeError("Generator is already executing.");for(;u;)try{if(t=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,r=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(!(i=u.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){u=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){u.label=o[1];break}if(6===o[0]&&u.label<i[1]){u.label=i[1],i=o;break}if(i&&u.label<i[2]){u.label=i[2],u.ops.push(o);break}i[2]&&u.ops.pop(),u.trys.pop();continue}o=n.call(e,u)}catch(e){o=[6,e],r=0}finally{t=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}var B=Object.defineProperty,L=Object.defineProperties,Y=Object.getOwnPropertyDescriptors,U=Object.getOwnPropertySymbols,z=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable,H=function(e,n,t){return n in e?B(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t},J=function(e,n,t){return new i((function(r,o){var u=function(e){try{a(t.next(e))}catch(e){o(e)}},s=function(e){try{a(t.throw(e))}catch(e){o(e)}},a=function(e){return e.done?r(e.value):i.resolve(e.value).then(u,s)};a((t=t.apply(e,n)).next())}))};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==f?f:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="18d8c46d-8a33-4239-994f-309ab4394e5a",e._sentryDebugIdIdentifier="sentry-dbid-18d8c46d-8a33-4239-994f-309ab4394e5a")}catch(e){}}();var V=e("../../../common/vendor.js"),Q=e("../../../stores/modules/user/index.js");e("../../../stores/modules/history/index.js"),e("../../../stores/modules/login/index.js"),e("../../../stores/modules/search-history/index.js");var W=e("../../../stores/modules/koc-ascription/index.js"),X=e("../../../stores/modules/purchaseInfo/index.js");e("../../../stores/modules/clientInfo/index.js"),e("../../../stores/modules/systenInfo/index.js"),e("../../../stores/modules/globalVariable/index.js"),e("../../../stores/modules/trackInfo/index.js"),e("../../../stores/modules/devDebug/index.js");var Z=e("../../../utils/track.js"),ee=e("../util.js"),ne=e("../../../utils/constant.js"),te=e("../../../utils/utils.js"),re=e("../../../hooks/usePayment.js");e("../../../utils/appFetch.js"),e("../../../utils/request.js"),e("../../../writeConstant.js"),e("../../../utils/appInit.js"),e("../../../utils/onGetUdid.js"),e("../../../utils/launchOptionsHelper.js"),e("../../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../../utils/encryptHandler.js"),e("../../../utils/requstErrorMiddlewares.js"),e("../../../utils/pageHelper.js"),e("../../../pages/manuscript/store.js"),e("../../../utils/showToast.js"),e("../../../utils/payUtils.js"),e("../fetch.js"),Array||V.resolveComponent("confirm-and-pay-modal")(),Math||(ie+oe+function(){return"../../confirm-and-pay-modal/confirm-and-pay-modal.js"})();var ie=function(){return"./promotion-card-tile.js"},oe=function(){return"./pay-popup.js"},ue={__name:"sku-card",props:{card:{default:{}},index:{type:Number,default:function(){return 0}},styleType:{type:String,required:!1,default:function(){return"scroll"}},vipType:{type:String,required:!1,default:function(){return""}},isShowInManuscript:{type:Boolean,required:!0,default:function(){return!1}}},setup:function(e,n){var t,r,i=n.expose,o=e;i({startPayFn:se});var u=X.usePurchaseInfo(),s=V.storeToRefs(u),a=s.serverNowTime,l=s.promotionDescFirst,c=s.promotionDescSecond,d=s.discount,p=null!=(t=V.inject("sectionId"))?t:"",f=null!=(r=V.inject("contentType"))?r:"",y=V.toRefs(o),v=y.card,m=y.isShowInManuscript,x=y.vipType,_=V.ref(!1),h=V.ref(m.value?"manuscript":"buy"),T=function(e,n){return n?e?"".concat(te.centToDiamond(n)," 钻"):"¥".concat(te.centToYuan(n,2)):""},b=V.computed((function(){var e,n,t,r,i,u,s,a,l=o.card,c=!!o.card.coupon,d=c?null==(e=l.coupon)?void 0:e.discountPrice:l.salePrice,p=!!(null==(n=null==l?void 0:l.businessExtra)?void 0:n.benefitEndTime),f=p?null==(t=l.businessExtra)?void 0:t.benefitTitle:null==(r=l.label)?void 0:r.text,y=c?null==(i=l.coupon)?void 0:i.expireAt:p?Number(null==(u=l.businessExtra)?void 0:u.benefitEndTime):0,v="renewal"===l.type,m=c&&"single"!==l.skuType,x=Number(l.saleText)?T(D.value,(100*Number(l.saleText)).toFixed(0)):l.saleText,_=m?T(D.value,l.salePrice):x,h=m||Number(l.saleText),b=f;v?b="续费会员":"single"===l.skuType&&(b=null==(s=l.label)?void 0:s.text),b=b?D.value?null==b?void 0:b.replace(/(\d+)\s*(元)/,(function(e,n){return 10*n+"钻"})):b:"";var k,P,g=v?"次月19元续费，可随时取消":l.tip,w=null!=(a=null==g?void 0:g.replace(/(\d+)\s*(元)/,(function(e,n){return D.value?10*n+"钻":n+"元"})))?a:"";return k=function(e,n){for(var t in n||(n={}))z.call(n,t)&&H(e,t,n[t]);var r=!0,i=!1,o=void 0;if(U)try{for(var u,s=U(n)[Symbol.iterator]();!(r=(u=s.next()).done);r=!0){t=u.value;G.call(n,t)&&H(e,t,n[t])}}catch(e){i=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(i)throw o}}return e}({},o.card),P={price:D.value?"".concat(te.centToDiamond(d)):"".concat(te.centToYuan(d,2)),islineThrough:h,specialPrice:_,tip:w,realPrice:te.centToField(d,2),outDatedPrice:c?te.centToYuan(l.salePrice):"",realDiamondPrice:te.centToDiamond(d),outDiamondPrice:c?te.centToDiamond(l.salePrice):"",title:l.title,isDisCount:(c||p)&&"single"!==l.skuType,saleText:D.value?" ".concat(te.centToDiamond(l.salePrice)," 钻"):"".concat(te.centToYuan(l.salePrice),"元"),discountText:D.value?null==f?void 0:f.replace(/(\d+)\s*(元)/,(function(e,n){return 10*n+"钻"})):f,tagText:b,expireTime:y,isCoupon:c,isPromotion:p},L(k,Y(P))})),k=(W.useKocAscriptionStore(),Q.useUserStore()),P=V.reactive({isPayFinished:!1}),g=V.storeToRefs(k),w=g.isLogin,j=g.isGuest,I=V.storeToRefs(u),C=I.clickedSkuId,S=I.prizeSkuId;var D=V.ref(ee.isIos()&&!0),E=V.ref({type:"loading",isPayPopup:!1}),M=re.usePayment(P,E,o.isShowInManuscript),$=M.startPay,A=M.ttLoginAndStartPay,N=M.closePayPopup,O=M.showPayLoadingPopup;V.onMounted((function(){!function(){J(this,null,(function(){var e;return R(this,(function(n){return e={service:"payment",success:function(e){P.provider=e.provider[0]},fail:function(){P.provider="fail"}},V.index.getProvider(e),[2]}))}))}(),V.index.$on("login_success_continue_pay",ue),V.index.$on("start_pay_fn_".concat(h.value),se),V.index.$on("lucky_wheel_claim",q),V.index.$on(ne.PAY_CANCEL_CB,K)})),V.onUnmounted((function(){V.index.$off("login_success_continue_pay",ue),V.index.$off("start_pay_fn_".concat(h.value),se),V.index.$off("lucky_wheel_claim",q),V.index.$off(ne.PAY_CANCEL_CB,K)})),V.watch((function(){return o.card}),(function(e){var n,t,r;P.couponId=null==(n=e.coupon)?void 0:n.id,P.sku_id=e.skuId,P.isRenew="renewal"===e.type,P.promoKey=e.promoKey,P.type=e.type,P.skuType=e.skuType,P.wxSkuId=e.wxSkuId,P.wxPromoKey=e.wxPromoKey,P.quantity=e.quantity||1,P.startPoint=e.startPoint||"",P.benefitType=null==(t=e.businessExtra)?void 0:t.benefitType,P.channelKey=null==(r=e.coupon)?void 0:r.channelKey}),{deep:!0,immediate:!0});var K=function(){N()},q=function(){var e;S.value===o.card.skuId&&(Z.track({elementType:"Button",eventType:"Click",message:"文稿页面取消支付转盘立即使用",extra:{mini_action_tag:null==(e=o.card.coupon)?void 0:e.channelKey}}),$())},F=function(){o.isShowInManuscript?V.index.$emit("manuscript_get_sku_info"):u.getPayCards({ignoreCoupon:1})};function B(){ie(),A()}function ie(){_.value=!1}function oe(){return J(this,null,(function(){var e,n,t,r;return R(this,(function(i){if(o.isShowInManuscript&&V.trackEvent({event_type:0,event_name:"文稿页点击截断",event_value:{sku_id:o.card.skuId,sku_name:o.card.title,real_amount:o.card.salePrice}}),r=o.isShowInManuscript?"会员购买截断卡片点击":"购买页会员卡片sku点击",Z.track({elementText:o.card.title,elementType:"Card",eventType:"Click",moduleId:"pay_card_sku",message:r,extra:{sku_id:o.sku_id,sku_name:o.card.title,real_amount:o.card.salePrice,section_id:null==p?void 0:p.value}}),Z.track({elementType:"Block",eventType:"Click",moduleId:"vipmini_recharge_panel_sku",message:"充值面板sku-模块单击",extra:{sku_id:o.card.skuId,sku_name:o.card.title,real_amout:o.card.salePrice,coupon_amout:null!=(t=null==(n=null==(e=null==o?void 0:o.card)?void 0:e.coupon)?void 0:n.discountPrice)?t:"",section_id:null==p?void 0:p.value,mini_content_type:null==f?void 0:f.value}}),u.updateClickedSkuId(o.card.skuId),u.setNeedShowSubscribeConfirm(!0),w.value){if(o.isShowInManuscript,P.isRenew)return[2,j.value?void V.index.$emit("zh_login_event_before_purchase"):void(_.value=!0)];se()}else V.index.$emit("zh_login_event_before_purchase");return[2]}))}))}function ue(){if(C.value===o.card.skuId){var e=o.isShowInManuscript?"文稿页游客登录后自动购买会员":"购买页游客登录后自动购买会员";Z.track({elementText:o.card.title,elementType:"Card",eventType:"Unknown",moduleId:"login_success_pay",message:e,extra:{sku_id:C.value}}),o.isShowInManuscript,P.isRenew?A():se()}}function se(e){C.value===o.card.skuId&&$(e)}return function(n,t){var r,i,o,u;return V.e({a:"scroll"==e.styleType},"scroll"==e.styleType?V.e({b:!!(null==(r=V.unref(v).label)?void 0:r.text)},(null==(i=V.unref(v).label)?void 0:i.text)?{c:V.t(V.unref(b).discountText)}:{},{d:V.t(V.unref(b).title),e:V.unref(b).isDisCount?1:"",f:V.unref(b).realPrice},V.unref(b).realPrice?V.e({g:!D.value},(D.value,{}),{h:V.t(D.value?V.unref(b).realDiamondPrice:V.unref(b).realPrice),i:D.value},(D.value,{}),{j:V.unref(b).isDisCount?1:""}):{},{k:V.unref(b).isCoupon&&"single"!==V.unref(v).skuType},V.unref(b).isCoupon&&"single"!==V.unref(v).skuType?{l:V.t(V.unref(b).saleText)}:{},{m:V.unref(b).tip,n:null==(o=V.unref(b).activity)?void 0:o.activityText},(null==(u=V.unref(b).activity)?void 0:u.activityText)?{o:V.t(V.unref(b).activity.activityText)}:{},{p:V.n(V.unref(x)),q:V.n({discount:V.unref(b).isDisCount}),r:V.o(oe)}):{},{s:"tile"==e.styleType},"tile"==e.styleType?V.e({t:V.unref(b).tagText},V.unref(b).tagText?V.e({v:V.t(V.unref(b).tagText+""),w:V.unref(b).isDisCount},V.unref(b).isDisCount?{x:V.p({expireTime:V.unref(b).expireTime,serverNowTime:V.unref(a),countdownEnds:F,promotionDescFirst:V.unref(l),promotionDescSecond:V.unref(c),discount:V.unref(d)})}:{}):{},{y:!D.value},(D.value,{}),{z:V.t(V.unref(b).price),A:D.value},(D.value,{}),{B:V.unref(b).specialPrice},V.unref(b).specialPrice?{C:V.t(V.unref(b).specialPrice),D:V.unref(b).islineThrough?1:""}:{},{E:V.unref(b).tip},V.unref(b).tip?{F:V.t(V.unref(b).tip)}:{},{G:V.n(V.unref(x)),H:V.n({discount:V.unref(b).isDisCount}),I:V.o(oe)}):{},{J:V.p({show:E.value.isPayPopup,type:E.value.type,closePayPopup:V.unref(N),showPayLoadingPopup:V.unref(O)}),K:V.p({isShowPayModal:_.value,confirmAndPay:B,closePayModal:ie})})}}},se=V._export_sfc(ue,[["__scopeId","data-v-62af15b6"]]);tt.createComponent(se)}));
//# sourceMappingURL=sku-card.js.map