define("components/novel-plugin/trace.js",(function(e,n,s,t,o,i,l,r,a,d,u,c,m,g,x,v,b,f,I,p,w,y,P,_,j,h,k,U,T,V,D,S,W,E,H,L,R,q,z,A){"use strict";!function(){try{var e=void 0!==u?u:"undefined"!=typeof global?global:void 0!==g?g:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="6b772c71-996b-411c-8fde-a7b406cb2fb4",e._sentryDebugIdIdentifier="sentry-dbid-6b772c71-996b-411c-8fde-a7b406cb2fb4")}catch(e){}}();var B=e("../../utils/track.js"),C=e("../../common/vendor.js");e("../../stores/modules/user/index.js"),e("../../stores/modules/history/index.js");var F=e("../../stores/modules/login/index.js");e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js"),e("../../stores/modules/purchaseInfo/index.js"),e("../../stores/modules/clientInfo/index.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js"),e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js");var G=e("./utils.js"),J=null,K=function(){J&&(a(J),J=null)};s.novelPluginPageShowTrace=function(e){var n=e.sectionId,s=e.id,t=e.title;B.track({elementType:"Page",eventType:"Show",message:"文稿页曝光",wxViewUrl:G.getWxViewUrlPath(),extra:{sectionId:n}}),C.trackEvent({event_type:1,event_name:"文稿页曝光",event_value:{info_id:null!=s?s:"",info_title:null!=t?t:""}}),B.track({elementType:"Page",eventType:"Unknown",moduleId:"manuscript_loading_start",message:"文稿页 loading 开始",wxViewUrl:G.getWxViewUrlPath(),extra:{sectionId:n}})},s.novelPluginResponseTrace=function(e){var n=F.useLoginStore().memberHashId;B.track({elementType:"Page",eventType:"Unknown",moduleId:"manuscript_res_success",message:"文稿页接口返回成功",wxViewUrl:G.getWxViewUrlPath(),extra:{memberHashId:n,manuscriptInfo:e}}),B.track({elementType:"Page",eventType:"Unknown",moduleId:"manuscript_loading_end",message:"文稿页 loading 结束",wxViewUrl:G.getWxViewUrlPath(),extra:{sectionId:e.sectionId}})},s.novelPluginStartPolling=function(e){K(),J=r((function(){var n;B.track({elementText:"微小阅读器有效用户数据上报",elementType:"Page",moduleIndex:0,message:"微小阅读器有效用户数据上报",wxViewUrl:G.getWxViewUrlPath(),extra:{section_id:null!=(n=null==e?void 0:e.sectionId)?n:"",service_time:"30"}})}),3e4)},s.novelPluginStopPolling=K}));
//# sourceMappingURL=trace.js.map