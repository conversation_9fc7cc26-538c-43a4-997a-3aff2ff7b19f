define("utils/appFetch.js",(function(e,n,t,r,o,i,a,u,s,c,l,f,d,p,h,b,g,y,m,v,I,_,w,j,P,A,x,O,E,D,S,T,C,R,M,N,k,U,L,F){"use strict";function G(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=new Array(n);t<n;t++)r[t]=e[t];return r}function B(e){return function(e){if(Array.isArray(e))return G(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,n){if(!e)return;if("string"==typeof e)return G(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(t);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return G(e,n)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(e,n){var t,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(t)throw new TypeError("Generator is already executing.");for(;a;)try{if(t=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=n.call(e,a)}catch(e){i=[6,e],r=0}finally{t=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}}var Q=Object.defineProperty,X=Object.defineProperties,z=Object.getOwnPropertyDescriptors,H=Object.getOwnPropertySymbols,V=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable,Y=function(e,n,t){return n in e?Q(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t},$=function(e,n){for(var t in n||(n={}))V.call(n,t)&&Y(e,t,n[t]);var r=!0,o=!1,i=void 0;if(H)try{for(var a,u=H(n)[Symbol.iterator]();!(r=(a=u.next()).done);r=!0){t=a.value;W.call(n,t)&&Y(e,t,n[t])}}catch(e){o=!0,i=e}finally{try{r||null==u.return||u.return()}finally{if(o)throw i}}return e},J=function(e,n){return X(e,z(n))},K=function(e,n,t){return new o((function(r,i){var a=function(e){try{s(t.next(e))}catch(e){i(e)}},u=function(e){try{s(t.throw(e))}catch(e){i(e)}},s=function(e){return e.done?r(e.value):o.resolve(e.value).then(a,u)};s((t=t.apply(e,n)).next())}))};!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==p?p:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="f962d900-f675-4722-862f-ed4eabb14b17",e._sentryDebugIdIdentifier="sentry-dbid-f962d900-f675-4722-862f-ed4eabb14b17")}catch(e){}}();var Z=e("./request.js"),ee=e("../writeConstant.js");e("../stores/modules/user/index.js"),e("../stores/modules/history/index.js"),e("../stores/modules/login/index.js"),e("../stores/modules/search-history/index.js"),e("../stores/modules/koc-ascription/index.js"),e("../stores/modules/purchaseInfo/index.js"),e("../stores/modules/clientInfo/index.js"),e("../stores/modules/systenInfo/index.js"),e("../stores/modules/globalVariable/index.js"),e("../stores/modules/trackInfo/index.js"),e("../stores/modules/devDebug/index.js"),e("../common/vendor.js");var ne=e("./constant.js");t.cancelSubscription=function(e){return K(t,[e],(function(e){var n,t;return q(this,(function(r){switch(r.label){case 0:return n=e.contractId,t=e.signingChannel,[4,Z.api.post("/wallet-vip/renewPage/subscription/cancel",{contract_id:n,signing_channel:t})];case 1:return[2,r.sent()]}}))}))},t.getAuthTokenLogin=function(e){return K(t,null,(function(){return q(this,(function(n){switch(n.label){case 0:return[4,Z.api.post("/api/account/prod/applet/login/auto",{code:e},{header:J($({},ee.LOGIN_BASE_HEADER),{"x-external-source":"".concat(ee.MINI_APP_PLATFORM,"-miniprogram"),"X-Tenant-ID":"0"})})];case 1:return[2,n.sent()]}}))}))},t.getCommonConfig=function(){return K(t,null,(function(){return q(this,(function(e){switch(e.label){case 0:return[4,Z.api.get("/dalaran/common/config",{id:ne.COMMON_CONFIG_REQUEST_ID,usePrefetchCache:!0})];case 1:return[2,e.sent()]}}))}))},t.getDigitalCode=function(e){return K(t,null,(function(){return q(this,(function(n){switch(n.label){case 0:return[4,Z.api.post("/dalaran/guide_code",e)];case 1:return[2,n.sent()]}}))}))},t.getGreyList=function(e){return K(t,[e],(function(e){var n,t;return q(this,(function(r){switch(r.label){case 0:return n=e.scene,t=e.section_ids,[4,Z.api.post("/dalaran/grey_list/content_judge",{scene:n,section_ids:t})];case 1:return[2,r.sent()]}}))}))},t.getRenewPageInfo=function(){return K(t,null,(function(){return q(this,(function(e){switch(e.label){case 0:return[4,Z.api.get("/wallet-vip/renewPage/subscription/query",{params:{sign_channel:"DOUYIN_SUBSCRIPTION"}})];case 1:return[2,e.sent()]}}))}))},t.getUdid=function(){return K(t,null,(function(){return q(this,(function(e){switch(e.label){case 0:return[4,Z.api.post("/wx-minapp-account/udid",{},{id:ne.UDID_REQUEST_ID,usePrefetchCache:!0})];case 1:return[2,e.sent()]}}))}))},t.getUserInfo=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return K(t,null,(function(){return q(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,Z.api.get("/dalaran/user_info",{id:ne.GET_USERINFO_REQUEST_ID,usePrefetchCache:e})];case 1:return[2,n.sent()];case 2:return n.sent(),[3,3];case 3:return[2]}}))}))},t.getVipCardsApi=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return K(t,B(n),(function(){var e,n,t=arguments;return q(this,(function(r){switch(r.label){case 0:return e=t.length>0&&void 0!==t[0]?t[0]:{ignore_coupon:0},n="/km-ravenclaw/".concat(ee.MINI_APP_PLATFORM,"/zhihu_novel/vip_purchase"),[4,Z.api.get(n,{params:e})];case 1:return[2,r.sent()]}}))}))},t.phonenumAuthorizationLogin=function(e){return K(t,null,(function(){var n;return q(this,(function(t){switch(t.label){case 0:return n="/api/account/prod/applet/login/phonenum/".concat(ee.MINI_APP_PLATFORM),[4,Z.api.post(n,$({},e),{header:J($({},ee.LOGIN_BASE_HEADER),{"X-Tenant-ID":"0","x-external-source":"".concat(ee.MINI_APP_PLATFORM,"-miniprogram"),"X-Requested-With":"Fetch"})})];case 1:return[2,t.sent()]}}))}))},t.trackBuyEvent=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"manuscript",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return K(t,null,(function(){return q(this,(function(t){return[2,Z.api.get("/api/dalaran-nodejs/open/track/device-info",{params:{channel_id:ee.MINI_APPID,content_id:"manuscript"===e?n:"",extra:{type:"buy",origin:e}}}).catch((function(){}))]}))}))}}));
//# sourceMappingURL=appFetch.js.map