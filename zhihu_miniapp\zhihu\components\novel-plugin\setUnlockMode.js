define("components/novel-plugin/setUnlockMode.js",(function(e,n,t,o,r,s,l,i,u,a,c,d,f,b,y,h,p,v,g,m,x,j,w,k,I,<PERSON>,D,U,_,M,S,T,E,G,N,P,V,B,H,R){"use strict";function W(e,n){var t,o,r,s,l={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return s={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function i(s){return function(i){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;l;)try{if(t=1,o&&(r=2&s[0]?o.return:s[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,s[1])).done)return r;switch(o=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return l.label++,{value:s[1],done:!1};case 5:l.label++,o=s[1],s=[0];continue;case 7:s=l.ops.pop(),l.trys.pop();continue;default:if(!(r=l.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){l=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){l.label=s[1];break}if(6===s[0]&&l.label<r[1]){l.label=r[1],r=s;break}if(r&&l.label<r[2]){l.label=r[2],l.ops.push(s);break}r[2]&&l.ops.pop(),l.trys.pop();continue}s=n.call(e,l)}catch(e){s=[6,e],o=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,i])}}}!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==b?b:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="5396b426-59bc-4e34-912f-5811dec1190c",e._sentryDebugIdIdentifier="sentry-dbid-5396b426-59bc-4e34-912f-5811dec1190c")}catch(e){}}();var q=e("../../common/vendor.js"),z=e("../../utils/payUtils.js");e("../../stores/modules/user/index.js"),e("../../stores/modules/history/index.js"),e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js"),e("../../stores/modules/purchaseInfo/index.js"),e("../../stores/modules/clientInfo/index.js"),e("../../stores/modules/systenInfo/index.js");var A=e("../../stores/modules/globalVariable/index.js");e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js"),t.setUnlockModeHandle=function(){return e=t,n=function(){var e,n,t,o,r;return W(this,(function(s){return e=!0,n=q.NovelPlugin.getCurrentNovelManager(),t=A.useGlobalVariable(),o=q.storeToRefs(t),r=o.iosPaymentSwitch,z.isIos()&&(e=r.value),n.setChargeWay({globalConfig:{mode:3,buttonText:"购买",tip:e?"本章为付费章节，购买后可继续阅读":"本章为付费章节，暂不支持ios支付",showButton:e}}),n.onUserClickCustomUnlock((function(e){n.openChargeDialog({})})),[2]}))},new r((function(t,o){var s=function(e){try{i(n.next(e))}catch(e){o(e)}},l=function(e){try{i(n.throw(e))}catch(e){o(e)}},i=function(e){return e.done?t(e.value):r.resolve(e.value).then(s,l)};i((n=n.apply(e,null)).next())}));var e,n}}));
//# sourceMappingURL=setUnlockMode.js.map