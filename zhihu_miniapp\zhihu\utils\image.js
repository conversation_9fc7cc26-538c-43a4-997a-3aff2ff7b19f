define("utils/image.js",(function(e,n,t,r,a,c,o,u,i,l,s,f,b,p,h,d,v,y,g,w,m,x,I,j,k,_,D,E,S,R,z,C,G,L,O,T,U,W,$,q){"use strict";function A(e,n){var t,r,a,c,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return c={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(c[Symbol.iterator]=function(){return this}),c;function u(c){return function(u){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;o;)try{if(t=1,r&&(a=2&c[0]?r.return:c[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,c[1])).done)return a;switch(r=0,a&&(c=[2&c[0],a.value]),c[0]){case 0:case 1:a=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,r=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(a=o.trys,(a=a.length>0&&a[a.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!a||c[1]>a[0]&&c[1]<a[3])){o.label=c[1];break}if(6===c[0]&&o.label<a[1]){o.label=a[1],a=c;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(c);break}a[2]&&o.ops.pop(),o.trys.pop();continue}c=n.call(e,o)}catch(e){c=[6,e],r=0}finally{t=a=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,u])}}}!function(){try{var e=void 0!==s?s:"undefined"!=typeof global?global:void 0!==p?p:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="610b767f-ba18-4ee2-8892-9398c10be4a4",e._sentryDebugIdIdentifier="sentry-dbid-610b767f-ba18-4ee2-8892-9398c10be4a4")}catch(e){}}();var B=e("../common/vendor.js"),F=null,H=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"60w",t=arguments.length>2?arguments[2]:void 0;if(!e)return"";var r=new RegExp("\\.(".concat(["jpg","jpeg","png","bmp"].join("|"),")(?:\\?.*)?$"),"i"),a=e.match(r);if(a){var c=a[1].toLowerCase();return e.replace(new RegExp("\\.".concat(c),"i"),"_".concat(n,".").concat(t||c))}return e};t.getOptimalImageUrl=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"60w";if(!e)return"";try{return null!=F&&F?H(e,n,"webp"):H(e,n)}catch(n){return e}},t.initWebpSupport=function(){return e=t,n=function(){var e;return A(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),null!==F?[2,F]:[4,new a((function(e){try{B.index.getImageInfo({src:"https://pica.zhimg.com/v2-b292ca232a2558573e9063d832b75c68.webp?source=6a64a727",success:function(){e(!0)},fail:function(){e(!1)}})}catch(n){e(!1)}}))];case 1:return e=n.sent(),[2,(F=e,e)];case 2:return n.sent(),[2,(F=!1,!1)];case 3:return[2]}}))},new a((function(t,r){var c=function(e){try{u(n.next(e))}catch(e){r(e)}},o=function(e){try{u(n.throw(e))}catch(e){r(e)}},u=function(e){return e.done?t(e.value):a.resolve(e.value).then(c,o)};u((n=n.apply(e,null)).next())}));var e,n}}));
//# sourceMappingURL=image.js.map