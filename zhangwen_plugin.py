import html
import os
import tempfile
import re
import httpx
import urllib.parse
import json
import requests  # 添加requests库
import time  # 添加time模块
from nonebot import on_message
from nonebot.adapters.onebot.v11 import Event, Bot, GroupMessageEvent, Message, MessageSegment
from nonebot import logger
from nonebot.matcher import Matcher
from contextlib import asynccontextmanager
from typing import Dict, List, Optional
import asyncio
import random # Added for random delay

# 添加WebDAV配置
WEBDAV_URL = os.getenv("WEBDAV_URL", "https://al.zyii.xyz:666/dav")  # WebDAV服务器地址
WEBDAV_USERNAME = os.getenv("WEBDAV_USERNAME", "yuedu")  # WebDAV用户名
WEBDAV_PASSWORD = os.getenv("WEBDAV_PASSWORD", "123456")  # WebDAV密码
WEBDAV_FOLDER = os.getenv("WEBDAV_FOLDER", "琪露诺上传的《小说》")  # WebDAV上传目录

# 配置多个BASE_URL
BASE_URLS = [
    'http://154.8.217.196:6599',
    'http://154.8.217.196:6547',
    'http://154.8.217.196:6548',
    'http://154.8.217.196:6549',
    'http://154.8.217.196:6598'
]
# 选择一个默认的BASE_URL
BASE_URL = BASE_URLS[0]
USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
DEFAULT_THREADS = 5  # 设置默认线程数

# 定义优先级常量
class PluginPriority:
    CRITICAL = 1
    HIGH = 5
    NORMAL = 10
    LOW = 20

# 存储下载状态的字典
zhangwen_download_status = {}

# 创建章文品读小说专属规则函数
def zhangwen_url_rule() -> callable:
    async def _rule(event: Event) -> bool:
        if not isinstance(event, GroupMessageEvent):
            return False
        raw_message = str(event.get_message()).strip()
        logger.info(f"章文品读链接规则检查消息: {raw_message}")
        
        # 匹配抖音分享链接
        douyin_pattern = r"https?://www\.iesdouyin\.com/share/microapp/"
        # 匹配章文品读链接
        zhangwen_pattern = r"https?://.*?bookId=[0-9]+"
        
        # 检查所有模式
        if re.search(douyin_pattern, raw_message) or re.search(zhangwen_pattern, raw_message):
            logger.info("成功匹配章文品读小说链接")
            return True
                
        return False
    return _rule

# 创建一个全局消息处理器来捕获任何可能的章文品读小说链接
zhangwen_global_matcher = on_message(
    priority=PluginPriority.CRITICAL,  # 使用最高优先级
    block=True  # 修改为阻断，避免重复处理
)

@zhangwen_global_matcher.handle()
async def handle_zhangwen_global(matcher: Matcher, bot: Bot, event: Event):
    """全局处理器，捕获所有可能的章文品读小说链接"""
    if not isinstance(event, GroupMessageEvent):
        return
    
    raw_message = str(event.get_message()).strip()
    logger.info(f"全局章文品读小说链接检查: {raw_message}")
    
    # 检查是否包含抖音分享链接
    douyin_pattern = r"https?://www\.iesdouyin\.com/share/microapp/"
    contains_douyin = re.search(douyin_pattern, raw_message)
    
    # 检查是否包含bookId参数
    has_book_id = 'bookId' in raw_message
    
    if contains_douyin or has_book_id:
        logger.info(f"检测到疑似章文品读小说链接: {raw_message}")
        # 尝试提取book_id
        book_id = await extract_book_id_from_url(raw_message)
        if book_id:
            logger.info(f"成功提取到章文品读小说book_id: {book_id}，将处理链接")
            # 阻断后续处理
            matcher.stop_propagation()
            logger.info("事件传播已阻断，由章文品读小说插件处理")
            # 处理链接下载
            await handle_download_zhangwen_novel(matcher, bot, event)
            return
        else:
            logger.info("无法提取章文品读小说book_id，继续传递事件")

# 移除专用匹配器，只保留全局匹配器
# 创建章文品读小说下载匹配器
# download_zhangwen_novel = on_message(
#     priority=PluginPriority.CRITICAL,
#     block=True,
#     rule=zhangwen_url_rule()
# )

# 创建一个空的匹配器，用于导出
download_zhangwen_novel = on_message(
    priority=PluginPriority.CRITICAL,
    block=True,
    rule=lambda: False  # 永远不会匹配
)

@download_zhangwen_novel.handle()
async def handle_download_zhangwen_novel_dummy():
    """仅用于导出，实际不会被调用"""
    pass

# 实际的下载处理函数
async def handle_download_zhangwen_novel(matcher: Matcher, bot: Bot, event: Event):
    """处理章文品读小说下载请求"""
    try:
        # 检查是否为群消息
        if not isinstance(event, GroupMessageEvent):
            logger.info("非群聊消息，退出处理")
            return
        
        raw_message = str(event.get_message()).strip()
        logger.info(f"收到章文品读小说链接：{raw_message}")
        cleaned_message = html.unescape(raw_message)

        await bot.send(event, "正在为您下载小说，请稍等片刻...")

        try:
            book_id = await extract_book_id_from_url(cleaned_message)
            if not book_id:
                await bot.send(event, "链接无效，无法提取书籍ID，请检查链接是否正确。")
                return

            # 下载书籍到临时文件
            temp_file_path = await download_book_to_file(book_id, event.user_id, "未知书名")
            if not temp_file_path:
                await bot.send(event, "下载失败，可能是网络问题或API不可用，请稍后再试。")
                return

            try:
                # 读取文件内容
                logger.info(f"开始读取临时文件：{temp_file_path}")
                with open(temp_file_path, 'rb') as f:
                    file_content = f.read()
                logger.info(f"文件大小：{len(file_content)} 字节")
                
                # 提取书名
                with open(temp_file_path, 'r', encoding='utf-8') as f:
                    book_title = f.readline().strip()
                
                # 上传到群文件
                logger.info(f"开始上传群文件，群号：{event.group_id}")

                # 使用安全的文件名
                safe_name = re.sub(r'[\\/:"*?<>|]+', "_", f"{book_title}.txt")
                
                # 尝试上传到群文件
                try:
                    # 使用文件路径方式上传
                    await bot.send(event, f"《{book_title}》下载完成，正在上传到群文件...")
                    
                    await bot.call_api(
                        "upload_group_file",
                        group_id=event.group_id,
                        file=temp_file_path,
                        name=safe_name
                    )
                    
                    logger.info("群文件上传成功")
                    
                    # 发送成功消息
                    success_msg = Message([
                        MessageSegment.at(event.user_id),
                        MessageSegment.text(f"\n《{book_title}》\n------------\n小说已上传至群文件\n共{len(file_content)//1000}KB，请查收❤\n------------")
                    ])
                    await bot.send(event, success_msg)
                    
                except Exception as e:
                    logger.error(f"群文件上传失败，错误: {str(e)}")
                    # 直接进入WebDAV上传流程
                    
                    # 上传到WebDAV
                    await bot.send(event, f"群文件上传受限，正在尝试WebDAV上传...")
                    download_url = await upload_to_webdav(temp_file_path, safe_name)
                    
                    if download_url:
                        # 发送下载链接
                        web_msg = Message([
                            MessageSegment.at(event.user_id),
                            MessageSegment.text(f"\n《{book_title}》\n------------\n小说下载完成\n由于群文件上传限制\n已上传至网盘\n请通过以下链接下载\n------------\n{download_url}")
                        ])
                        await bot.send(event, web_msg)
                    else:
                        # 如果WebDAV上传也失败，发送错误消息
                        error_msg = Message([
                            MessageSegment.at(event.user_id),
                            MessageSegment.text(f" \n文件上传失败，但您可以稍后再试。")
                        ])
                        await bot.send(event, error_msg)
                
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                
            except Exception as outer_e:
                # 这里添加对外层异常的处理
                logger.error(f"文件读取或处理过程中发生错误: {str(outer_e)}", exc_info=True)
                error_msg = Message([
                    MessageSegment.at(event.user_id),
                    MessageSegment.text(f" \n文件处理过程中出错，请稍后重试。")
                ])
                await bot.send(event, error_msg)
                
                # 清理临时文件
                if temp_file_path and os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
            
        except Exception as e:
            logger.error(f"下载小说时发生错误: {str(e)}", exc_info=True)
            error_msg = Message([
                MessageSegment.at(event.user_id),
                MessageSegment.text(f" \n下载过程中出现错误，请稍后再试。")
            ])
            await bot.send(event, error_msg)
            if 'book_title' in locals():
                clear_download_status(event.user_id, book_title)

    except Exception as e:
        logger.error(f"处理章文品读小说时发生错误: {str(e)}", exc_info=True)
        error_msg = Message([
            MessageSegment.at(event.user_id),
            MessageSegment.text(f" 处理过程中出现错误，请稍后再试。")
        ])
        await bot.send(event, error_msg)

# 保留下载进度查询功能
progress_query_zhangwen = on_message(priority=PluginPriority.CRITICAL)

@progress_query_zhangwen.handle()
async def handle_progress_zhangwen(matcher: Matcher, bot: Bot, event: Event):
    """处理章文品读进度查询请求"""
    if not isinstance(event, GroupMessageEvent):
        return
        
    raw_message = str(event.get_message()).strip()
    cleaned_message = html.unescape(raw_message)
    
    if "章文品读下载进度" in cleaned_message or "品读小说进度" in cleaned_message:
        user_progress = zhangwen_download_status.get(event.user_id, [])
        progress_text = "章文品读小说下载进度：\n"
        if not user_progress:
            progress_text += "没有进行中的下载任务"
        else:
            for i, book_progress in enumerate(user_progress, 1):
                progress_text += f"{i}. {book_progress['progress']}\n"
        await bot.send(event, progress_text)
        await matcher.finish()

# 直接从6666.py复制的ZhangwenDownloader类
class ZhangwenDownloader:
    def __init__(self):
        """初始化下载器"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': USER_AGENT,
        })

    def _make_request(self, url: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """发送GET请求并解析JSON响应"""
        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get('success') and data.get('code') == 1:
                return data.get('data')
            else:
                logger.warning(f"API请求失败: {url}, 消息: {data.get('message', '未知')}")
                return None
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败 {url}: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"响应解析失败 {url}: {e}")
            return None

    def get_book_info(self, book_id: str) -> Optional[Dict]:
        """获取书籍详细信息"""
        url = f"{BASE_URL}/book/detail"
        params = {'bookId': book_id}
        return self._make_request(url, params)

    def get_chapter_list(self, book_id: str) -> List[Dict]:
        """获取章节列表"""
        url = f"{BASE_URL}/book/menu"
        params = {'bookId': book_id}
        data = self._make_request(url, params)
        if data and 'menu' in data and len(data['menu']) > 0:
            # 假设只有一个分卷 "全部章节"
            return data['menu'][0].get('chapters', [])
        return []

    def get_chapter_content(self, book_id: str, chapter_id: str) -> str:
        """获取章节内容 (纯文本)"""
        url = f"{BASE_URL}/book/chapter"
        params = {'bookId': book_id, 'chapterId': chapter_id}
        data = self._make_request(url, params)
        if data and 'chapter' in data and 'content' in data['chapter']:
            # 获取原始HTML内容
            html_content = data['chapter']['content']
            # 简单去除HTML标签，提取纯文本
            text_content = re.sub(r'<[^>]+>', '', html_content) # 简单移除标签
            text_content = re.sub(r'\s+', ' ', text_content).strip() # 规范化空白符
            return text_content
        return ""

# 异步包装类，用于在NoneBot中使用ZhangwenDownloader
class AsyncZhangwenWrapper:
    def __init__(self):
        # 不在初始化时创建downloader实例
        pass
        
    async def get_book_info(self, book_id: str) -> Optional[Dict]:
        """异步包装获取书籍详细信息"""
        # 尝试所有API地址，直到成功
        for base_url in BASE_URLS:
            try:
                # 临时修改BASE_URL
                global BASE_URL
                old_base_url = BASE_URL
                BASE_URL = base_url
                
                # 创建新的下载器实例
                downloader = ZhangwenDownloader()
                
                # 在线程池中执行同步方法
                result = await asyncio.to_thread(downloader.get_book_info, book_id)
                
                # 如果成功，保持当前BASE_URL并返回结果
                if result:
                    logger.debug(f"使用 {base_url} 获取数据成功")
                    return result
                
                # 恢复原来的BASE_URL
                BASE_URL = old_base_url
            except Exception as e:
                logger.error(f"使用 {base_url} 获取书籍信息失败: {e}")
                # 恢复原来的BASE_URL
                BASE_URL = old_base_url
                continue
                
        logger.error("所有API接口都无法获取书籍信息")
        return None
        
    async def get_chapter_list(self, book_id: str) -> List[Dict]:
        """异步包装获取章节列表"""
        # 尝试所有API地址，直到成功
        for base_url in BASE_URLS:
            try:
                # 临时修改BASE_URL
                global BASE_URL
                old_base_url = BASE_URL
                BASE_URL = base_url
                
                # 创建新的下载器实例
                downloader = ZhangwenDownloader()
                
                # 在线程池中执行同步方法
                result = await asyncio.to_thread(downloader.get_chapter_list, book_id)
                
                # 如果成功，保持当前BASE_URL
                if result:
                    logger.debug(f"使用 {base_url} 获取章节列表成功")
                    return result
                
                # 恢复原来的BASE_URL
                BASE_URL = old_base_url
            except Exception as e:
                logger.error(f"使用 {base_url} 获取章节列表失败: {e}")
                # 恢复原来的BASE_URL
                BASE_URL = old_base_url
                continue
                
        logger.error("所有API接口都无法获取章节列表")
        return []
        
    async def get_chapter_content(self, book_id: str, chapter_id: str) -> str:
        """异步包装获取章节内容，完全在线程中执行6666.py的逻辑"""
        def get_content_in_thread():
            # 在线程中创建全新的会话
            session = requests.Session()
            session.headers.update({
                'User-Agent': USER_AGENT,
            })
            
            # 尝试所有API地址
            for base_url in BASE_URLS:
                try:
                    url = f"{base_url}/book/chapter"
                    params = {'bookId': book_id, 'chapterId': chapter_id}
                    
                    # 直接使用requests库发送请求
                    response = session.get(url, params=params, timeout=10)
                    response.raise_for_status()
                    data = response.json()
                    
                    if data.get('success') and data.get('code') == 1 and 'data' in data:
                        chapter_data = data['data']
                        if 'chapter' in chapter_data and 'content' in chapter_data['chapter']:
                            # 获取原始HTML内容
                            html_content = chapter_data['chapter']['content']
                            # 简单去除HTML标签，提取纯文本
                            text_content = re.sub(r'<[^>]+>', '', html_content)  # 简单移除标签
                            text_content = re.sub(r'\s+', ' ', text_content).strip()  # 规范化空白符
                            return text_content
                except Exception as e:
                    continue
            
            # 如果所有API都失败
            return ""
        
        # 在线程池中执行上面的函数
        return await asyncio.to_thread(get_content_in_thread)

async def extract_book_id_from_url(url: str) -> Optional[str]:
    """
    尝试从URL中提取bookId参数。
    支持标准章文品读链接和抖音分享链接。
    """
    # 1. 尝试标准的章文品读平台链接解析
    parsed_url = urllib.parse.urlparse(url)
    query_params = urllib.parse.parse_qs(parsed_url.query)
    book_id_list = query_params.get('bookId')
    if book_id_list:
        return book_id_list[0]

    # 2. 尝试从路径中匹配
    path_parts = parsed_url.path.strip('/').split('/')
    if 'book' in path_parts:
        try:
            book_index = path_parts.index('book')
            if book_index + 1 < len(path_parts):
                 potential_id = path_parts[book_index + 1]
                 # 简单检查是否像ID (数字字符串)
                 if potential_id.isdigit():
                     return potential_id
        except (ValueError, IndexError):
            pass

    # 3. 尝试解析抖音分享链接
    douyin_share_pattern = r"https?://www\.iesdouyin\.com/share/microapp/"
    if re.search(douyin_share_pattern, url):
        logger.info("检测到抖音分享链接，正在尝试从中提取书籍ID...")
        try:
            # 发送 GET 请求获取网页内容
            async with httpx.AsyncClient(timeout=15) as client:  # 增加超时时间到15秒
                headers = {
                    'User-Agent': USER_AGENT
                }
                response = await client.get(url, headers=headers)
                response.raise_for_status()
                html_content = response.text

            # 在HTML内容中查找 id="RENDER_DATA" 的 script 标签
            render_data_match = re.search(r'<script\s+id="RENDER_DATA"[^>]*>(.*?)</script>', html_content, re.DOTALL | re.IGNORECASE)
            if not render_data_match:
                logger.warning("在网页源码中未找到 id='RENDER_DATA' 的 <script> 标签。")
                # 尝试直接在HTML中查找bookId
                book_id_match = re.search(r'["\']bookId["\']\s*[:=]\s*["\']?(\d+)', html_content)
                if book_id_match:
                    book_id = book_id_match.group(1)
                    logger.info(f"直接在HTML中找到书籍ID: {book_id}")
                    return book_id
                return None

            # 提取 script 标签内的内容 (URL 编码的 JSON)
            encoded_json_data = render_data_match.group(1).strip()

            # 对提取到的数据进行 URL 解码 (可能需要解码两次)
            # 第一次解码
            decoded_once = urllib.parse.unquote(encoded_json_data)
            # 第二次解码，以处理嵌套编码 (如 bookId%253D...)
            decoded_json_str = urllib.parse.unquote(decoded_once)

            # 尝试多种模式匹配bookId
            book_id_patterns = [
                r'["\']bookId["\']\s*[:=]\s*["\']?(\d+)',  # 标准格式: "bookId": "123456"
                r'bookId=(\d+)',                           # URL格式: bookId=123456
                r'bookId%3D(\d+)',                         # URL编码格式: bookId%3D123456
                r'bookId%253D(\d+)'                        # 双重URL编码格式: bookId%253D123456
            ]
            
            for pattern in book_id_patterns:
                book_id_match = re.search(pattern, decoded_json_str)
                if book_id_match:
                    book_id = book_id_match.group(1)
                    logger.info(f"成功匹配到书籍ID: {book_id}，使用模式: {pattern}")
                    return book_id
            
            # 如果上面的模式没找到，尝试匹配 Query 字段中的编码形式
            query_match = re.search(r'"Query"\s*:\s*"([^"]*)"', decoded_json_str)
            if query_match:
                query_value = query_match.group(1)
                # 在 Query 值中再次解码并查找 bookId
                decoded_query = urllib.parse.unquote(query_value)
                
                # 对解码后的query再次应用所有模式
                for pattern in book_id_patterns:
                    inner_book_id_match = re.search(pattern, decoded_query)
                    if inner_book_id_match:
                        book_id = inner_book_id_match.group(1)
                        logger.info(f"成功在 Query 参数中找到书籍ID: {book_id}")
                        return book_id
            
            # 如果仍然没找到，尝试在整个HTML内容中查找
            logger.info("在解码后的JSON中未找到书籍ID，尝试在整个HTML内容中查找...")
            for pattern in book_id_patterns:
                book_id_match = re.search(pattern, html_content)
                if book_id_match:
                    book_id = book_id_match.group(1)
                    logger.info(f"在HTML内容中找到书籍ID: {book_id}")
                    return book_id

            logger.warning("在所有可能的位置都未找到书籍ID。")
            return None

        except httpx.RequestError as e:
            logger.error(f"请求抖音分享链接失败: {e}")
            return None
        except Exception as e:  # 捕获其他可能的错误，如正则错误、JSON解析错误等
            logger.error(f"解析抖音分享链接时出错: {e}")
            return None

    # 如果以上方法都未找到，则返回 None
    logger.warning("无法从提供的链接中提取书籍ID。")
    return None

async def update_download_status(user_id: int, book_name: str, total_chapters: int):
    """更新用户的下载状态"""
    if user_id not in zhangwen_download_status:
        zhangwen_download_status[user_id] = []
    
    # 检查是否已存在相同书籍的下载状态
    for progress in zhangwen_download_status[user_id]:
        if progress['book_name'] == book_name:
            progress['total_chapters'] = total_chapters
            progress['current_chapter'] = 0
            progress['progress'] = f"《{book_name}》 (0/{total_chapters})"
            return
    
    # 添加新的下载状态
    zhangwen_download_status[user_id].append({
        'book_name': book_name,
        'total_chapters': total_chapters,
        'current_chapter': 0,
        'progress': f"《{book_name}》 (0/{total_chapters})"
    })

async def update_chapter_progress(user_id: int, book_name: str, current_chapter: int):
    """更新章节下载进度"""
    if user_id in zhangwen_download_status:
        for progress in zhangwen_download_status[user_id]:
            if progress['book_name'] == book_name:
                progress['current_chapter'] = current_chapter
                progress['progress'] = f"《{book_name}》 ({current_chapter}/{progress['total_chapters']})"
                break

def clear_download_status(user_id: int, book_name: str):
    """清理下载状态"""
    if user_id in zhangwen_download_status:
        zhangwen_download_status[user_id] = [p for p in zhangwen_download_status[user_id] if p['book_name'] != book_name]
        if not zhangwen_download_status[user_id]:  # 如果用户没有其他下载任务，删除用户记录
            del zhangwen_download_status[user_id]

async def download_single_chapter(downloader, book_id, chapter, index, total, user_id, book_title):
    """下载单个章节"""
    chapter_id = chapter['id']
    chapter_name = chapter['name']
    sequence_raw = chapter.get('sequence', str(index+1))
    
    try:
        # 尝试将序号转换为整数
        sequence_int = int(float(sequence_raw))  # 先转 float 再转 int，以处理 "1.0" 这样的字符串
        sequence_str = str(sequence_int)
    except (ValueError, TypeError):
        # 如果转换失败（例如 '?' 或 None），则使用原始值
        sequence_str = str(sequence_raw)
    
    # 获取章节内容，添加重试机制
    content = ""
    max_retries = 3
    retry_count = 0
    
    # 定义线程中执行的函数
    def get_content_in_thread():
        # 在线程中创建全新的会话
        session = requests.Session()
        session.headers.update({
            'User-Agent': USER_AGENT,
        })
        
        # 尝试所有API地址
        for base_url in BASE_URLS:
            try:
                url = f"{base_url}/book/chapter"
                params = {'bookId': book_id, 'chapterId': chapter_id}
                
                # 直接使用requests库发送请求
                response = session.get(url, params=params, timeout=10)
                response.raise_for_status()
                data = response.json()
                
                if data.get('success') and data.get('code') == 1 and 'data' in data:
                    chapter_data = data['data']
                    if 'chapter' in chapter_data and 'content' in chapter_data['chapter']:
                        # 获取原始HTML内容
                        html_content = chapter_data['chapter']['content']
                        # 简单去除HTML标签，提取纯文本
                        text_content = re.sub(r'<[^>]+>', '', html_content)  # 简单移除标签
                        text_content = re.sub(r'\s+', ' ', text_content).strip()  # 规范化空白符
                        return text_content
            except Exception as e:
                continue
        
        # 如果所有API都失败
        return ""
    
    while not content and retry_count < max_retries:
        # 每次重试前增加延迟
        if retry_count > 0:
            delay = 1 + retry_count * 2  # 1秒, 3秒, 5秒
            await asyncio.sleep(delay)
            logger.info(f"重试获取章节 '{chapter_name}' (第{retry_count}次)")
        
        # 在线程池中执行上面的函数
        content = await asyncio.to_thread(get_content_in_thread)
        retry_count += 1
    
    # 更新进度
    await update_chapter_progress(user_id, book_title, index+1)
    
    if content:
        logger.debug(f"章节 '{chapter_name}' 下载完成 ({index+1}/{total})")
        return f"第{sequence_str}章 {chapter_name}\n{content}\n"
    else:
        logger.warning(f"章节 '{chapter_name}' (ID: {chapter_id}) 下载失败或内容为空。")
        return f"第{sequence_str}章 {chapter_name}\n[下载失败]\n"

async def download_book_to_file(book_id: str, user_id: int, book_name: str) -> Optional[str]:
    """下载整本书到临时文件，使用多线程并行下载，每个API使用单独的线程"""
    
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8', suffix='.txt')
    temp_path = temp_file.name
    logger.info(f"创建临时文件: {temp_path}")
    
    # 用于跟踪API成功率的字典
    api_success = {base_url: 1 for base_url in BASE_URLS}  # 初始化为1，确保所有API都被使用
    
    # 定义获取书籍信息的函数
    def get_book_info_thread():
        """在线程中获取书籍信息"""
        for base_url in BASE_URLS:
            try:
                session = requests.Session()
                session.headers.update({'User-Agent': USER_AGENT})
                
                url = f"{base_url}/book/detail"
                params = {'bookId': book_id}
                logger.info(f"尝试从 {url} 获取书籍信息")
                
                response = session.get(url, params=params, timeout=10)
                response.raise_for_status()
                data = response.json()
                
                if data.get('success') and data.get('code') == 1:
                    book_info = data.get('data')
                    if book_info:
                        logger.info(f"成功从 {url} 获取书籍信息")
                        # 记录成功的API
                        api_success[base_url] += 1
                        return book_info
            except Exception as e:
                logger.warning(f"从 {url} 获取书籍信息失败: {e}")
                continue
        
        logger.error("所有API都无法获取书籍信息")
        return None
    
    # 定义获取章节列表的函数
    def get_chapter_list_thread():
        """在线程中获取章节列表"""
        for base_url in BASE_URLS:
            try:
                session = requests.Session()
                session.headers.update({'User-Agent': USER_AGENT})
                
                url = f"{base_url}/book/menu"
                params = {'bookId': book_id}
                logger.info(f"尝试从 {url} 获取章节列表")
                
                response = session.get(url, params=params, timeout=10)
                response.raise_for_status()
                data = response.json()
                
                if data.get('success') and data.get('code') == 1:
                    menu_data = data.get('data')
                    if menu_data and 'menu' in menu_data and len(menu_data['menu']) > 0:
                        chapters = menu_data['menu'][0].get('chapters', [])
                        if chapters:
                            logger.info(f"成功从 {url} 获取章节列表，共 {len(chapters)} 章")
                            # 记录成功的API
                            api_success[base_url] += 1
                            return chapters
            except Exception as e:
                logger.warning(f"从 {url} 获取章节列表失败: {e}")
                continue
        
        logger.error("所有API都无法获取章节列表")
        return []
    
    # 定义下载单个章节的函数
    def download_chapter_thread(base_url, book_id, chapter, index, total_chapters):
        """在线程中下载单个章节"""
        max_retries = 3  # 最大重试次数
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 为每个请求创建新的会话，确保独立的状态
                session = requests.Session()
                session.headers.update({
                    'User-Agent': USER_AGENT,
                })
                
                chapter_id = chapter['id']
                chapter_name = chapter['name']
                sequence_raw = chapter.get('sequence', str(index+1))
                
                try:
                    # 尝试将序号转换为整数
                    sequence_int = int(float(sequence_raw))
                    sequence_str = str(sequence_int)
                except (ValueError, TypeError):
                    sequence_str = str(sequence_raw)
                
                # 获取章节内容
                url = f"{base_url}/book/chapter"
                params = {'bookId': book_id, 'chapterId': chapter_id}
                
                if retry_count > 0:
                    logger.debug(f"第{retry_count}次重试从 {url} 获取章节 {index+1}/{total_chapters}: {chapter_name}")
                else:
                    logger.debug(f"尝试从 {url} 获取章节 {index+1}/{total_chapters}: {chapter_name}")
                
                # 使用完整的请求参数，确保会话正确
                response = session.get(url, params=params, timeout=15)  # 增加超时时间
                response.raise_for_status()
                data = response.json()
                
                # 打印完整的响应数据用于调试
                logger.debug(f"API响应: {data}")
                
                # 更详细的数据检查和错误处理
                if not data:
                    logger.warning(f"从 {url} 获取的响应为空")
                    retry_count += 1
                    time.sleep(1)  # 重试前等待1秒
                    continue
                    
                # 检查响应结构
                if data.get('success') and data.get('code') == 1:
                    chapter_data = data.get('data', {})
                    chapter_info = chapter_data.get('chapter', {})
                    html_content = chapter_info.get('content', '')
                    
                    if html_content:
                        # 简单去除HTML标签，提取纯文本
                        content = re.sub(r'<[^>]+>', '', html_content)  # 简单移除标签
                        content = re.sub(r'\s+', ' ', content).strip()  # 规范化空白符
                        
                        if content:
                            logger.info(f"成功从 {url} 获取章节 {index+1}/{total_chapters} 内容")
                            # 记录成功的API
                            api_success[base_url] += 1
                            return {
                                'index': index,
                                'sequence': sequence_str,
                                'name': chapter_name,
                                'content': content,
                                'success': True
                            }
                
                # 如果执行到这里，说明获取失败
                error_message = data.get('message', '未知错误')
                logger.warning(f"从 {url} 获取章节 {index+1}/{total_chapters} 失败: {error_message}")
                retry_count += 1
                time.sleep(1)  # 重试前等待1秒
                
            except Exception as e:
                logger.error(f"下载章节时出错: {e}")
                retry_count += 1
                time.sleep(1)  # 重试前等待1秒
        
        # 所有重试都失败了
        return {
            'index': index,
            'sequence': sequence_str,
            'name': chapter_name,
            'content': "",
            'success': False
        }
    
    # 在线程池中获取书籍信息
    logger.info(f"开始获取书籍信息 (ID: {book_id})...")
    book_info = await asyncio.to_thread(get_book_info_thread)
    
    if not book_info:
        logger.error("获取书籍信息失败")
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return None
    
    # 获取关键信息
    book_title = book_info.get('name') or '未知书名'
    author = book_info.get('authorname') or book_info.get('author') or '未知作者'
    intro = book_info.get('introduce') or '暂无简介'
    logger.info(f"书籍信息: 《{book_title}》 作者: {author}")
    
    # 在线程池中获取章节列表
    logger.info("开始获取章节列表...")
    chapters = await asyncio.to_thread(get_chapter_list_thread)
    
    if not chapters:
        logger.error("获取章节列表失败")
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return None
    
    # 更新下载状态
    await update_download_status(user_id, book_title, len(chapters))
    
    # 写入书籍元信息
    temp_file.write(f"{book_title}\n")
    temp_file.write(f"作者: {author}\n")
    temp_file.write(f"简介: {intro}\n")
    temp_file.write("="*50 + "\n")
    temp_file.close()
    logger.info("已写入书籍元信息")
    
    # 根据成功率排序API
    sorted_apis = sorted(api_success.items(), key=lambda x: x[1], reverse=True)
    logger.info(f"API成功率排序: {sorted_apis}")
    
    # 直接使用所有API，不考虑成功率
    working_apis = BASE_URLS
    logger.info(f"使用所有API: {working_apis}")
    
    # 使用多线程并行下载章节
    logger.info(f"开始并行下载 {len(chapters)} 个章节，使用 {len(working_apis)} 个API...")
    
    # 为每个API创建一个下载队列
    api_queues = {}
    for base_url in working_apis:
        api_queues[base_url] = []
    
    # 将章节均匀分配到不同的API队列
    for i, chapter in enumerate(chapters):
        # 使用轮询方式分配章节到不同API
        base_url = working_apis[i % len(working_apis)]
        api_queues[base_url].append((chapter, i))
    
    # 确保每个API都有任务
    logger.info(f"章节分配情况:")
    for base_url, queue in api_queues.items():
        logger.info(f"API {base_url}: {len(queue)} 章节")
        
    # 为每个API创建一个下载线程
    async def download_api_queue(base_url, queue):
        results = []
        for chapter, index in queue:
            # 在线程中下载章节
            result = await asyncio.to_thread(
                download_chapter_thread, 
                base_url, 
                book_id, 
                chapter, 
                index, 
                len(chapters)
            )
            results.append(result)
            # 更新进度
            await update_chapter_progress(user_id, book_title, len(results))
            # 添加小延迟避免请求过快
            await asyncio.sleep(0.1)  # 减少延迟到0.1秒
            
            # 如果下载失败，尝试使用其他成功率高的API
            if not result['success'] and len(working_apis) > 1:
                logger.info(f"尝试使用其他API下载失败的章节 {index+1}/{len(chapters)}")
                for retry_api in working_apis:
                    if retry_api != base_url:  # 不要使用已经失败的API
                        retry_result = await asyncio.to_thread(
                            download_chapter_thread, 
                            retry_api, 
                            book_id, 
                            chapter, 
                            index, 
                            len(chapters)
                        )
                        if retry_result['success']:
                            logger.info(f"使用备用API {retry_api} 成功下载章节 {index+1}/{len(chapters)}")
                            results[-1] = retry_result  # 替换失败的结果
                            break
                        await asyncio.sleep(0.1)  # 重试之间添加延迟
        return results
    
    # 创建所有API的下载任务
    tasks = []
    for base_url, queue in api_queues.items():
        if queue:  # 只为有章节的队列创建任务
            tasks.append(download_api_queue(base_url, queue))
    
    # 并行执行所有API的下载任务
    all_results = []
    if tasks:
        results_lists = await asyncio.gather(*tasks)
        for results in results_lists:
            all_results.extend(results)
    
    # 按章节索引排序结果
    all_results.sort(key=lambda x: x['index'])
    
    # 将结果写入文件
    with open(temp_path, 'a', encoding='utf-8') as f:
        success_count = 0
        for result in all_results:
            if result['success']:
                f.write(f"第{result['sequence']}章 {result['name']}\n")
                f.write(result['content'] + "\n\n")
                success_count += 1
            else:
                f.write(f"第{result['sequence']}章 {result['name']}\n[下载失败]\n\n")
    
    logger.info(f"下载完成! 成功: {success_count}/{len(chapters)} 章")
    
    return temp_path

async def upload_to_webdav(file_path: str, file_name: str) -> Optional[str]:
    """将文件上传到WebDAV"""
    try:
        # 读取文件内容
        with open(file_path, 'rb') as f:
            file_content = f.read()
        
        # 构建远程文件路径
        remote_path = f"{WEBDAV_FOLDER}/{file_name}"
        webdav_url = f"{WEBDAV_URL}/{remote_path}"
        
        # 构建认证信息
        auth = (WEBDAV_USERNAME, WEBDAV_PASSWORD)
        
        # 首先检查目录是否存在
        try:
            async with httpx.AsyncClient(auth=auth, verify=False) as client:
                # 检查文件夹是否存在
                folder_url = f"{WEBDAV_URL}/{WEBDAV_FOLDER}"
                response = await client.request("PROPFIND", folder_url, headers={"Depth": "0"})
                
                # 如果文件夹不存在，创建它
                if response.status_code == 404:
                    logger.info(f"WebDAV文件夹 {WEBDAV_FOLDER} 不存在，尝试创建")
                    mkdir_response = await client.request("MKCOL", folder_url)
                    if mkdir_response.status_code not in (201, 200, 207):
                        logger.error(f"创建WebDAV文件夹失败: {mkdir_response.status_code}")
                        # 继续尝试上传，可能文件夹已存在
        except Exception as e:
            logger.error(f"检查WebDAV文件夹时发生错误: {str(e)}，尝试直接上传")
        
        # 上传文件
        async with httpx.AsyncClient(auth=auth, verify=False) as client:
            response = await client.put(webdav_url, content=file_content, timeout=60)
            
            if response.status_code in (200, 201, 204):
                # 构建下载链接 (使用alist的Web界面链接)
                base_url = WEBDAV_URL.split('/dav')[0]
                
                # 使用简化路径格式
                download_url = f"{base_url}/{WEBDAV_FOLDER}/{file_name}"
                
                logger.info(f"文件已成功上传到WebDAV，下载链接: {download_url}")
                return download_url
            else:
                logger.error(f"WebDAV上传失败，状态码: {response.status_code}")
                return None
    except Exception as e:
        logger.error(f"上传到WebDAV时发生错误: {str(e)}", exc_info=True)
        return None
