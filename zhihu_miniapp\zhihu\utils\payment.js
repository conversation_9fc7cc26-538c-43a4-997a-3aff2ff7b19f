define("utils/payment.js",(function(e,r,n,t,i,a,o,s,u,c,l,f,d,p,y,b,h,m,I,w,v,P,g,_,A,O,x,S,k,j,E,T,Y,M,N,U,D,L,$,q){"use strict";function C(e,r){var n,t,i,a,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,t&&(i=2&a[0]?t.return:a[0]?t.throw||((i=t.return)&&i.call(t),0):t.next)&&!(i=i.call(t,a[1])).done)return i;switch(t=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,t=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(i=o.trys,(i=i.length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=r.call(e,o)}catch(e){a=[6,e],t=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}}var F=Object.defineProperty,R=Object.defineProperties,W=Object.getOwnPropertyDescriptors,z=Object.getOwnPropertySymbols,K=Object.prototype.hasOwnProperty,V=Object.prototype.propertyIsEnumerable,X=function(e,r,n){return r in e?F(e,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[r]=n},G=function(e,r){for(var n in r||(r={}))K.call(r,n)&&X(e,n,r[n]);var t=!0,i=!1,a=void 0;if(z)try{for(var o,s=z(r)[Symbol.iterator]();!(t=(o=s.next()).done);t=!0){n=o.value;V.call(r,n)&&X(e,n,r[n])}}catch(e){i=!0,a=e}finally{try{t||null==s.return||s.return()}finally{if(i)throw a}}return e},B=function(e,r){return R(e,W(r))},H=function(e,r,n){return new i((function(t,a){var o=function(e){try{u(n.next(e))}catch(e){a(e)}},s=function(e){try{u(n.throw(e))}catch(e){a(e)}},u=function(e){return e.done?t(e.value):i.resolve(e.value).then(o,s)};u((n=n.apply(e,r)).next())}))};!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==p?p:{},r=(new Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="b63275a2-8799-4f78-b7f8-89928fe40484",e._sentryDebugIdIdentifier="sentry-dbid-b63275a2-8799-4f78-b7f8-89928fe40484")}catch(e){}}();var J=e("../common/vendor.js"),Q=e("./gift.js"),Z=e("./payUtils.js"),ee=e("./paymentSubsApi.js"),re=e("./commonPay.js"),ne=function(e,r){return new i((function(n,t){ks.pay(B(G({},e),{success:function(e){return H(this,null,(function(){var i,a,o;return C(this,(function(s){switch(s.label){case 0:return[4,ae({platform:"ks",response:e,dealId:r})];case 1:return i=s.sent(),a=i.isPay,o=i.err,a?n(e):t(o),[2]}}))}))},fail:function(e){J.index.$emit(ee.PAYMENT_PAY_FAIL,{platform:"ks",error:e}),t(e)}}))}))},te=function(e,r){var n=r.isRenew,t=r.iosParams,a=void 0===t?{}:t;return new i((function(t,i){var o=function(e){J.index.$emit(ee.PAYMENT_PAY_FAIL,{platform:"dy",error:e}),i(e)},s=function(e){return H(this,null,(function(){var n,a,o;return C(this,(function(s){switch(s.label){case 0:return[4,ae({platform:"dy",response:e,dealId:r.dealId})];case 1:return n=s.sent(),a=n.isPay,o=n.err,a?t(e):i(o),[2]}}))}))};n?tt.createSignOrder(B(G({businessType:2},e),{success:function(e){tt.sign({businessType:2,orderId:e.authOrderId,success:s,fail:o})},fail:o})):tt.requestOrder(B(G({},e),{success:function(e){tt.getOrderPayment(B(G({orderId:e.orderId},Z.isIos()?a:{}),{success:s,fail:o}))},fail:o}))}))},ie=function(e){return new i((function(r,n){wx.requestPayment(B(G({},e),{success:function(e){return H(this,null,(function(){var t,i,a;return C(this,(function(o){switch(o.label){case 0:return[4,ae({platform:"wx",response:e})];case 1:return t=o.sent(),i=t.isPay,a=t.err,i?r(e):n(a),[2]}}))}))},fail:function(e){J.index.$emit(ee.PAYMENT_PAY_FAIL,{platform:"wx",error:e}),n(e)}}))}))},ae=function(e){return H(n,[e],(function(e){var r,n,t,i,a,o,s;return C(this,(function(u){switch(u.label){case 0:return r=e.platform,n=e.response,t=e.dealId,i={isPay:!0,err:null},a=function(){J.index.$emit(ee.PAYMENT_PAY_SUCCESS,{platform:r,response:n})},t?[4,re.getPayStaus({dealId:t})]:[3,2];case 1:return(o=u.sent())?a():(s=new Error("未查询到支付结果"),J.index.$emit(ee.PAYMENT_PAY_FAIL,{platform:r,error:s}),i.err=s),i.isPay=o,[3,3];case 2:a(),u.label=3;case 3:return[2,i]}}))}))};n.pay=function(e,r){return H(n,null,(function(){var t,a;return C(this,(function(o){switch(o.label){case 0:return function(e,r){return"ks"===e?Z.isIos()?ks.canIUse("pay.object.payType.IAPPurchase"):ks.canIUse("pay"):"dy"===e?Z.isIos()?tt.canIUse("getOrderPayment.object.imId")&&tt.canIUse("requestOrder"):r?tt.canIUse("createSignOrder"):tt.canIUse("requestOrder"):"wx"===e&&wx.canIUse("requestPayment")}(t=Q.isMiniDY()?"dy":Q.isMiniKS()?"ks":Q.isMiniWX()?"wx":"unkonwn",r.isRenew)?[4,function(e,r,t){return H(n,null,(function(){var n,i,a,o,s,u,c,l,f,d,p,y;return C(this,(function(b){switch(b.label){case 0:return n=e.orderNo,i=e.orderInfoToken,a=e.data,o=e.byteAuthorization,s=e.timestamp,u=e.nonceStr,c=e.package,l=e.signType,f=e.sign,d=r.paymentChannel,"ks"!==t?[3,2]:[4,re.getProvider("payment")];case 1:return p=b.sent(),y=p.provider,[2,G({serviceId:"1",orderInfo:{order_no:n,order_info_token:i},paymentChannel:{provider_channel_type:d,provider:null==y?void 0:y[0]}},Z.isIos()?{payType:"IAPPurchase"}:{})];case 2:return[2,"dy"===t?{data:a,byteAuthorization:o}:"wx"===t?{timeStamp:s,nonceStr:u,package:c,signType:l,paySign:f}:{}]}}))}))}(e,r,t)]:[2,(J.index.$emit(ee.PAYMENT_VERSION_LOW,{platform:t,error:{message:"当前客户端版本过低，请升级",code:"PAYMENT_VERSION_LOW"}}),i.reject(new Error("当前客户端版本过低，请升级")))];case 1:return a=o.sent(),[2,Q.isMiniKS()?ne(a,r.dealId):Q.isMiniDY()?te(a,r):Q.isMiniWX()?ie(a,r.dealId):function(){return i.reject(new Error("目前不支持该平台支付"))}]}}))}))}}));
//# sourceMappingURL=payment.js.map