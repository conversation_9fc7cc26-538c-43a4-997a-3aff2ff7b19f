define("components/pay-part-read-page/pay-part-read-page.js",(function(e,t,s,n,a,r,o,i,d,u,l,p,c,y,f,m,T,j,v,h,I,g,x,_,C,k,w,b,P,U,D,E,S,R,q,H,A,B,G,L){"use strict";!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==y?y:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="9e712a7d-6edd-439c-a821-518e770110ed",e._sentryDebugIdIdentifier="sentry-dbid-9e712a7d-6edd-439c-a821-518e770110ed")}catch(e){}}();var M=e("../../common/vendor.js"),N=e("../../utils/track.js"),V=e("../../stores/modules/user/index.js");e("../../stores/modules/history/index.js"),e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js");var F=e("../../stores/modules/purchaseInfo/index.js");e("../../stores/modules/clientInfo/index.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js"),e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js");var K=e("../pay-card/pay.js");e("../../utils/appFetch.js"),e("../../utils/request.js"),e("../../writeConstant.js"),e("../../utils/appInit.js"),e("../../utils/onGetUdid.js"),e("../../utils/utils.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),e("../../utils/pageHelper.js"),e("../../utils/constant.js"),e("../../pages/manuscript/store.js"),Math||(O+Y)();var O=function(){return"../pay-card/pay-card-switch.js"},Y=function(){return"../pay-card/pay-card-tile.js"},$={__name:"pay-part-read-page",props:{payCardType:{default:""},sectionId:{default:""},title:{default:""},contentType:{default:""},ifShowPayCard:{default:!1},toolTipText:{},showIosTruncate:{default:!1},iosTruncateText:{default:""},iosTruncateImage:{default:""},isIos:{default:!1},handleTimeUp:{type:n,required:!0,default:function(){}}},setup:function(e){var t=e,s=F.usePurchaseInfo(),n=M.storeToRefs(s),a=n.isGetPurchase,r=n.remindText,o=V.useUserStore(),i=M.storeToRefs(o).isLogin,d=M.toRefs(t),u=d.sectionId,l=d.title,p=d.toolTipText,c=d.isIos,y=d.contentType;M.provide("sectionId",u),M.provide("contentType",y);var f=function(){M.index.$emit(K.PAY_CARD_CLICK_EVENT)},m=function(){M.trackEvent({event_type:0,event_name:"文稿页点击截断"}),N.track({elementText:p.value,elementType:"Block",eventType:"Click",message:"文稿页截断处文字链点击",extra:{sectionId:u.value,title:l.value}}),N.track({elementType:"Block",eventType:"Click",moduleId:"vipmini_recharge_panel_text_link",message:"知乎小说-充值面板文字链-模块单击",extra:{sectionId:u.value,sectionName:l.value,mini_content_type:y.value}}),M.index.pageScrollTo({scrollTop:999999})},T=function(){M.trackEvent({event_type:0,event_name:"文稿页点击截断"}),N.track({elementType:"Card",eventType:"Click",message:"ios截断卡片点击"}),i.value&&M.index.showToast({icon:"none",title:"苹果不支持购买"})};return function(t,s){return M.e({a:e.ifShowPayCard},e.ifShowPayCard?M.e({b:M.unref(a)},M.unref(a)?M.e({c:M.unref(r)},M.unref(r)?{d:M.t(M.unref(r)),e:M.o(m)}:{},{f:!M.unref(c)||M.unref(c)&&!e.showIosTruncate},!M.unref(c)||M.unref(c)&&!e.showIosTruncate?M.e({g:"switch"==e.payCardType},"switch"==e.payCardType?{h:M.p({handleTimeUp:e.handleTimeUp,type:"manuscript"})}:"tile"==e.payCardType?{j:M.p({handleTimeUp:e.handleTimeUp,type:"manuscript"})}:{},{i:"tile"==e.payCardType}):{k:M.t(t.iosPrompt),l:M.o(T),m:"url(".concat(e.iosTruncateImage,")")}):{},{n:M.n(e.payCardType)}):{},{o:M.o(f)})}}},z=M._export_sfc($,[["__scopeId","data-v-8c44b374"]]);tt.createComponent(z)}));
//# sourceMappingURL=pay-part-read-page.js.map