define("components/article-content/article-content.js",(function(e,t,n,a,r,c,i,o,l,u,s,d,b,f,p,m,g,v,y,I,_,D,h,C,T,j,k,S,q,w,x,E,N,O,z,A,B,F,G,H){"use strict";!function(){try{var e=void 0!==s?s:"undefined"!=typeof global?global:void 0!==f?f:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="0b410881-e91c-46b0-b3ee-217b5f9ae347",e._sentryDebugIdIdentifier="sentry-dbid-0b410881-e91c-46b0-b3ee-217b5f9ae347")}catch(e){}}();var J=e("../../common/vendor.js"),K={__name:"article-content",props:{articleTitle:{type:String,required:!0},manuscriptInfo:{type:Object,default:function(){return{}}},manuscriptContent:{type:String,default:""},contentType:{type:Number,default:0}},emits:["clipboardData","handleTagsClick"],setup:function(e,t){var n=t.emit,a=function(){n("clipboardData")},r=function(){n("handleTagsClick")};return function(t,n){var c,i,o,l;return J.e({a:J.t(e.articleTitle),b:J.o(a),c:(null==(c=e.manuscriptInfo)?void 0:c.labels)&&(null==(i=e.manuscriptInfo)?void 0:i.labels.length)},(null==(o=e.manuscriptInfo)?void 0:o.labels)&&(null==(l=e.manuscriptInfo)?void 0:l.labels.length)?{d:J.f(e.manuscriptInfo.labels.slice(0,2),(function(e,t,n){return{a:J.t(t?" · ":""),b:J.t(e),c:t}})),e:J.o(r)}:{},{f:e.manuscriptContent})}}},L=J._export_sfc(K,[["__scopeId","data-v-ba366cfe"]]);tt.createComponent(L)}));
//# sourceMappingURL=article-content.js.map