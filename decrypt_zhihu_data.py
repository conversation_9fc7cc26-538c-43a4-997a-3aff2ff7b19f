import base64
import binascii
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad


def decrypt_zhihu_data(encrypted_data_b64):
    """
    解密知乎小说的加密数据
    """
    # 从shareBookSource.json中获取的AES密钥和IV
    aeskey_hex = "3e6cebec56e4bd95e8ae6ed4bf03c52e"
    aesiv_hex = "54dee60aac15d992a4293886d5119b0c"
    
    try:
        # 1. 解码 Base64
        encrypted_bytes = base64.b64decode(encrypted_data_b64)
        print(f"[DEBUG] Base64 decoded data length: {len(encrypted_bytes)} bytes")

        # 2. 转换十六进制密钥和 IV 为字节
        key_bytes = binascii.unhexlify(aeskey_hex)
        iv_bytes = binascii.unhexlify(aesiv_hex)
        print(f"[DEBUG] Key length: {len(key_bytes)} bytes")
        print(f"[DEBUG] IV length: {len(iv_bytes)} bytes")

        # 3. 创建 AES 解密器 (CBC 模式)
        cipher = AES.new(key_bytes, AES.MODE_CBC, iv_bytes)

        # 4. 解密
        decrypted_bytes = cipher.decrypt(encrypted_bytes)
        print(f"[DEBUG] Raw decrypted data length: {len(decrypted_bytes)} bytes")

        # 5. 移除 PKCS7 填充
        unpadded_bytes = unpad(decrypted_bytes, AES.block_size, style='pkcs7')
        print(f"[DEBUG] Unpadded data length: {len(unpadded_bytes)} bytes")

        # 6. 转换为字符串
        plaintext = unpadded_bytes.decode('utf-8')
        return plaintext

    except Exception as e:
        print(f"[ERROR] Decryption failed: {e}")
        return None


def main():
    # 你提供的加密数据
    encrypted_data = "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

        print("开始解密数据...")
        decrypted_text = decrypt_zhihu_data(encrypted_data)
        
        if decrypted_text:
            print("\n[SUCCESS] 解密成功!")
            print("-" * 50)
            print(decrypted_text)
            print("-" * 50)

            # 保存解密后的内容到文件
            try:
                with open('decrypted_result.txt', 'w', encoding='utf-8') as f:
                    f.write(decrypted_text)
                print("\n[INFO] 解密内容已保存到 'decrypted_result.txt'")
            except Exception as e:
                print(f"\n[WARNING] 无法保存解密内容到文件: {e}")
        else:
            print("\n[FAILURE] 解密失败.")

if __name__ == "__main__":
    main()