define("utils/paymentSubsApi.js",(function(_,e,S,C,E,T,n,t,d,s,A,R,a,b,i,r,y,U,c,f,u,o,O,p,H,K,l,I,P,g,D,L,N,Y,m,v,M,w,X,h){"use strict";!function(){try{var _=void 0!==A?A:"undefined"!=typeof global?global:void 0!==b?b:{},e=(new Error).stack;e&&(_._sentryDebugIds=_._sentryDebugIds||{},_._sentryDebugIds[e]="02d2cf73-f6d6-4e6b-b8da-3be80e57c679",_._sentryDebugIdIdentifier="sentry-dbid-02d2cf73-f6d6-4e6b-b8da-3be80e57c679")}catch(_){}}(),S.CHECK_STATUS_ERROR="CHECK_STATUS_ERROR",S.CHECK_STATUS_EXCEED_COUNT_ERROR="CHECK_STATUS_EXCEED_COUNT_ERROR",S.CHECK_STATUS_POLL_START="CHECK_STATUS_POLL_START",S.CHECK_STATUS_SUCCESS="CHECK_STATUS_SUCCESS",S.PAYMENT_PAY_FAIL="payment_pay_fail",S.PAYMENT_PAY_SUCCESS="payment_pay_success",S.PAYMENT_VERSION_LOW="payment_version_low"}));
//# sourceMappingURL=paymentSubsApi.js.map