import base64
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import hashlib

class Decryptor:
    def __init__(self, key, iv):
        """
        初始化解密器
        
        Args:
            key (str): AES密钥(十六进制字符串)
            iv (str): 初始化向量(十六进制字符串)
        """
        self.key = bytes.fromhex(key)
        self.iv = bytes.fromhex(iv)
    
    def decrypt_base64(self, data):
        """
        Base64 解码
        
        Args:
            data (str): Base64编码的数据
            
        Returns:
            bytes: 解码后的数据
        """
        try:
            return base64.b64decode(data)
        except Exception as e:
            raise ValueError(f"Base64解码失败: {e}")
    
    def decrypt_aes(self, data):
        """
        AES 解密
        
        Args:
            data (bytes): 待解密的数据
            
        Returns:
            bytes: 解密后的数据
        """
        try:
            cipher = AES.new(self.key, AES.MODE_CBC, self.iv)
            decrypted_data = cipher.decrypt(data)
            return unpad(decrypted_data, AES.block_size)
        except Exception as e:
            raise ValueError(f"AES解密失败: {e}")
    
    def decrypt_data(self, encrypted_data):
        """
        解密数据
        
        Args:
            encrypted_data (str): 加密的字符串数据
            
        Returns:
            str: 解密后的字符串
        """
        # 首先进行 Base64 解码
        decoded_data = self.decrypt_base64(encrypted_data)
        # 然后进行 AES 解密
        decrypted_data = self.decrypt_aes(decoded_data)
        return decrypted_data.decode('utf-8')

def main():
    # 从jie.json中提取的密钥和初始化向量
    AES_KEY_HEX = "3e6cebec56e4bd95e8ae6ed4bf03c52e"
    AES_IV_HEX = "54dee60aac15d992a4293886d5119b0c"
    
    # 创建解密器实例
    decryptor = Decryptor(AES_KEY_HEX, AES_IV_HEX)
    
    try:
        # 读取 encrypt_section.json 文件
        with open("C:\\botfanqie\\解密\\encrypt_section.json", "r", encoding='utf-8') as file:
            json_data = file.read()
        
        # 解析 JSON 数据
        data_dict = json.loads(json_data)
        
        # 根据jie.json中的逻辑，需要处理payload和data两个字段
        decrypted_results = {}
        
        if "payload" in data_dict:
            try:
                decrypted_payload = decryptor.decrypt_data(data_dict["payload"])
                decrypted_results["payload"] = decrypted_payload
                print("Payload解密成功!")
            except Exception as e:
                print(f"Payload解密失败: {e}")
        
        if "data" in data_dict:
            try:
                decrypted_data = decryptor.decrypt_data(data_dict["data"])
                decrypted_results["data"] = decrypted_data
                print("Data解密成功!")
            except Exception as e:
                print(f"Data解密失败: {e}")
        
        if not decrypted_results:
            print("错误: JSON 文件中缺少 'payload' 或 'data' 字段")
            return
        
        # 输出解密结果
        for key, value in decrypted_results.items():
            print(f"\n{key}解密后的数据:")
            print(value)
        
        # 将解密结果保存到文件
        with open("C:\\botfanqie\\解密\\decrypted_result.txt", "w", encoding='utf-8') as output_file:
            for key, value in decrypted_results.items():
                output_file.write(f"{key}:\n{value}\n\n")
        print("\n解密结果已保存到 decrypted_result.txt")
        
    except FileNotFoundError:
        print("错误: 未找到 encrypt_section.json 文件")
    except json.JSONDecodeError:
        print("错误: JSON 文件格式不正确")
    except ValueError as e:
        print(f"解密错误: {e}")
    except Exception as e:
        print(f"未知错误: {e}")

if __name__ == "__main__":
    main()