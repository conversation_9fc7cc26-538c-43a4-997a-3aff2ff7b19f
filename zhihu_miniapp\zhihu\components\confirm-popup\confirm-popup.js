define("components/confirm-popup/confirm-popup.js",(function(e,t,n,r,o,i,a,l,c,f,u,d,s,p,m,y,b,g,v,h,A,C,I,S,_,w,j,D,$,x,T,E,O,k,B,M,P,U,q,z){"use strict";function F(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function G(e){return function(e){if(Array.isArray(e))return F(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return F(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(n);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return F(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}!function(){try{var e=void 0!==u?u:"undefined"!=typeof global?global:void 0!==p?p:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="e36f74b7-74ae-4ca2-8196-0c84811817e5",e._sentryDebugIdIdentifier="sentry-dbid-e36f74b7-74ae-4ca2-8196-0c84811817e5")}catch(e){}}();var H=e("../../common/vendor.js"),J={name:"ConfirmPopup",props:{visible:{type:Boolean,default:!1},title:{type:String,default:"提示"},content:{type:String,default:""},contentAlign:{type:String,default:"center",validator:function(e){return["left","center"].includes(e)}},confirmText:{type:String,default:"确认"}},emits:["confirm","update:visible"],methods:{handleConfirm:function(){this.$emit("confirm"),this.$emit("update:visible",!1)},handleCancel:function(){this.$emit("update:visible",!1)}}},K=H._export_sfc(J,[["render",function(e,t,n,r,o,i){var a,l;return H.e({a:n.visible},n.visible?{b:H.o((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.handleCancel&&(a=i).handleCancel.apply(a,G(t))})),c:H.t(n.title),d:H.t(n.content),e:"left"===n.contentAlign?1:"",f:H.t(n.confirmText),g:H.o((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.handleConfirm&&(l=i).handleConfirm.apply(l,G(t))}))}:{})}],["__scopeId","data-v-d3d01991"]]);tt.createComponent(K)}));
//# sourceMappingURL=confirm-popup.js.map