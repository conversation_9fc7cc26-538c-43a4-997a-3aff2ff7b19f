define("components/pay-card/components/promotion-card.js",(function(e,t,r,n,o,u,s,i,a,l,c,d,f,p,y,b,m,h,v,j,g,w,x,I,S,q,_,T,k,D,A,C,E,F,N,H,M,O,G,R){"use strict";function U(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function z(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,u=[],s=!0,i=!1;try{for(r=r.call(e);!(s=(n=r.next()).done)&&(u.push(n.value),!t||u.length!==t);s=!0);}catch(e){i=!0,o=e}finally{try{s||null==r.return||r.return()}finally{if(i)throw o}}return u}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return U(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return U(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(e,t){var r,n,o,u,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return u={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function i(u){return function(i){return function(u){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(o=2&u[0]?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[2&u[0],o.value]),u[0]){case 0:case 1:o=u;break;case 4:return s.label++,{value:u[1],done:!1};case 5:s.label++,n=u[1],u=[0];continue;case 7:u=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==u[0]&&2!==u[0])){s=0;continue}if(3===u[0]&&(!o||u[1]>o[0]&&u[1]<o[3])){s.label=u[1];break}if(6===u[0]&&s.label<o[1]){s.label=o[1],o=u;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(u);break}o[2]&&s.ops.pop(),s.trys.pop();continue}u=t.call(e,s)}catch(e){u=[6,e],n=0}finally{r=o=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,i])}}}!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==p?p:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d0c72bae-d451-4394-a4eb-056062bbb50f",e._sentryDebugIdIdentifier="sentry-dbid-d0c72bae-d451-4394-a4eb-056062bbb50f")}catch(e){}}();var $=e("../../../common/vendor.js"),B=e("../../../utils/utils.js"),J=e("../util.js");e("../../../writeConstant.js"),e("../../../stores/modules/user/index.js"),e("../../../utils/appFetch.js"),e("../../../utils/request.js"),e("../../../stores/modules/history/index.js"),e("../../../stores/modules/login/index.js"),e("../../../utils/constant.js"),e("../../../stores/modules/search-history/index.js"),e("../../../stores/modules/koc-ascription/index.js"),e("../../../stores/modules/purchaseInfo/index.js"),e("../../../pages/manuscript/store.js"),e("../../../stores/modules/clientInfo/index.js"),e("../../../stores/modules/systenInfo/index.js"),e("../../../stores/modules/globalVariable/index.js"),e("../../../stores/modules/trackInfo/index.js"),e("../../../stores/modules/devDebug/index.js"),e("../../../utils/appInit.js"),e("../../../utils/onGetUdid.js"),e("../../../utils/launchOptionsHelper.js"),e("../../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../../utils/encryptHandler.js"),e("../../../utils/requstErrorMiddlewares.js"),e("../../../utils/pageHelper.js"),Array||$.resolveComponent("uni-countdown")(),Math;var K={__name:"promotion-card",props:{vipType:{type:String,required:!1,default:function(){return""}},expireTime:{type:Number,required:!0,default:0},serverNowTime:{type:Number,required:!0,default:0},countdownEnds:{type:n,required:!0,default:function(){}},promotionDescFirst:{type:String,required:!0,default:"全场会员"},promotionDescSecond:{type:String,required:!0,default:"惊喜限时特惠"},discount:{type:Number,required:!0,default:0},splitorColor:{type:String,required:!0,default:"#ffffff"}},setup:function(e){var t=this,r=e,n=$.toRefs(r).vipType,u=$.ref(!1),s=$.reactive({hour:0,minute:0,second:0}),i=$.toRefs(s),a=i.hour,l=i.minute,c=i.second,d=function(){return e=t,n=function(){return V(this,(function(e){switch(e.label){case 0:return u.value?[4,B.sleep(500)]:[3,2];case 1:e.sent(),r.countdownEnds(),u.value=!1,e.label=2;case 2:return[2]}}))},new o((function(t,r){var u=function(e){try{i(n.next(e))}catch(e){r(e)}},s=function(e){try{i(n.throw(e))}catch(e){r(e)}},i=function(e){return e.done?t(e.value):o.resolve(e.value).then(u,s)};i((n=n.apply(e,null)).next())}));var e,n};return $.watch([function(){return r.expireTime},function(){return r.serverNowTime}],(function(e){var t,r,n,o=z(e,2);t=o[0],r=o[1],n=z(J.dateToDiff(t,r),3),a.value=n[0],l.value=n[1],c.value=n[2],u.value=a.value>0||l.value>0||c.value>0}),{immediate:!0}),function(t,r){return $.e({a:u.value},u.value?$.e({b:"discounts-coupon"!=$.unref(n)},($.unref(n),{}),{c:$.o(d),d:$.p({"show-day":!1,hour:$.unref(a),minute:$.unref(l),second:$.unref(c),"border-color":"#F05159","font-size":"9",color:"#FF3838","background-color":"white",splitorColor:e.splitorColor}),e:$.n($.unref(n))}):{})}}},L=$._export_sfc(K,[["__scopeId","data-v-e81a89e5"]]);tt.createComponent(L)}));
//# sourceMappingURL=promotion-card.js.map