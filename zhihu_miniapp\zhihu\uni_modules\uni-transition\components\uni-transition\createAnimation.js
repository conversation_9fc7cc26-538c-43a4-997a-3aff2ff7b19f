define("uni_modules/uni-transition/components/uni-transition/createAnimation.js",(function(t,n,e,r,i,a,o,s,u,c,l,f,y,m,h,d,v,p,b,g,A,w,k,S,j,x,_,I,O,$,E,D,P,X,Y,C,T,Z,N,R){"use strict";function M(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function U(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function q(t){return function(t){if(Array.isArray(t))return M(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(!t)return;if("string"==typeof t)return M(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(e);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return M(t,n)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var z=Object.defineProperty,B=Object.getOwnPropertySymbols,F=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable,H=function(t,n,e){return n in t?z(t,n,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[n]=e},J=function(t,n){for(var e in n||(n={}))F.call(n,e)&&H(t,e,n[e]);var r=!0,i=!1,a=void 0;if(B)try{for(var o,s=B(n)[Symbol.iterator]();!(r=(o=s.next()).done);r=!0){e=o.value;G.call(n,e)&&H(t,e,n[e])}}catch(t){i=!0,a=t}finally{try{r||null==s.return||s.return()}finally{if(i)throw a}}return t};!function(){try{var t=void 0!==l?l:"undefined"!=typeof global?global:void 0!==m?m:{},n=(new Error).stack;n&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[n]="ce6002fb-724f-4c84-87bf-875787d0ec17",t._sentryDebugIdIdentifier="sentry-dbid-ce6002fb-724f-4c84-87bf-875787d0ec17")}catch(t){}}();var K=t("../../../../common/vendor.js"),L=function(){function t(n,e){!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,t),this.options=n,this.animation=K.index.createAnimation(J({},n)),this.currentStepAnimates={},this.next=0,this.$=e}var n,e,r;return n=t,e=[{key:"_nvuePushAnimates",value:function(t,n){var e={};if(e=this.currentStepAnimates[this.next]||{styles:{},config:{}},Q.includes(t)){e.styles.transform||(e.styles.transform="");var r="";"rotate"===t&&(r="deg"),e.styles.transform+="".concat(t,"(").concat(n+r,") ")}else e.styles[t]="".concat(n);this.currentStepAnimates[this.next]=e}},{key:"_animateRun",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},e=this.$.$refs.ani.ref;if(e)return new i((function(r,i){nvueAnimation.transition(e,J({styles:t},n),(function(t){r()}))}))}},{key:"_nvueNextAnimate",value:function(t){var n=this,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2?arguments[2]:void 0,i=t[e];if(i){var a=i.styles,o=i.config;this._animateRun(a,o).then((function(){e+=1,n._nvueNextAnimate(t,e,r)}))}else this.currentStepAnimates={},"function"==typeof r&&r(),this.isEnd=!0}},{key:"step",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.animation.step(t),this}},{key:"run",value:function(t){this.$.animationData=this.animation.export(),this.$.timer=a((function(){"function"==typeof t&&t()}),this.$.durationTime)}}],e&&U(n.prototype,e),r&&U(n,r),t}(),Q=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"];Q.concat(["opacity","backgroundColor"],["width","height","left","right","top","bottom"]).forEach((function(t){L.prototype[t]=function(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];var i;return(i=this.animation)[t].apply(i,q(e)),this}})),e.createAnimation=function(t,n){if(n)return o(n.timer),new L(t,n)}}));
//# sourceMappingURL=createAnimation.js.map