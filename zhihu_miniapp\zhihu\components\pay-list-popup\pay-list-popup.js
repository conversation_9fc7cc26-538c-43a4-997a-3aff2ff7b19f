define("components/pay-list-popup/pay-list-popup.js",(function(e,n,s,t,o,u,a,r,l,i,c,p,d,f,v,m,j,y,_,h,x,b,g,I,k,w,C,R,P,T,D,q,H,M,Y,$,E,O,S,U){"use strict";!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==f?f:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="00a0528c-a820-450c-8eae-b2c47fee9499",e._sentryDebugIdIdentifier="sentry-dbid-00a0528c-a820-450c-8eae-b2c47fee9499")}catch(e){}}();var A=e("../../common/vendor.js"),F=e("../../utils/utils.js");e("../../stores/modules/user/index.js"),e("../../stores/modules/history/index.js"),e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js");var G=e("../../stores/modules/purchaseInfo/index.js");e("../../stores/modules/clientInfo/index.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js"),e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js"),e("../../writeConstant.js"),e("../../utils/appFetch.js"),e("../../utils/request.js"),e("../../utils/appInit.js"),e("../../utils/onGetUdid.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),e("../../utils/pageHelper.js"),e("../../utils/constant.js"),e("../../pages/manuscript/store.js"),Array||A.resolveComponent("uni-popup")(),Math;var L={__name:"pay-list-popup",props:{pageType:{type:String,default:function(){return"manuscript"}},skuCardRef:{type:Object,required:!0,default:function(){return null}}},setup:function(e){var n=e,s=G.usePurchaseInfo(),t=A.storeToRefs(s),o=t.skuList,a=t.clickedSkuId,r=A.toRefs(n),l=(r.skuCardRef,r.pageType),i=A.ref(),c=A.ref("wx"),p=A.ref(0),d=A.ref(""),f=A.ref(""),v=A.getCurrentInstance(),m=function(e){e.show||u((function(){c.value="wx"}),200)},j=function(){var e,n;A.index.$emit("handle_pay_list_start_pay_".concat(l.value),{index:p.value,type:c.value}),null==(n=null==(e=v.refs)?void 0:e.paymentChannelPopupRef)||n.close(),u((function(){c.value="wx"}),200)},y=function(e){var n,s,t,u=(null==(n=o.value)?void 0:n.filter((function(e){return e.skuId===a.value}))[0])||{},r=u||{},l=r.coupon,i=r.salePrice;p.value=e.index;var c=!!l;d.value=c?F.centToYuan(l.discountPrice):F.centToYuan(i),f.value=(null==l?void 0:l.amount)?F.centToYuan(l.amount):"",null==(t=null==(s=v.refs)?void 0:s.paymentChannelPopupRef)||t.open("bottom")},_=function(e){c.value=e.detail.value};return A.onMounted((function(){A.index.$on("show_payment_channel_popup_".concat(l.value),y)})),A.onUnmounted((function(){A.index.$off("show_payment_channel_popup_".concat(l.value),y)})),function(e,n){return A.e({a:A.t(d.value),b:f.value},f.value?{c:A.t(f.value)}:{},{d:A.o(_),e:A.o(j),f:A.sr(i,"a9cd71ba-0",{k:"paymentChannelPopupRef"}),g:A.o(m),h:A.p({"mask-click":!0})})}}},V=A._export_sfc(L,[["__scopeId","data-v-a9cd71ba"]]);tt.createComponent(V)}));
//# sourceMappingURL=pay-list-popup.js.map