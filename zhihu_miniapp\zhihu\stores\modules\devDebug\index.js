define("stores/modules/devDebug/index.js",(function(e,t,n,s,d,i,o,a,r,u,f,b,c,p,g,v,D,I,h,y,l,A,x,z,_,m,H,P,S,j,Z,k,w,E,q,B,C,F,G,J){"use strict";!function(){try{var e=void 0!==f?f:"undefined"!=typeof global?global:void 0!==p?p:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f8fcc4d9-f424-4e77-a471-890ad24831b6",e._sentryDebugIdIdentifier="sentry-dbid-f8fcc4d9-f424-4e77-a471-890ad24831b6")}catch(e){}}();var K=e("../../../common/vendor.js"),L={zapApiHost:"",zapApiPath:"",sandboxId:""},M=K.defineStore("devDebug",{state:function(){return L},actions:{setZapApiHost:function(e){this.zapApiHost=e},setZapApiPath:function(e){this.zapApiPath=e},setSandboxId:function(e){this.sandboxId=e}},persist:{enabled:!0}});n.useDevDebugStore=M}));
//# sourceMappingURL=index.js.map