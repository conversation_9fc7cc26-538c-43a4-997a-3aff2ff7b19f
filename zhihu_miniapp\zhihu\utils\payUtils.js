define("utils/payUtils.js",(function(s,e,o,d,n,t,r,i,u,l,a,c,b,f,m,j,y,I,x,g,v,h,D,p,_,k,S,w,E,N,U,V,q,z,A,B,C,F,G,H){"use strict";!function(){try{var s=void 0!==a?a:"undefined"!=typeof global?global:void 0!==f?f:{},e=(new Error).stack;e&&(s._sentryDebugIds=s._sentryDebugIds||{},s._sentryDebugIds[e]="18d85b4d-7a56-46cf-95d3-815d7b7196ed",s._sentryDebugIdIdentifier="sentry-dbid-18d85b4d-7a56-46cf-95d3-815d7b7196ed")}catch(s){}}(),s("../stores/modules/user/index.js"),s("../stores/modules/history/index.js"),s("../stores/modules/login/index.js"),s("../stores/modules/search-history/index.js"),s("../stores/modules/koc-ascription/index.js"),s("../stores/modules/purchaseInfo/index.js"),s("../stores/modules/clientInfo/index.js");var J=s("../stores/modules/systenInfo/index.js");s("../stores/modules/globalVariable/index.js"),s("../stores/modules/trackInfo/index.js"),s("../stores/modules/devDebug/index.js"),o.isIos=function(){var s=J.useSystemInfoStore();return s.osName||s.init(),s.isIos}}));
//# sourceMappingURL=payUtils.js.map