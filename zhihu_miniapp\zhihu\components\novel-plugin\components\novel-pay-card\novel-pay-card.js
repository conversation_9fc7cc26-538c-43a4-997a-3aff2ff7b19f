define("components/novel-plugin/components/novel-pay-card/novel-pay-card.js",(function(e,n,t,r,s,o,u,i,a,l,c,d,f,p,h,y,v,g,b,m,j,x,I,w,_,S,C,k,P,U,D,E,H,T,M,$,q,A,B,G){"use strict";function N(e,n){var t,r,s,o,u={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]};return o={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function i(o){return function(i){return function(o){if(t)throw new TypeError("Generator is already executing.");for(;u;)try{if(t=1,r&&(s=2&o[0]?r.return:o[0]?r.throw||((s=r.return)&&s.call(r),0):r.next)&&!(s=s.call(r,o[1])).done)return s;switch(r=0,s&&(o=[2&o[0],s.value]),o[0]){case 0:case 1:s=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,r=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(!(s=u.trys,(s=s.length>0&&s[s.length-1])||6!==o[0]&&2!==o[0])){u=0;continue}if(3===o[0]&&(!s||o[1]>s[0]&&o[1]<s[3])){u.label=o[1];break}if(6===o[0]&&u.label<s[1]){u.label=s[1],s=o;break}if(s&&u.label<s[2]){u.label=s[2],u.ops.push(o);break}s[2]&&u.ops.pop(),u.trys.pop();continue}o=n.call(e,u)}catch(e){o=[6,e],r=0}finally{t=s=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,i])}}}var Y=function(e,n,t){return new s((function(r,o){var u=function(e){try{a(t.next(e))}catch(e){o(e)}},i=function(e){try{a(t.throw(e))}catch(e){o(e)}},a=function(e){return e.done?r(e.value):s.resolve(e.value).then(u,i)};a((t=t.apply(e,n)).next())}))};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==p?p:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="54a1ba1e-1772-49e6-8f4d-121b91ca3242",e._sentryDebugIdIdentifier="sentry-dbid-54a1ba1e-1772-49e6-8f4d-121b91ca3242")}catch(e){}}();var F=e("../../../../common/vendor.js"),L=e("../../../../stores/modules/user/index.js");e("../../../../stores/modules/history/index.js"),e("../../../../stores/modules/login/index.js"),e("../../../../stores/modules/search-history/index.js"),e("../../../../stores/modules/koc-ascription/index.js");var O=e("../../../../stores/modules/purchaseInfo/index.js");e("../../../../stores/modules/clientInfo/index.js"),e("../../../../stores/modules/systenInfo/index.js"),e("../../../../stores/modules/globalVariable/index.js"),e("../../../../stores/modules/trackInfo/index.js"),e("../../../../stores/modules/devDebug/index.js");var R=e("../../../../utils/utils.js"),V=e("../../../../utils/constant.js");e("../../../../utils/appFetch.js"),e("../../../../utils/request.js"),e("../../../../writeConstant.js"),e("../../../../utils/appInit.js"),e("../../../../utils/onGetUdid.js"),e("../../../../utils/launchOptionsHelper.js"),e("../../../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../../../utils/encryptHandler.js"),e("../../../../utils/requstErrorMiddlewares.js"),e("../../../../utils/pageHelper.js"),e("../../../../pages/manuscript/store.js"),Math||(z+J)();var z=function(){return"../../../pay-card/pay-card-switch.js"},J=function(){return"../../../pay-card/pay-card-tile.js"},K={__name:"novel-pay-card",props:{sectionId:{type:String,default:""}},setup:function(e){var n=this,t=e,r=O.usePurchaseInfo(),s=L.useUserStore(),o=F.storeToRefs(r),u=o.manuscriptPanelStyle,i=o.skuList,a=o.appletPackage,l=F.ref(null);F.onMounted((function(){return Y(n,null,(function(){return N(this,(function(e){return F.index.$on(V.PAY_SUCCESS_CB,f),F.index.$off("manuscript_get_sku_info"),F.index.getSystemInfo({success:function(e){var n=.75*e.windowHeight;l.value=n}}),[2]}))}))})),F.onUnmounted((function(){return Y(n,null,(function(){return N(this,(function(e){return F.index.$off(V.PAY_SUCCESS_CB,f),[2]}))}))}));var c=F.computed((function(){var e,n;return(null==(e=i.value)?void 0:e.length)&&(null==(n=a.value)?void 0:n.length)?u.value:"tile"})),d=function(){return Y(n,null,(function(){return N(this,(function(e){switch(e.label){case 0:return[4,R.sleep(500)];case 1:return e.sent(),r.getPayCards({ignoreCoupon:1,ignoreSingle:t.sectionId?0:1,contentId:t.sectionId}),[2]}}))}))};function f(){r.getPayCards({ignoreCoupon:1,ignoreSingle:t.sectionId?0:1,contentId:t.sectionId}),s.updateUserInfo()}return function(e,n){return F.e({a:"switch"==F.unref(c)},"switch"==F.unref(c)?{b:F.p({handleTimeUp:d,isNovelPlugin:!0,type:"manuscript"})}:"tile"==F.unref(c)?{d:F.p({handleTimeUp:d,isNovelPlugin:!0,type:"manuscript"})}:{},{c:"tile"==F.unref(c),e:l.value+"px"})}}},Q=F._export_sfc(K,[["__scopeId","data-v-f33143b0"]]);tt.createComponent(Q)}));
//# sourceMappingURL=novel-pay-card.js.map