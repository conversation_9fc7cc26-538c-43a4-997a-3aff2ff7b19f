define("components/common-cards/common-cards.js",(function(e,n,o,t,r,a,l,u,d,s,i,f,c,v,b,m,g,_,p,k,y,I,h,w,D,j,C,x,E,M,R,q,z,A,B,F,G,<PERSON>,J,<PERSON>){"use strict";!function(){try{var e=void 0!==i?i:"undefined"!=typeof global?global:void 0!==v?v:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="6124d235-6be1-4f2e-aad5-47591ef3294a",e._sentryDebugIdIdentifier="sentry-dbid-6124d235-6be1-4f2e-aad5-47591ef3294a")}catch(e){}}();var L=e("../../common/vendor.js");Math||N();var N=function(){return"../book-tags/book-tags.js"},O={__name:"common-cards",props:{data:{default:null}},setup:function(e){var n=e,o=L.toRefs(n).data;return function(e,n){var t,r,a,l,u,d,s,i,f,c,v;return L.e({a:null==(t=L.unref(o))?void 0:t.title,b:L.t(null==(r=L.unref(o))?void 0:r.content),c:null==(a=L.unref(o))?void 0:a.artwork},(null==(l=L.unref(o))?void 0:l.artwork)?{d:"url(".concat(null==(u=L.unref(o))?void 0:u.artwork,")")}:{},{e:null==(s=null==(d=L.unref(o))?void 0:d.labels)?void 0:s.length},(null==(f=null==(i=L.unref(o))?void 0:i.labels)?void 0:f.length)?{f:L.p({data:null==(c=L.unref(o))?void 0:c.labels,likeCount:null==(v=L.unref(o))?void 0:v.likeCount})}:{})}}},P=L._export_sfc(O,[["__scopeId","data-v-aee4e12b"]]);tt.createComponent(P)}));
//# sourceMappingURL=common-cards.js.map