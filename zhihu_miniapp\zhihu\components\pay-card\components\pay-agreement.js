define("components/pay-card/components/pay-agreement.js",(function(e,t,r,n,s,o,u,i,a,c,l,p,d,f,m,y,j,g,b,v,_,h,k,x,I,A,w,P,T,D,S,q,C,L,E,H,O,M,U,B){"use strict";function F(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function G(e){return function(e){if(Array.isArray(e))return F(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return F(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return F(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}!function(){try{var e=void 0!==l?l:"undefined"!=typeof global?global:void 0!==f?f:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="1658990c-151f-41bd-81ab-5b6e0d10f8d4",e._sentryDebugIdIdentifier="sentry-dbid-1658990c-151f-41bd-81ab-5b6e0d10f8d4")}catch(e){}}();var V=e("../../../common/vendor.js"),$=e("../../../utils/track.js"),z=e("../../../utils/jumpToProtocolPage.js");e("../../../stores/modules/user/index.js"),e("../../../utils/appFetch.js"),e("../../../utils/request.js"),e("../../../stores/modules/history/index.js"),e("../../../stores/modules/login/index.js"),e("../../../utils/constant.js"),e("../../../stores/modules/search-history/index.js"),e("../../../stores/modules/koc-ascription/index.js"),e("../../../stores/modules/purchaseInfo/index.js"),e("../../../pages/manuscript/store.js"),e("../../../stores/modules/clientInfo/index.js"),e("../../../utils/utils.js"),e("../../../writeConstant.js"),e("../../../stores/modules/systenInfo/index.js"),e("../../../stores/modules/globalVariable/index.js"),e("../../../stores/modules/trackInfo/index.js"),e("../../../stores/modules/devDebug/index.js"),e("../../../utils/appInit.js"),e("../../../utils/onGetUdid.js"),e("../../../utils/launchOptionsHelper.js"),e("../../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../../utils/encryptHandler.js"),e("../../../utils/requstErrorMiddlewares.js"),e("../../../utils/pageHelper.js");var J={__name:"pay-agreement",props:{skuList:{type:Array,required:!0,default:function(){return[]}},appletPackage:{type:Array,required:!0,default:function(){return[]}}},setup:function(e){var t=e,r=V.computed((function(){return G(t.appletPackage).concat(G(t.skuList)).some((function(e){return"renewal"===e.type}))})),n=V.computed((function(){return G(t.appletPackage).concat(G(t.skuList)).some((function(e){return"svip"===e.vipType}))})),s=V.computed((function(){return G(t.appletPackage).concat(G(t.skuList)).some((function(e){return["applet_dy_svip","applet_ks_svip","applet_wx_story_svip"].includes(e.vipType)}))})),o=function(e,t){$.track({elementType:"Block",eventType:"Click",moduleId:"vipmini_recharge_panel_agreement",message:"充值面板协议-模块单击",extra:{agreement_text:e}}),z.jumpToProtocolPage(t)};return function(e,t){return V.e({a:V.unref(r)},V.unref(r)?{b:V.o((function(){return o("连续服务订阅协议",6)}))}:{},{c:V.unref(n)},V.unref(n)?{d:V.o((function(){return o("会员服务协议",1)}))}:{},{e:V.unref(s)},V.unref(s)?{f:V.o((function(){return o("体验会员协议",8)}))}:{},{g:V.o((function(){return o("支付协议",2)}))})}}},K=V._export_sfc(J,[["__scopeId","data-v-95f431f3"]]);tt.createComponent(K)}));
//# sourceMappingURL=pay-agreement.js.map