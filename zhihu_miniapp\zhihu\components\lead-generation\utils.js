define("components/lead-generation/utils.js",(function(e,t,n,r,o,f,i,a,u,b,c,d,l,y,s,p,g,v,h,m,w,C,I,O,T,j,x,D,_,E,P,S,k,A,F,L,U,q,z,B){"use strict";var G=Object.defineProperty,H=Object.getOwnPropertySymbols,J=Object.prototype.hasOwnProperty,K=Object.prototype.propertyIsEnumerable,M=function(e,t,n){return t in e?G(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n};!function(){try{var e=void 0!==c?c:"undefined"!=typeof global?global:void 0!==y?y:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="92adff25-3ba8-420d-9283-f457b9e8b3fb",e._sentryDebugIdIdentifier="sentry-dbid-92adff25-3ba8-420d-9283-f457b9e8b3fb")}catch(e){}}(),n.DEFAULT={content:{code:"",guideText:"",buttonText:"复制口令词",successTips:"复制成功",showShadow:!0},theme:{background:"#f9f9f9",textColor:"#056de8",buttonColor:"#056de8",buttonTextColor:"#fff"},behavior:{type:"copy",bpConfig:function(){return{}},contentType:0}},n.mergeConfig=function(e,t){var n=function(e,t){for(var n in t||(t={}))J.call(t,n)&&M(e,n,t[n]);var r=!0,o=!1,f=void 0;if(H)try{for(var i,a=H(t)[Symbol.iterator]();!(r=(i=a.next()).done);r=!0){n=i.value;K.call(t,n)&&M(e,n,t[n])}}catch(e){o=!0,f=e}finally{try{r||null==a.return||a.return()}finally{if(o)throw f}}return e}({},t);for(var r in e)if(void 0!==e[r]){if("bpConfig"===r&&"function"!=typeof e[r])continue;n[r]=e[r]}return n}}));
//# sourceMappingURL=utils.js.map