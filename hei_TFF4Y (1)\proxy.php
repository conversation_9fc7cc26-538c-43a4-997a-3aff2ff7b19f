<?php
header('Content-Type: application/json; charset=utf-8');

// Include required files
require_once 'cache.php';
require_once 'logger.php';

// Load configuration
$config = require_once 'config.php';

// Configuration
$baseUrl = $config['api']['base_url'];
$sessionId = $config['api']['session_id'];
$secret = $config['api']['secret'];
$cacheEnabled = $config['cache']['enabled'];
$cacheTTL = $config['cache']['ttl'];
$cacheDir = $config['cache']['directory'];

// Initialize cache and logger
$cache = new Cache($cacheDir, $cacheTTL);
$logger = new Logger('access.log', true);

// Build headers
$headerArray = [];
foreach ($config['headers'] as $key => $value) {
    $headerArray[] = $key . ': ' . $value;
}

// Add authorization header
$token = getAuthToken($sessionId);
if (!empty($token)) {
    $headerArray[] = 'authorization: Bearer ' . $token;
    // 记录获取到的令牌（仅显示前10个字符，保护安全）
    $logger->logRequest("Authorization token obtained: " . substr($token, 0, 10) . "...", 200, 0);
} else {
    // 如果无法获取令牌，记录错误并返回错误信息
    $logger->logRequest($_SERVER['REQUEST_URI'], 401, 0);
    $logger->logRequest("Failed to obtain authorization token", 401, 0);
    echo json_encode(['success' => false, 'message' => 'Authorization failed: Unable to get token']);
    exit;
}

// Get the request path
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);
$query = $_SERVER['QUERY_STRING'] ?? '';

// 记录请求信息到日志（调试用）
$logger->logRequest("Request: {$path}?{$query}", 200, 0);
$logger->logRequest("Query string raw: " . $query, 200, 0);
$logger->logRequest("Full request URI: " . $requestUri, 200, 0);

// Route the request based on the path
switch ($path) {
    case '/search':
        // 直接从 REQUEST_URI 提取查询参数
        if (preg_match('/queryString=([^&]+)/', $requestUri, $queryMatches) && 
            preg_match('/page=([^&]+)/', $requestUri, $pageMatches)) {
            
            $queryString = urldecode($queryMatches[1]);
            $page = $pageMatches[1];
            
            $logger->logRequest("Directly extracted from URI - queryString: {$queryString}, page: {$page}", 200, 0);
        } else {
            // 尝试从 $_GET 获取
            $queryString = isset($_GET['queryString']) ? $_GET['queryString'] : null;
            $page = isset($_GET['page']) ? $_GET['page'] : null;
            
            $logger->logRequest("From _GET - queryString: {$queryString}, page: {$page}", 200, 0);
            
            // 如果还是没有，尝试解析 QUERY_STRING
            if (empty($queryString) || empty($page)) {
                $params = [];
                parse_str($query, $params);
                
                $logger->logRequest("Parsed params: " . json_encode($params), 200, 0);
                
                $queryString = isset($params['queryString']) ? $params['queryString'] : null;
                $page = isset($params['page']) ? $params['page'] : null;
                
                $logger->logRequest("From parse_str - queryString: {$queryString}, page: {$page}", 200, 0);
            }
        }
        
        // 确保必要的参数存在
        if (empty($queryString) || empty($page)) {
            echo json_encode(['success' => false, 'message' => 'Missing required parameters: queryString or page']);
            $logger->logRequest($requestUri, 400);
            break;
        }
        
        // 构建正确的搜索URL
        $endpoint = '/search/query';
        
        // 重新获取最新的授权令牌，确保不使用可能过期的令牌
        $freshToken = getAuthToken($sessionId);
        if (empty($freshToken)) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed: Unable to get token for search']);
            $logger->logRequest("Search - Failed to get fresh token", 401, 0);
            break;
        }
        
        // 添加必要的头部，完全匹配 hei.json 中的配置
        $searchHeaders = [
            'User-Agent: Mozilla/5.0 (Linux; Android 13; 2210132C Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.156 Mobile Safari/537.36 aweme/24.2.0 ToutiaoMicroApp/2.81.1 PluginVersion/24209009',
            'authorization: Bearer ' . $freshToken,
            'app-name: 3',
            'client-platform: 5',
            'app-version: 81218893',
            'os-type: 3',
            'package-time: 1741852720676',
            'Referer: https://tmaservice.developer.toutiao.com/?appid=tta977dfca3648b5f501&version=1.3.39',
            'Accept: application/json, text/plain, */*'
        ];
        
        // 构建完整的查询字符串
        $fullQuery = http_build_query([
            'queryString' => $queryString,
            'page' => $page
        ]);
        
        $url = $baseUrl . $endpoint . '?' . $fullQuery;
        
        // 记录实际请求的URL（调试用）
        $logger->logRequest("Actual URL: {$url}", 200, 0);
        $logger->logRequest("Headers: " . json_encode($searchHeaders), 200, 0);
        $logger->logRequest("Query parameters - queryString: {$queryString}, page: {$page}", 200, 0);
        $logger->logRequest("Search - Using fresh token: " . substr($freshToken, 0, 10) . "...", 200, 0);
        
        // 使用GET方法请求
        proxyRequest($url, $searchHeaders);
        break;
        
    case '/book/detail':
        // 记录原始请求信息（调试用）
        $logger->logRequest("Book Detail - Raw Query: " . $query, 200, 0);
        $logger->logRequest("Book Detail - GET params: " . json_encode($_GET), 200, 0);
        
        // 尝试多种方式获取 bookId 参数
        $bookId = null;
        
        // 1. 直接从 $_GET 获取
        if (isset($_GET['bookId'])) {
            $bookId = $_GET['bookId'];
            $logger->logRequest("Book Detail - Got bookId from _GET: {$bookId}", 200, 0);
        } 
        // 2. 从 URL 直接提取
        else if (preg_match('/bookId=([^&]+)/', $requestUri, $matches)) {
            $bookId = $matches[1];
            $logger->logRequest("Book Detail - Got bookId from URL: {$bookId}", 200, 0);
        }
        // 3. 解析查询字符串
        else {
            $params = [];
            parse_str($query, $params);
            if (isset($params['bookId'])) {
                $bookId = $params['bookId'];
                $logger->logRequest("Book Detail - Got bookId from parse_str: {$bookId}", 200, 0);
            }
        }
        
        if (empty($bookId)) {
            echo json_encode(['success' => false, 'message' => 'Missing required parameter: bookId']);
            $logger->logRequest($requestUri, 400);
            break;
        }
        
        // 构建正确的图书详情URL
        $endpoint = '/book/cdn/book/detail';
        
        // 强制重新获取令牌，不使用缓存
        $logger->logRequest("Book Detail - Forcing new token acquisition", 200, 0);
        
        // 清除令牌缓存
        $cacheKey = 'auth_token_' . md5($sessionId);
        $cache->delete($cacheKey);
        
        // 多次尝试获取令牌，最多尝试3次
        $maxRetries = 3;
        $freshToken = '';
        
        for ($i = 0; $i < $maxRetries; $i++) {
            $freshToken = getAuthToken($sessionId);
            if (!empty($freshToken)) {
                $logger->logRequest("Book Detail - Got token on attempt " . ($i + 1), 200, 0);
                break;
            }
            $logger->logRequest("Book Detail - Failed to get token on attempt " . ($i + 1) . ", retrying...", 500, 0);
            sleep(1); // 等待1秒后重试
        }
        
        if (empty($freshToken)) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed: Unable to get token for book detail after ' . $maxRetries . ' attempts']);
            $logger->logRequest("Book Detail - Failed to get fresh token after " . $maxRetries . " attempts", 401, 0);
            break;
        }
        
        // 添加必要的头部，确保与 hei.json 中的配置完全一致
        $detailHeaders = [
            'User-Agent: Mozilla/5.0 (Linux; Android 13; 2210132C Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.156 Mobile Safari/537.36 aweme/24.2.0 ToutiaoMicroApp/2.81.1 PluginVersion/24209009',
            'authorization: Bearer ' . $freshToken,
            'app-name: 3',
            'client-platform: 5',
            'app-version: 81218893',
            'os-type: 3',
            'package-time: 1741852720676',
            'Referer: https://tmaservice.developer.toutiao.com/?appid=tta977dfca3648b5f501&version=1.3.39',
            'Accept: application/json, text/plain, */*',
            'Content-Type: application/json; charset=utf-8'
        ];
        
        // 构建完整的查询字符串
        $fullQuery = http_build_query(['bookId' => $bookId]);
        
        $url = $baseUrl . $endpoint . '?' . $fullQuery;
        
        // 记录实际请求的URL和头部（调试用）
        $logger->logRequest("Book Detail - Actual URL: {$url}", 200, 0);
        $logger->logRequest("Book Detail - Headers: " . json_encode($detailHeaders), 200, 0);
        $logger->logRequest("Book Detail - Using fresh token: " . substr($freshToken, 0, 10) . "...", 200, 0);
        
        // 直接使用curl请求，不使用proxyRequest函数，以便更好地控制
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $detailHeaders);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允许重定向
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5); // 最多允许5次重定向
        
        // 创建一个临时文件来存储 cURL 详细信息
        $verbose = fopen('php://temp', 'w+');
        curl_setopt($ch, CURLOPT_STDERR, $verbose);
        
        $startTime = microtime(true);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $effectiveUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL); // 获取最终URL
        
        // 记录 cURL 详细信息
        rewind($verbose);
        $verboseLog = stream_get_contents($verbose);
        fclose($verbose);
        $logger->logRequest("Book Detail - cURL verbose: " . $verboseLog, 200, 0);
        $logger->logRequest("Book Detail - Effective URL: " . $effectiveUrl, 200, 0);
        
        if (curl_errno($ch)) {
            $errorMessage = curl_error($ch);
            $errorResponse = json_encode([
                'success' => false, 
                'message' => 'Curl error: ' . $errorMessage,
                'error_code' => curl_errno($ch),
                'url' => $url
            ]);
            echo $errorResponse;
            $logger->logRequest("Book Detail - Curl error: " . $errorMessage, 500, microtime(true) - $startTime);
        } else {
            // 记录响应信息
            $logger->logRequest("Book Detail - Response code: {$httpCode}", 200, 0);
            $logger->logRequest("Book Detail - Full response: " . $response, 200, 0);
            
            // 尝试解析 JSON 响应
            $jsonResponse = json_decode($response, true);
            if ($jsonResponse === null && json_last_error() !== JSON_ERROR_NONE) {
                $logger->logRequest("Book Detail - JSON parse error: " . json_last_error_msg(), 500, 0);
                
                // 如果不是JSON，尝试返回原始响应
                header('HTTP/1.1 ' . $httpCode);
                echo $response;
            } else {
                $logger->logRequest("Book Detail - JSON response parsed successfully", 200, 0);
                
                // 检查响应是否成功
                if (isset($jsonResponse['success']) && $jsonResponse['success']) {
                    $logger->logRequest("Book Detail - API returned success response", 200, 0);
                    
                    // 缓存成功的响应
                    if ($cacheEnabled) {
                        $cache->set($url, $response);
                        $logger->logRequest("Book Detail - Response cached", 200, 0);
                    }
                    
                    // 输出成功响应
                    header('HTTP/1.1 ' . $httpCode);
                    echo $response;
                } else {
                    $errorMessage = isset($jsonResponse['message']) ? $jsonResponse['message'] : 'Unknown error';
                    $logger->logRequest("Book Detail - API returned error: " . $errorMessage, 400, 0);
                    
                    // 如果是认证错误，尝试再次获取令牌并重试
                    if (isset($jsonResponse['code']) && ($jsonResponse['code'] == 90021 || $jsonResponse['code'] == 90004)) {
                        $logger->logRequest("Book Detail - Authentication error, retrying with new token", 401, 0);
                        
                        // 清除令牌缓存
                        $cache->delete($cacheKey);
                        
                        // 重新获取令牌
                        $freshToken = getAuthToken($sessionId);
                        if (!empty($freshToken)) {
                            // 更新认证头部
                            foreach ($detailHeaders as $key => $value) {
                                if (strpos($value, 'authorization: Bearer ') === 0) {
                                    $detailHeaders[$key] = 'authorization: Bearer ' . $freshToken;
                                    break;
                                }
                            }
                            
                            $logger->logRequest("Book Detail - Retrying with new token: " . substr($freshToken, 0, 10) . "...", 200, 0);
                            
                            // 重新发起请求
                            curl_setopt($ch, CURLOPT_HTTPHEADER, $detailHeaders);
                            $response = curl_exec($ch);
                            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                            
                            $logger->logRequest("Book Detail - Retry response code: {$httpCode}", 200, 0);
                            $logger->logRequest("Book Detail - Retry full response: " . $response, 200, 0);
                            
                            // 输出重试后的响应
                            header('HTTP/1.1 ' . $httpCode);
                            echo $response;
                        } else {
                            // 如果仍然无法获取令牌，返回原始错误响应
                            header('HTTP/1.1 ' . $httpCode);
                            echo $response;
                        }
                    } else {
                        // 非认证错误，直接返回原始响应
                        header('HTTP/1.1 ' . $httpCode);
                        echo $response;
                    }
                }
            }
            
            $logger->logRequest($url, $httpCode, microtime(true) - $startTime);
        }
        
        curl_close($ch);
        break;
        
    case '/book/menu':
        // 记录原始请求信息（调试用）
        $logger->logRequest("Book Menu - Raw Query: " . $query, 200, 0);
        $logger->logRequest("Book Menu - GET params: " . json_encode($_GET), 200, 0);
        
        // 尝试多种方式获取 bookId 参数
        $bookId = null;
        
        // 1. 直接从 $_GET 获取
        if (isset($_GET['bookId'])) {
            $bookId = $_GET['bookId'];
            $logger->logRequest("Book Menu - Got bookId from _GET: {$bookId}", 200, 0);
        } 
        // 2. 从 URL 直接提取
        else if (preg_match('/bookId=([^&]+)/', $requestUri, $matches)) {
            $bookId = $matches[1];
            $logger->logRequest("Book Menu - Got bookId from URL: {$bookId}", 200, 0);
        }
        // 3. 解析查询字符串
        else {
            $params = [];
            parse_str($query, $params);
            if (isset($params['bookId'])) {
                $bookId = $params['bookId'];
                $logger->logRequest("Book Menu - Got bookId from parse_str: {$bookId}", 200, 0);
            }
        }
        
        if (empty($bookId)) {
            echo json_encode(['success' => false, 'message' => 'Missing required parameter: bookId']);
            $logger->logRequest($requestUri, 400);
            break;
        }
        
        // 构建正确的图书目录URL
        $endpoint = '/book/cdn/menu';
        
        // 重新获取最新的授权令牌，确保不使用可能过期的令牌
        $freshToken = getAuthToken($sessionId);
        if (empty($freshToken)) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed: Unable to get token for book menu']);
            $logger->logRequest("Book Menu - Failed to get fresh token", 401, 0);
            break;
        }
        
        // 添加必要的头部，与搜索 API 保持一致
        $menuHeaders = [
            'User-Agent: Mozilla/5.0 (Linux; Android 13; 2210132C Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.156 Mobile Safari/537.36 aweme/24.2.0 ToutiaoMicroApp/2.81.1 PluginVersion/24209009',
            'authorization: Bearer ' . $freshToken,
            'app-name: 3',
            'client-platform: 5',
            'app-version: 81218893',
            'os-type: 3',
            'package-time: 1741852720676',
            'Referer: https://tmaservice.developer.toutiao.com/?appid=tta977dfca3648b5f501&version=1.3.39',
            'Accept: application/json, text/plain, */*'
        ];
        
        // 构建完整的查询字符串
        $fullQuery = http_build_query(['bookId' => $bookId]);
        
        $url = $baseUrl . $endpoint . '?' . $fullQuery;
        
        // 记录实际请求的URL（调试用）
        $logger->logRequest("Book Menu - Actual URL: {$url}", 200, 0);
        $logger->logRequest("Book Menu - Headers: " . json_encode($menuHeaders), 200, 0);
        $logger->logRequest("Book Menu - Using fresh token: " . substr($freshToken, 0, 10) . "...", 200, 0);
        
        // 使用GET方法请求
        proxyRequest($url, $menuHeaders);
        break;
        
    case '/book/chapter':
        // 记录原始请求信息（调试用）
        $logger->logRequest("Book Chapter - Raw Query: " . $query, 200, 0);
        $logger->logRequest("Book Chapter - GET params: " . json_encode($_GET), 200, 0);
        
        // 尝试多种方式获取 bookId 和 chapterId 参数
        $bookId = null;
        $chapterId = null;
        
        // 1. 直接从 $_GET 获取
        if (isset($_GET['bookId'])) {
            $bookId = $_GET['bookId'];
            $logger->logRequest("Book Chapter - Got bookId from _GET: {$bookId}", 200, 0);
        }
        
        if (isset($_GET['chapterId'])) {
            $chapterId = $_GET['chapterId'];
            $logger->logRequest("Book Chapter - Got chapterId from _GET: {$chapterId}", 200, 0);
        }
        
        // 2. 从 URL 直接提取
        if (empty($bookId) && preg_match('/bookId=([^&]+)/', $requestUri, $matches)) {
            $bookId = $matches[1];
            $logger->logRequest("Book Chapter - Got bookId from URL: {$bookId}", 200, 0);
        }
        
        if (empty($chapterId) && preg_match('/chapterId=([^&]+)/', $requestUri, $matches)) {
            $chapterId = $matches[1];
            $logger->logRequest("Book Chapter - Got chapterId from URL: {$chapterId}", 200, 0);
        }
        
        // 3. 解析查询字符串
        if (empty($bookId) || empty($chapterId)) {
            $params = [];
            parse_str($query, $params);
            
            if (empty($bookId) && isset($params['bookId'])) {
                $bookId = $params['bookId'];
                $logger->logRequest("Book Chapter - Got bookId from parse_str: {$bookId}", 200, 0);
            }
            
            if (empty($chapterId) && isset($params['chapterId'])) {
                $chapterId = $params['chapterId'];
                $logger->logRequest("Book Chapter - Got chapterId from parse_str: {$chapterId}", 200, 0);
            }
        }
        
        if (empty($bookId) || empty($chapterId)) {
            echo json_encode(['success' => false, 'message' => 'Missing required parameters: bookId or chapterId']);
            $logger->logRequest($requestUri, 400);
            break;
        }
        
        // 强制重新获取令牌，不使用缓存
        $logger->logRequest("Book Chapter - Forcing new token acquisition", 200, 0);
        
        // 清除令牌缓存
        $cacheKey = 'auth_token_' . md5($sessionId);
        $cache->delete($cacheKey);
        
        // 多次尝试获取令牌，最多尝试3次
        $maxRetries = 3;
        $freshToken = '';
        
        for ($i = 0; $i < $maxRetries; $i++) {
            $freshToken = getAuthToken($sessionId);
            if (!empty($freshToken)) {
                $logger->logRequest("Book Chapter - Got token on attempt " . ($i + 1), 200, 0);
                break;
            }
            $logger->logRequest("Book Chapter - Failed to get token on attempt " . ($i + 1) . ", retrying...", 500, 0);
            sleep(1); // 等待1秒后重试
        }
        
        if (empty($freshToken)) {
            echo json_encode(['success' => false, 'message' => 'Authorization failed: Unable to get token for chapter content after ' . $maxRetries . ' attempts']);
            $logger->logRequest("Book Chapter - Failed to get fresh token after " . $maxRetries . " attempts", 401, 0);
            break;
        }
        
        // 构建正确的章节内容URL，包括签名
        $timestamp = time() * 1000; // Current timestamp in milliseconds
        
        $signParams = "bookId={$bookId}&chapterId={$chapterId}&timestamp={$timestamp}&unlockChapter=false";
        $sign = md5($signParams . "&{$timestamp}={$secret}");
        
        $endpoint = "/book/cdn/chapter";
        
        // 添加必要的头部，确保与 hei.json 中的配置完全一致
        $chapterHeaders = [
            'User-Agent: Mozilla/5.0 (Linux; Android 13; 2210132C Build/TKQ1.221114.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.156 Mobile Safari/537.36 aweme/24.2.0 ToutiaoMicroApp/2.81.1 PluginVersion/24209009',
            'authorization: Bearer ' . $freshToken,
            'app-name: 3',
            'client-platform: 5',
            'app-version: 81218893',
            'os-type: 3',
            'package-time: 1741852720676',
            'Referer: https://tmaservice.developer.toutiao.com/?appid=tta977dfca3648b5f501&version=1.3.39',
            'Accept: application/json, text/plain, */*',
            'Content-Type: application/json; charset=utf-8'
        ];
        
        // 构建完整的查询字符串，包括签名
        $fullQuery = "{$signParams}&sign={$sign}";
        
        $url = $baseUrl . $endpoint . '?' . $fullQuery;
        
        // 记录实际请求的URL和头部（调试用）
        $logger->logRequest("Book Chapter - Actual URL: {$url}", 200, 0);
        $logger->logRequest("Book Chapter - Headers: " . json_encode($chapterHeaders), 200, 0);
        $logger->logRequest("Book Chapter - Using fresh token: " . substr($freshToken, 0, 10) . "...", 200, 0);
        
        // 直接使用curl请求，不使用proxyRequest函数，以便更好地控制
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $chapterHeaders);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允许重定向
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5); // 最多允许5次重定向
        
        // 创建一个临时文件来存储 cURL 详细信息
        $verbose = fopen('php://temp', 'w+');
        curl_setopt($ch, CURLOPT_STDERR, $verbose);
        
        $startTime = microtime(true);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $effectiveUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL); // 获取最终URL
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE); // 获取内容类型
        
        // 记录 cURL 详细信息
        rewind($verbose);
        $verboseLog = stream_get_contents($verbose);
        fclose($verbose);
        $logger->logRequest("Book Chapter - cURL verbose: " . $verboseLog, 200, 0);
        $logger->logRequest("Book Chapter - Effective URL: " . $effectiveUrl, 200, 0);
        $logger->logRequest("Book Chapter - Content Type: " . $contentType, 200, 0);
        $logger->logRequest("Book Chapter - Response length: " . strlen($response) . " bytes", 200, 0);
        
        if (curl_errno($ch)) {
            $errorMessage = curl_error($ch);
            $errorResponse = json_encode([
                'success' => false, 
                'message' => 'Curl error: ' . $errorMessage,
                'error_code' => curl_errno($ch),
                'url' => $url
            ]);
            echo $errorResponse;
            $logger->logRequest("Book Chapter - Curl error: " . $errorMessage, 500, microtime(true) - $startTime);
        } else {
            // 记录响应信息
            $logger->logRequest("Book Chapter - Response code: {$httpCode}", 200, 0);
            
            // 检查响应是否为空或太短
            if (empty($response) || strlen($response) < 10) {
                $logger->logRequest("Book Chapter - Response is empty or too short: '" . bin2hex($response) . "'", 400, 0);
                echo json_encode([
                    'success' => false,
                    'message' => 'Empty or invalid response from server',
                    'raw_response' => bin2hex($response),
                    'response_length' => strlen($response)
                ]);
                break;
            }
            
            // 检查内容类型，确保是JSON
            if (strpos($contentType, 'application/json') === false) {
                $logger->logRequest("Book Chapter - Unexpected content type: " . $contentType, 400, 0);
                $logger->logRequest("Book Chapter - Raw response: " . substr(bin2hex($response), 0, 200), 400, 0);
            }
            
            // 尝试解析 JSON 响应
            $jsonResponse = json_decode($response, true);
            if ($jsonResponse === null && json_last_error() !== JSON_ERROR_NONE) {
                $logger->logRequest("Book Chapter - JSON parse error: " . json_last_error_msg(), 500, 0);
                $logger->logRequest("Book Chapter - Raw response: " . substr(bin2hex($response), 0, 200), 500, 0);
                
                // 返回错误信息和原始响应的十六进制表示
                echo json_encode([
                    'success' => false,
                    'message' => 'JSON parse error: ' . json_last_error_msg(),
                    'raw_response' => bin2hex($response),
                    'response_length' => strlen($response)
                ]);
            } else {
                $logger->logRequest("Book Chapter - JSON response parsed successfully", 200, 0);
                
                // 检查响应是否成功
                if (isset($jsonResponse['success']) && $jsonResponse['success']) {
                    $logger->logRequest("Book Chapter - API returned success response", 200, 0);
                    
                    // 缓存成功的响应
                    if ($cacheEnabled) {
                        $cache->set($url, $response);
                        $logger->logRequest("Book Chapter - Response cached", 200, 0);
                    }
                    
                    // 输出成功响应
                    header('HTTP/1.1 ' . $httpCode);
                    header('Content-Type: application/json; charset=utf-8');
                    echo $response;
                } else {
                    $errorMessage = isset($jsonResponse['message']) ? $jsonResponse['message'] : 'Unknown error';
                    $logger->logRequest("Book Chapter - API returned error: " . $errorMessage, 400, 0);
                    
                    // 如果是认证错误，尝试再次获取令牌并重试
                    if (isset($jsonResponse['code']) && ($jsonResponse['code'] == 90021 || $jsonResponse['code'] == 90004)) {
                        $logger->logRequest("Book Chapter - Authentication error, retrying with new token", 401, 0);
                        
                        // 清除令牌缓存
                        $cache->delete($cacheKey);
                        
                        // 重新获取令牌
                        $freshToken = getAuthToken($sessionId);
                        if (!empty($freshToken)) {
                            // 更新认证头部
                            foreach ($chapterHeaders as $key => $value) {
                                if (strpos($value, 'authorization: Bearer ') === 0) {
                                    $chapterHeaders[$key] = 'authorization: Bearer ' . $freshToken;
                                    break;
                                }
                            }
                            
                            $logger->logRequest("Book Chapter - Retrying with new token: " . substr($freshToken, 0, 10) . "...", 200, 0);
                            
                            // 重新发起请求
                            curl_setopt($ch, CURLOPT_HTTPHEADER, $chapterHeaders);
                            $response = curl_exec($ch);
                            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                            
                            $logger->logRequest("Book Chapter - Retry response code: {$httpCode}", 200, 0);
                            $logger->logRequest("Book Chapter - Retry response length: " . strlen($response) . " bytes", 200, 0);
                            
                            // 输出重试后的响应
                            header('HTTP/1.1 ' . $httpCode);
                            header('Content-Type: application/json; charset=utf-8');
                            echo $response;
                        } else {
                            // 如果仍然无法获取令牌，返回原始错误响应
                            header('HTTP/1.1 ' . $httpCode);
                            header('Content-Type: application/json; charset=utf-8');
                            echo $response;
                        }
                    } else {
                        // 非认证错误，直接返回原始响应
                        header('HTTP/1.1 ' . $httpCode);
                        header('Content-Type: application/json; charset=utf-8');
                        echo $response;
                    }
                }
            }
            
            $logger->logRequest($url, $httpCode, microtime(true) - $startTime);
        }
        
        curl_close($ch);
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid endpoint']);
        $logger->logRequest($requestUri, 404);
        break;
}

/**
 * Make a proxy request to the target URL
 */
function proxyRequest($url, $headers) {
    global $cache, $cacheEnabled, $logger;
    
    $startTime = microtime(true);
    
    // 记录请求信息
    $logger->logRequest("ProxyRequest - URL: {$url}", 200, 0);
    $logger->logRequest("ProxyRequest - Headers: " . json_encode($headers), 200, 0);
    
    // Check if we have a cached response
    if ($cacheEnabled && $cache->exists($url)) {
        $cachedResponse = $cache->get($url);
        echo $cachedResponse;
        $logger->logRequest("ProxyRequest - Using cached response", 200, microtime(true) - $startTime);
        $logger->logRequest("Response (cached): " . substr($cachedResponse, 0, 200) . "...", 200, 0);
        return;
    }
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 设置超时时间为30秒
    curl_setopt($ch, CURLOPT_VERBOSE, true); // 启用详细信息
    
    // 创建一个临时文件来存储 cURL 详细信息
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $info = curl_getinfo($ch);
    
    // 记录 cURL 详细信息
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    fclose($verbose);
    $logger->logRequest("cURL verbose: " . $verboseLog, 200, 0);
    
    // 记录请求信息
    $logger->logRequest("Request info: " . json_encode($info), 200, 0);
    
    // 记录完整的响应内容（调试用）
    $logger->logRequest("Full response: " . $response, 200, 0);
    
    if (curl_errno($ch)) {
        $errorMessage = curl_error($ch);
        $errorResponse = json_encode([
            'success' => false, 
            'message' => 'Curl error: ' . $errorMessage,
            'error_code' => curl_errno($ch),
            'url' => $url
        ]);
        echo $errorResponse;
        $logger->logRequest("Curl error: " . $errorMessage, 500, microtime(true) - $startTime);
        $logger->logRequest("Curl error code: " . curl_errno($ch), 500, 0);
    } else {
        // Forward the response headers
        header('HTTP/1.1 ' . $httpCode);
        
        // 记录响应信息（调试用）
        $logger->logRequest("Response code: {$httpCode}", 200, 0);
        $logger->logRequest("Response: " . substr($response, 0, 200) . "...", 200, 0);
        
        // 尝试解析 JSON 响应
        $jsonResponse = json_decode($response, true);
        if ($jsonResponse === null && json_last_error() !== JSON_ERROR_NONE) {
            $logger->logRequest("JSON parse error: " . json_last_error_msg(), 500, 0);
        } else {
            $logger->logRequest("JSON response parsed successfully", 200, 0);
        }
        
        // Cache the response if it's successful
        if ($cacheEnabled && $httpCode == 200) {
            $cache->set($url, $response);
            $logger->logRequest("Response cached", 200, 0);
        }
        
        // Output the response body
        echo $response;
        
        // Log the request
        $logger->logRequest($url, $httpCode, microtime(true) - $startTime);
    }
    
    curl_close($ch);
}

/**
 * Get authorization token from session ID
 */
function getAuthToken($sessionId) {
    global $cache, $config, $logger;
    
    // Try to get token from cache first
    $cacheKey = 'auth_token_' . md5($sessionId);
    
    if ($cache->exists($cacheKey)) {
        $token = $cache->get($cacheKey);
        $logger->logRequest("Using cached token: " . substr($token, 0, 10) . "...", 200, 0);
        return $token;
    }
    
    $logger->logRequest("Getting new token for sessionId: " . substr($sessionId, 0, 10) . "...", 200, 0);
    
    // Build login headers
    $loginHeaders = [];
    foreach ($config['login']['headers'] as $key => $value) {
        $loginHeaders[] = $key . ': ' . $value;
    }
    $loginHeaders[] = 'cookie: sessionid=' . $sessionId;
    
    // 记录完整的登录请求头（调试用）
    $logger->logRequest("Login headers: " . json_encode($loginHeaders), 200, 0);
    
    // Step 1: Get the code from the login API
    $loginParams = http_build_query($config['login']['params']);
    $loginUrl = $config['login']['url'] . '?' . $loginParams;
    
    // 记录登录 URL（调试用）
    $logger->logRequest("Login URL: " . $loginUrl, 200, 0);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $loginUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $loginHeaders);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 设置超时时间为30秒
    curl_setopt($ch, CURLOPT_VERBOSE, true); // 启用详细信息
    
    // 创建一个临时文件来存储 cURL 详细信息
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);
    
    $response = curl_exec($ch);
    
    // 记录 cURL 详细信息
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    fclose($verbose);
    $logger->logRequest("Login cURL verbose: " . $verboseLog, 200, 0);
    
    if (curl_errno($ch)) {
        $logger->logRequest("Login API curl error: " . curl_error($ch), 500, 0);
        curl_close($ch);
        return '';
    }
    
    curl_close($ch);
    
    // 记录登录 API 响应（调试用）
    $logger->logRequest("Login API response: " . $response, 200, 0);
    
    $responseData = json_decode($response, true);
    
    if (!isset($responseData['data']['code'])) {
        $logger->logRequest("Login API response error: " . json_encode($responseData), 500, 0);
        return '';
    }
    
    $code = $responseData['data']['code'];
    $logger->logRequest("Got code: " . $code, 200, 0);
    
    // Step 2: Get the auth token using the code
    $authUrl = $config['login']['auth_url'];
    $postData = 'code=' . $code . '&isLogin=true';
    
    // Build auth headers
    $authHeaders = [];
    foreach ($config['login']['auth_headers'] as $key => $value) {
        $authHeaders[] = $key . ': ' . $value;
    }
    
    // 记录认证请求头（调试用）
    $logger->logRequest("Auth headers: " . json_encode($authHeaders), 200, 0);
    $logger->logRequest("Auth URL: " . $authUrl, 200, 0);
    $logger->logRequest("Auth post data: " . $postData, 200, 0);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $authUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $authHeaders);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 设置超时时间为30秒
    curl_setopt($ch, CURLOPT_VERBOSE, true); // 启用详细信息
    
    // 创建一个临时文件来存储 cURL 详细信息
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);
    
    $response = curl_exec($ch);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE); // 在关闭curl前获取header大小
    
    // 记录 cURL 详细信息
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    fclose($verbose);
    $logger->logRequest("Auth cURL verbose: " . $verboseLog, 200, 0);
    
    if (curl_errno($ch)) {
        $logger->logRequest("Auth API curl error: " . curl_error($ch), 500, 0);
        curl_close($ch);
        return '';
    }
    
    // 在关闭curl前分离header和body
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    curl_close($ch);
    
    // 记录认证 API 响应（调试用）
    $logger->logRequest("Auth API response headers: " . $headers, 200, 0);
    $logger->logRequest("Auth API response body: " . $body, 200, 0);
    
    // 尝试从响应头中提取 Access_token
    preg_match('/Access_token: ([^\r\n]+)/i', $headers, $matches);
    
    if (isset($matches[1])) {
        $token = $matches[1];
        $logger->logRequest("Got token from header: " . substr($token, 0, 10) . "...", 200, 0);
        
        // Cache the token
        $cache->set($cacheKey, $token, [], $config['login']['token_cache_ttl']);
        
        return $token;
    } else {
        $logger->logRequest("Failed to extract token from headers, trying body...", 500, 0);
        
        // 尝试从响应体中提取 token
        $bodyData = json_decode($body, true);
        
        if (isset($bodyData['data']['token'])) {
            $token = $bodyData['data']['token'];
            $logger->logRequest("Got token from response body: " . substr($token, 0, 10) . "...", 200, 0);
            
            // Cache the token
            $cache->set($cacheKey, $token, [], $config['login']['token_cache_ttl']);
            
            return $token;
        } else {
            // 尝试直接解析整个响应
            $fullResponseData = json_decode($response, true);
            if (isset($fullResponseData['data']['token'])) {
                $token = $fullResponseData['data']['token'];
                $logger->logRequest("Got token from full response: " . substr($token, 0, 10) . "...", 200, 0);
                
                // Cache the token
                $cache->set($cacheKey, $token, [], $config['login']['token_cache_ttl']);
                
                return $token;
            }
        }
    }
    
    $logger->logRequest("Failed to extract token from any source", 500, 0);
    return '';
}
?> 