define("utils/track.js",(function(e,t,o,r,n,i,s,a,d,l,u,c,y,f,m,b,p,v,h,g,x,I,_,j,S,k,w,O,z,E,V,D,T,K,C,P,U,q,H,Q){"use strict";var A=Object.defineProperty,G=Object.getOwnPropertySymbols,J=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable,N=function(e,t,o){return t in e?A(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o},B=function(e,t){for(var o in t||(t={}))J.call(t,o)&&N(e,o,t[o]);var r=!0,n=!1,i=void 0;if(G)try{for(var s,a=G(t)[Symbol.iterator]();!(r=(s=a.next()).done);r=!0){o=s.value;L.call(t,o)&&N(e,o,t[o])}}catch(e){n=!0,i=e}finally{try{r||null==a.return||a.return()}finally{if(n)throw i}}return e};!function(){try{var e=void 0!==u?u:"undefined"!=typeof global?global:void 0!==f?f:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="58f18df2-f8b7-4135-a6e4-a1874afbabee",e._sentryDebugIdIdentifier="sentry-dbid-58f18df2-f8b7-4135-a6e4-a1874afbabee")}catch(e){}}();var F=e("../common/vendor.js");e("../stores/modules/user/index.js"),e("../stores/modules/history/index.js");var M=e("../stores/modules/login/index.js");e("../stores/modules/search-history/index.js");var R=e("../stores/modules/koc-ascription/index.js");e("../stores/modules/purchaseInfo/index.js");var W=e("../stores/modules/clientInfo/index.js");e("../stores/modules/systenInfo/index.js");var X=e("../stores/modules/globalVariable/index.js"),Y=e("../stores/modules/trackInfo/index.js");e("../stores/modules/devDebug/index.js"),o.track=function(e){try{var t=Y.useTrackStore(),o=R.useKocAscriptionStore(),r=W.useClientStore(),n=r.udid,i=r.bizClientId,s=X.useGlobalVariable().abValue,a=M.useLoginStore(),d=o.search_query,l=o.koc_query,u=t.isShare,c=t.shareId,y=t.pageId,f=null!=e?e:{},m=f.pageId,b=void 0===m?y:m,p=f.elementType,v=void 0===p?"":p,h=f.eventType,g=void 0===h?"":h,x=f.moduleId,I=void 0===x?"":x,_=f.elementText,j=void 0===_?"":_,S=f.moduleIndex,k=void 0===S?-1:S,w=f.message,O=void 0===w?"":w,z=(f.wxViewUrl,f.extra),E=void 0===z?{}:z;s&&Object.keys(s).length&&(E.ab=s),Object.keys(E).forEach((function(e){var t;"object"==((t=E[e])&&"undefined"!=typeof Symbol&&t.constructor===Symbol?"symbol":typeof t)?E[e]=String(JSON.stringify(F.humpsExports.decamelizeKeys(E[e]))):"string"!=typeof E[e]&&(E[e]=String(E[e]))})),F.zaV3.config(B({baseExtraInfo:F.humpsExports.decamelizeKeys({isShare:String(!!u),shareId:c,bizClientId:i,elementText:j,udid:n})},a.hashId&&{memberHashId:a.hashId})),E.memberHashId=a.hashId;var V=F.humpsExports.decamelizeKeys(B(B(B({},d&&{searchQuery:d}),l&&{kocQuery:l}),E)),D=V.section_id,T=void 0===D?"":D,K=V.type,C=void 0===K?"":K,P=function(e,t){var o={};for(var r in e)J.call(e,r)&&t.indexOf(r)<0&&(o[r]=e[r]);var n=!0,i=!1,s=void 0;if(null!=e&&G)try{for(var a,d=G(e)[Symbol.iterator]();!(n=(a=d.next()).done);n=!0){r=a.value;t.indexOf(r)<0&&L.call(e,r)&&(o[r]=e[r])}}catch(e){i=!0,s=e}finally{try{n||null==d.return||d.return()}finally{if(i)throw s}}return o}(V,["section_id","type"]),U="Unknown"===g?"":g;"Show"===g?F.zaV3.trackShow({element_location:{type:v,text:O,module_id:I,module_index:k,page:{page_id:b},content:{id:T,type:C}}},{config_map:B(B({},P),T&&{section_id:T})}):F.zaV3.trackEvent({action:"Unknown",event_type:U,element_location:{module_id:I,type:v,text:O,module_index:k,page:{page_id:b},content:{id:T,type:C}}},{config_map:B(B({},P),T&&{section_id:T})})}catch(t){}}}));
//# sourceMappingURL=track.js.map