define("components/pay-card/pay-card-tile.js",(function(e,t,n,s,r,o,i,u,a,l,p,c,d,f,y,v,m,g,j,_,h,b,k,x,I,T,w,C,M,P,D,N,S,q,L,B,H,$,E,F){"use strict";!function(){try{var e=void 0!==p?p:"undefined"!=typeof global?global:void 0!==f?f:{},t=(new Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="89c09ee5-fd24-49da-958e-4475bb05d3a2",e._sentryDebugIdIdentifier="sentry-dbid-89c09ee5-fd24-49da-958e-4475bb05d3a2")}catch(e){}}();var U=e("../../common/vendor.js");e("../../stores/modules/user/index.js"),e("../../stores/modules/history/index.js"),e("../../stores/modules/login/index.js"),e("../../stores/modules/search-history/index.js"),e("../../stores/modules/koc-ascription/index.js");var A=e("../../stores/modules/purchaseInfo/index.js");e("../../stores/modules/clientInfo/index.js"),e("../../stores/modules/systenInfo/index.js"),e("../../stores/modules/globalVariable/index.js"),e("../../stores/modules/trackInfo/index.js"),e("../../stores/modules/devDebug/index.js");var G=e("../../utils/track.js");e("../../utils/appFetch.js"),e("../../utils/request.js"),e("../../writeConstant.js"),e("../../utils/appInit.js"),e("../../utils/onGetUdid.js"),e("../../utils/utils.js"),e("../../utils/launchOptionsHelper.js"),e("../../prelaunch/tt/pre-fetch-launch-options.js"),e("../../utils/encryptHandler.js"),e("../../utils/requstErrorMiddlewares.js"),e("../../utils/pageHelper.js"),e("../../utils/constant.js"),e("../../pages/manuscript/store.js"),Math||(O+V+R+z+J)();var O=function(){return"./components/sku-card-list.js"},R=function(){return"./components/pay-agreement.js"},V=function(){return"../pay-list-popup/pay-list-popup.js"},z=function(){return"../recharge-tips/recharge-tips.js"},J=function(){return"../confirm-popup/confirm-popup.js"},K={__name:"pay-card-tile",props:{type:{type:String,required:!0,default:""},isNovelPlugin:{type:Boolean,required:!1,default:!1}},setup:function(e){var t=e,n=U.ref(!1),s=U.ref(!1),r=A.usePurchaseInfo(),o=U.storeToRefs(r),i=o.skuList,u=o.prompt,a=o.appletPackage,l="manuscript"===t.type,p=U.ref(l?"manuscript":"buy"),c=function(e){var t=e.type;U.index.$emit("start_pay_fn_".concat(p.value),t)},d=function(){G.track({elementType:"Block",eventType:"Click",moduleId:"vipmini_recharge_panel_payment_information",message:"充值面板付费须知-模块单击"}),t.isNovelPlugin?U.index.showModal({title:"付费须知",content:u.value,showCancel:!1}):s.value=!0},f=function(){G.track({elementType:"Block",eventType:"Click",moduleId:"vipmini_recharge_panel_no_receive",message:"充值面板充值未到账-模块单击"}),t.isNovelPlugin?U.NovelPlugin.getCurrentNovelManager().setFullScreenComponentStatus({show:!0}):n.value=!0};return U.onMounted((function(){U.index.$on("handle_pay_list_start_pay_".concat(p.value),c)})),U.onUnmounted((function(){U.index.$off("handle_pay_list_start_pay_".concat(p.value),c)})),function(e,t){return U.e({a:U.unref(a).length},U.unref(a).length?{b:U.p({styleType:"tile",skuList:U.unref(a),vipType:"applet_svip",isShowInManuscript:l})}:{},{c:U.unref(i).length},U.unref(i).length?{d:U.p({styleType:"tile",skuList:U.unref(i),vipType:"svip",isShowInManuscript:l})}:{},{e:U.p({pageType:p.value}),f:U.p({skuList:U.unref(i),appletPackage:U.unref(a)}),g:U.o(d),h:U.o(f),i:U.o((function(e){return n.value=!1})),j:U.p({visible:n.value}),k:U.o((function(e){return s.value=e})),l:U.p({title:"付费须知",content:U.unref(u),contentAlign:"left",visible:s.value})})}}},Q=U._export_sfc(K,[["__scopeId","data-v-e03cc86d"]]);tt.createComponent(Q)}));
//# sourceMappingURL=pay-card-tile.js.map