# 章文品读小说下载插件

这是一个用于NoneBot2框架的章文品读小说下载插件，可以通过链接下载章文品读平台的小说。

## 功能特点

- 支持多种链接格式，包括标准链接和抖音分享链接
- 自动提取小说ID并下载全部章节
- 支持上传到群文件或WebDAV
- 完全模仿6666.py的下载逻辑，确保稳定性
- 支持多API备用，自动切换

## 支持的链接格式

- 标准链接: 包含`bookId=数字`的链接
- 抖音分享链接: `https://www.iesdouyin.com/share/microapp/?token=xxx`

## 配置说明

插件支持以下环境变量配置:

```
# WebDAV配置
WEBDAV_URL=https://your-webdav-server.com/dav  # WebDAV服务器地址
WEBDAV_USERNAME=username                       # WebDAV用户名
WEBDAV_PASSWORD=password                       # WebDAV密码
WEBDAV_FOLDER=小说                              # WebDAV上传目录
```

## 安装依赖

```bash
pip install httpx requests
```

## 使用方法

1. 在群聊中发送包含章文品读小说链接的消息
2. 插件会自动识别链接并开始下载
3. 下载完成后，会自动上传到群文件或WebDAV

## 注意事项

- 由于API限制，下载速度可能较慢
- 部分章节可能下载失败，插件会自动重试
- 如遇到"Authorization failed"错误，请稍后再试 