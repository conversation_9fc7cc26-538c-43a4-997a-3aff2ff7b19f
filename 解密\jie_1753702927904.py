# decrypt_aes.py
import base64
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import hashlib
import binascii

class Decryptor:
    def __init__(self, key_hex, iv_hex):
        self.key = bytes.fromhex(key_hex)
        self.iv = bytes.fromhex(iv_hex)

    def decrypt_base64(self, data):
        """自动识别并解码 Base64"""
        data = data.strip()
        try:
            return base64.b64decode(data)
        except:
            try:
                return base64.urlsafe_b64decode(data)
            except Exception as e:
                raise ValueError(f"Base64解码失败: {e}")

    def decrypt_aes_cbc(self, ciphertext):
        """AES-CBC 解密"""
        try:
            cipher = AES.new(self.key, AES.MODE_CBC, self.iv)
            plaintext = cipher.decrypt(ciphertext)
            return unpad(plaintext, AES.block_size)
        except Exception as e:
            raise ValueError(f"AES解密失败: {e}")

    def decrypt_data(self, encrypted_b64):
        decoded = self.decrypt_base64(encrypted_b64)
        if len(decoded) % 16 != 0:
            print(f"⚠️  警告: 数据长度 {len(decoded)} 不是 16 的倍数")
        decrypted = self.decrypt_aes_cbc(decoded)
        try:
            return decrypted.decode('utf-8')
        except UnicodeDecodeError:
            print("⚠️  解密成功，但非UTF-8文本，返回十六进制:")
            return decrypted.hex()


def main():
    AES_KEY_HEX = "3e6cebec56e4bd95e8ae6ed4bf03c52e"
    AES_IV_HEX = "54dee60aac15d992a4293886d5119b0c"

    decryptor = Decryptor(AES_KEY_HEX, AES_IV_HEX)

    try:
        with open("C:\\botfanqie\\解密\\encrypt_section.json", "r", encoding='utf-8') as f:
            data = json.load(f)

        results = {}
        for field in ['payload', 'data']:
            if field in data:
                print(f"\n🔄 正在解密 `{field}`...")
                try:
                    result = decryptor.decrypt_data(data[field])
                    results[field] = result
                    print("✅ 成功!")
                except Exception as e:
                    print(f"❌ 失败: {e}")

        if not results:
            print("❌ 错误: JSON 中未找到 'payload' 或 'data' 字段")
            return

        # 输出并保存
        with open("C:\\botfanqie\\解密\\decrypted_result.txt", "w", encoding='utf-8') as f:
            for k, v in results.items():
                f.write(f"{k}:\n{v}\n{'-'*50}\n")
                print(f"\n{k}:\n{v}")

        print("\n✅ 解密完成，结果已保存到 decrypted_result.txt")

    except FileNotFoundError:
        print("❌ 错误: 找不到 encrypt_section.json")
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")


if __name__ == "__main__":
    main()